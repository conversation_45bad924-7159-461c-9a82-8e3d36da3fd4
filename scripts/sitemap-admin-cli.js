#!/usr/bin/env node

/**
 * Sitemap Administration CLI
 * Command-line utility for managing sitemaps
 * 
 * Usage:
 *   node scripts/sitemap-admin-cli.js <command> [options]
 * 
 * Commands:
 *   status          - Show system health status
 *   stats           - Show system statistics
 *   warm-cache      - Warm up the sitemap cache
 *   regenerate      - Regenerate all sitemaps
 *   clear-cache     - Clear all cached sitemaps
 *   maintenance     - Perform system maintenance
 *   debug           - Generate debug report
 *   validate        - Validate sitemaps
 *   help            - Show this help message
 */

const https = require('https');
const http = require('http');

// Configuration
const config = {
  baseUrl: process.env.SITEMAP_BASE_URL || 'http://localhost:3000',
  adminKey: process.env.SITEMAP_ADMIN_KEY,
  timeout: 30000, // 30 seconds
};

// Command line argument parsing
const args = process.argv.slice(2);
const command = args[0];
const options = parseOptions(args.slice(1));

// Main execution
async function main() {
  if (!command || command === 'help') {
    showHelp();
    return;
  }

  try {
    await executeCommand(command, options);
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

// Command execution
async function executeCommand(command, options) {
  switch (command) {
    case 'status':
      await showStatus();
      break;
    
    case 'stats':
      await showStatistics();
      break;
    
    case 'warm-cache':
      await warmCache(options);
      break;
    
    case 'regenerate':
      await regenerateSitemaps(options);
      break;
    
    case 'clear-cache':
      await clearCache();
      break;
    
    case 'maintenance':
      await performMaintenance(options);
      break;
    
    case 'debug':
      await generateDebugReport(options);
      break;
    
    case 'validate':
      await validateSitemaps(options);
      break;
    
    default:
      console.error(`❌ Unknown command: ${command}`);
      showHelp();
      process.exit(1);
  }
}

// Command implementations
async function showStatus() {
  console.log('🔍 Checking system health...\n');
  
  const response = await makeRequest('GET', '/api/sitemap/admin?action=status');
  const data = JSON.parse(response);
  
  console.log(`Overall Status: ${getStatusIcon(data.overall)} ${data.overall.toUpperCase()}`);
  console.log(`Timestamp: ${new Date(data.timestamp).toLocaleString()}\n`);
  
  console.log('Component Health:');
  Object.entries(data.components).forEach(([component, info]) => {
    console.log(`  ${getStatusIcon(info.status)} ${component}: ${info.status}`);
  });
  
  if (data.recommendations.length > 0) {
    console.log('\n💡 Recommendations:');
    data.recommendations.forEach(rec => console.log(`  • ${rec}`));
  }
}

async function showStatistics() {
  console.log('📊 Gathering system statistics...\n');
  
  const response = await makeRequest('GET', '/api/sitemap/admin?action=statistics');
  const data = JSON.parse(response);
  
  console.log('Generation Statistics:');
  console.log(`  Total Generations: ${data.generation.totalGenerations}`);
  console.log(`  Average Time: ${Math.round(data.generation.averageTime)}ms`);
  console.log(`  Success Rate: ${data.generation.successRate.toFixed(1)}%`);
  console.log(`  Recent Errors: ${data.generation.recentErrors}`);
  
  console.log('\nCache Statistics:');
  console.log(`  Hit Rate: ${data.cache.hitRate.toFixed(1)}%`);
  console.log(`  Total Entries: ${data.cache.totalEntries}`);
  console.log(`  Memory Usage: ${formatBytes(data.cache.memoryUsage)}`);
  console.log(`  Oldest Entry: ${data.cache.oldestEntry ? new Date(data.cache.oldestEntry).toLocaleString() : 'N/A'}`);
  
  console.log('\nContent Statistics:');
  console.log(`  Total URLs: ${data.content.totalUrls}`);
  console.log('  By Content Type:');
  Object.entries(data.content.byContentType).forEach(([type, count]) => {
    console.log(`    ${type}: ${count}`);
  });
  console.log('  By Locale:');
  Object.entries(data.content.byLocale).forEach(([locale, count]) => {
    console.log(`    ${locale}: ${count}`);
  });
  
  console.log('\nPerformance Statistics:');
  console.log(`  Average Response Time: ${Math.round(data.performance.averageResponseTime)}ms`);
  console.log(`  Slow URLs: ${data.performance.slowUrls}`);
  console.log(`  Error Rate: ${data.performance.errorRate.toFixed(1)}%`);
}

async function warmCache(options) {
  console.log('🔥 Warming sitemap cache...\n');
  
  const payload = {
    action: 'warm-cache',
    options: {
      forceRegeneration: options.force || false,
      contentTypes: options.types ? options.types.split(',') : undefined,
      locales: options.locales ? options.locales.split(',') : undefined,
    },
  };
  
  const response = await makeRequest('POST', '/api/sitemap/admin', JSON.stringify(payload));
  const data = JSON.parse(response);
  
  if (data.success) {
    console.log('✅ Cache warming completed successfully!');
  } else {
    console.log('⚠️ Cache warming completed with some errors');
  }
  
  console.log(`\nResults:`);
  console.log(`  Warmed Sitemaps: ${data.warmedSitemaps.length}`);
  console.log(`  Total Time: ${data.totalTime}ms`);
  console.log(`  Cache Hits Before: ${data.cacheHitsBefore}`);
  console.log(`  Cache Hits After: ${data.cacheHitsAfter}`);
  
  if (data.warmedSitemaps.length > 0) {
    console.log('\nWarmed Sitemaps:');
    data.warmedSitemaps.forEach(sitemap => console.log(`  • ${sitemap}`));
  }
  
  if (data.errors.length > 0) {
    console.log('\n❌ Errors:');
    data.errors.forEach(error => console.log(`  • ${error}`));
  }
}

async function regenerateSitemaps(options) {
  console.log('🔄 Regenerating all sitemaps...\n');
  
  const payload = {
    action: 'regenerate',
    options: {
      clearCache: options.clearCache !== false,
      contentTypes: options.types ? options.types.split(',') : undefined,
      locales: options.locales ? options.locales.split(',') : undefined,
    },
  };
  
  const response = await makeRequest('POST', '/api/sitemap/admin', JSON.stringify(payload));
  const data = JSON.parse(response);
  
  if (data.success) {
    console.log('✅ Sitemap regeneration completed successfully!');
  } else {
    console.log('⚠️ Sitemap regeneration completed with some errors');
  }
  
  console.log(`\nResults:`);
  console.log(`  Regenerated Sitemaps: ${data.regeneratedSitemaps.length}`);
  console.log(`  Total URLs: ${data.totalUrls}`);
  console.log(`  Total Time: ${data.totalTime}ms`);
  
  if (data.regeneratedSitemaps.length > 0) {
    console.log('\nRegenerated Sitemaps:');
    data.regeneratedSitemaps.forEach(sitemap => console.log(`  • ${sitemap}`));
  }
  
  if (data.errors.length > 0) {
    console.log('\n❌ Errors:');
    data.errors.forEach(error => console.log(`  • ${error}`));
  }
}

async function clearCache() {
  console.log('🗑️ Clearing sitemap cache...\n');
  
  const payload = { action: 'clear-cache' };
  const response = await makeRequest('POST', '/api/sitemap/admin', JSON.stringify(payload));
  const data = JSON.parse(response);
  
  if (data.success) {
    console.log('✅ Cache cleared successfully!');
    console.log(`Timestamp: ${new Date(data.timestamp).toLocaleString()}`);
  } else {
    console.log('❌ Failed to clear cache');
  }
}

async function performMaintenance(options) {
  console.log('🧹 Performing system maintenance...\n');
  
  const payload = {
    action: 'maintenance',
    options: {
      clearOldCache: options.cache !== false,
      clearOldAnalytics: options.analytics !== false,
      cacheMaxAge: parseInt(options.cacheAge) || 7,
      analyticsMaxAge: parseInt(options.analyticsAge) || 30,
    },
  };
  
  const response = await makeRequest('POST', '/api/sitemap/admin', JSON.stringify(payload));
  const data = JSON.parse(response);
  
  console.log('Maintenance Results:');
  console.log(`  Cache Entries Cleared: ${data.cacheCleared}`);
  console.log(`  Analytics Entries Cleared: ${data.analyticsCleared}`);
  
  if (data.errors.length > 0) {
    console.log('\n❌ Errors:');
    data.errors.forEach(error => console.log(`  • ${error}`));
  } else {
    console.log('\n✅ Maintenance completed successfully!');
  }
}

async function generateDebugReport(options) {
  console.log('🐛 Generating debug report...\n');
  
  const params = new URLSearchParams({
    action: 'debug',
    cache: options.cache || 'true',
    analytics: options.analytics || 'true',
    validation: options.validation || 'false',
  });
  
  if (options.contentType) params.set('contentType', options.contentType);
  if (options.locale) params.set('locale', options.locale);
  
  const response = await makeRequest('GET', `/api/sitemap/admin?${params}`);
  const data = JSON.parse(response);
  
  console.log('System Information:');
  console.log(`  Timestamp: ${data.system.timestamp}`);
  console.log(`  Node Version: ${data.system.nodeVersion}`);
  console.log(`  Environment: ${data.system.environment}`);
  console.log(`  Base URL: ${data.system.config.baseUrl}`);
  console.log(`  Locales: ${data.system.config.locales.join(', ')}`);
  
  if (data.sampleGeneration) {
    console.log('\nSample Generation:');
    console.log(`  Content Type: ${data.sampleGeneration.contentType}`);
    console.log(`  Locale: ${data.sampleGeneration.locale}`);
    console.log(`  Success: ${data.sampleGeneration.success ? '✅' : '❌'}`);
    
    if (data.sampleGeneration.success) {
      console.log(`  URL Count: ${data.sampleGeneration.urlCount}`);
      console.log(`  XML Size: ${formatBytes(data.sampleGeneration.xmlSize)}`);
      console.log(`  Generation Time: ${data.sampleGeneration.generationTime}ms`);
      console.log(`  Valid: ${data.sampleGeneration.isValid ? '✅' : '❌'}`);
    } else {
      console.log(`  Error: ${data.sampleGeneration.error}`);
    }
  }
  
  if (options.output) {
    const fs = require('fs');
    fs.writeFileSync(options.output, JSON.stringify(data, null, 2));
    console.log(`\n📄 Full debug report saved to: ${options.output}`);
  }
}

async function validateSitemaps(options) {
  console.log('✅ Validating sitemaps...\n');
  
  const contentType = options.type || 'static';
  const locale = options.locale || 'en';
  const checkAccessibility = options.accessibility === 'true';
  
  const params = new URLSearchParams({
    type: contentType,
    locale: locale,
    accessibility: checkAccessibility.toString(),
    qa: 'true',
    format: 'summary',
  });
  
  const response = await makeRequest('GET', `/api/sitemap/validate?${params}`);
  console.log(response);
}

// Utility functions
function parseOptions(args) {
  const options = {};
  
  for (let i = 0; i < args.length; i += 2) {
    const key = args[i]?.replace(/^--?/, '');
    const value = args[i + 1];
    
    if (key) {
      options[key] = value || true;
    }
  }
  
  return options;
}

function makeRequest(method, path, body = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, config.baseUrl);
    const isHttps = url.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const options = {
      hostname: url.hostname,
      port: url.port || (isHttps ? 443 : 80),
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'SitemapAdminCLI/1.0',
      },
      timeout: config.timeout,
    };
    
    if (config.adminKey) {
      options.headers['Authorization'] = `Bearer ${config.adminKey}`;
    }
    
    if (body) {
      options.headers['Content-Length'] = Buffer.byteLength(body);
    }
    
    const req = client.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(data);
        } else {
          reject(new Error(`HTTP ${res.statusCode}: ${data}`));
        }
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (body) {
      req.write(body);
    }
    
    req.end();
  });
}

function getStatusIcon(status) {
  switch (status) {
    case 'healthy': return '✅';
    case 'warning': return '⚠️';
    case 'critical': return '❌';
    default: return '❓';
  }
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showHelp() {
  console.log(`
Sitemap Administration CLI

Usage:
  node scripts/sitemap-admin-cli.js <command> [options]

Commands:
  status                    Show system health status
  stats                     Show system statistics
  warm-cache               Warm up the sitemap cache
    --force                Force regeneration even if cached
    --types <types>        Comma-separated content types (static,posts,loops)
    --locales <locales>    Comma-separated locales (en,zh)
  
  regenerate               Regenerate all sitemaps
    --no-clear-cache       Don't clear cache before regeneration
    --types <types>        Comma-separated content types
    --locales <locales>    Comma-separated locales
  
  clear-cache              Clear all cached sitemaps
  
  maintenance              Perform system maintenance
    --no-cache             Don't clear old cache entries
    --no-analytics         Don't clear old analytics data
    --cache-age <days>     Max age for cache entries (default: 7)
    --analytics-age <days> Max age for analytics data (default: 30)
  
  debug                    Generate debug report
    --cache <true|false>   Include cache information (default: true)
    --analytics <true|false> Include analytics data (default: true)
    --validation <true|false> Include validation results (default: false)
    --content-type <type>  Content type for sample generation
    --locale <locale>      Locale for sample generation
    --output <file>        Save full report to file
  
  validate                 Validate sitemaps
    --type <type>          Content type to validate (default: static)
    --locale <locale>      Locale to validate (default: en)
    --accessibility        Check URL accessibility
  
  help                     Show this help message

Environment Variables:
  SITEMAP_BASE_URL         Base URL for API requests (default: http://localhost:3000)
  SITEMAP_ADMIN_KEY        Admin API key for authentication

Examples:
  node scripts/sitemap-admin-cli.js status
  node scripts/sitemap-admin-cli.js warm-cache --force --types static,posts
  node scripts/sitemap-admin-cli.js regenerate --locales en
  node scripts/sitemap-admin-cli.js debug --output debug-report.json
  node scripts/sitemap-admin-cli.js validate --type posts --locale zh --accessibility
`);
}

// Run the CLI
main().catch(console.error);