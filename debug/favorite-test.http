# Test favorite functionality
# Replace {track_uuid} with actual track UUID

### Check favorite status
GET http://localhost:3000/api/music/tracks/{track_uuid}/favorite
Content-Type: application/json

### Add to favorites
POST http://localhost:3000/api/music/tracks/{track_uuid}/favorite
Content-Type: application/json

### Remove from favorites
DELETE http://localhost:3000/api/music/tracks/{track_uuid}/favorite
Content-Type: application/json

### Check favorite status again
GET http://localhost:3000/api/music/tracks/{track_uuid}/favorite
Content-Type: application/json