# LoopCraft

AI-powered seamless loop music generation platform for creators and content makers.

![preview](preview.jpg)

## 快速开始

```bash
# 克隆仓库
git clone ***

# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev
```

## 核心功能

- 🎵 **AI 音乐生成**: 多提供商支持的高质量音乐生成
- 🔄 **无缝循环**: 专门创建完美循环的背景音乐
- 💳 **积分系统**: 灵活的使用计费模式
- 📜 **专业证书**: 为生成的音乐提供商业使用证书
- 🌍 **国际化**: 支持多语言（英文/中文）
- 💎 **高级功能**: 水印移除、高音质选项

## 技术栈

- **前端**: Next.js 15 + React 19 + TypeScript + Tailwind CSS
- **后端**: PostgreSQL + Drizzle ORM + NextAuth.js
- **AI**: 多提供商音乐生成集成
- **支付**: Stripe 集成
- **存储**: AWS S3

## 环境配置

```bash
# 复制环境变量模板
cp .env.example .env.development

# 编辑环境变量
# DATABASE_URL, NEXTAUTH_SECRET, STRIPE_SECRET_KEY 等
```

## 开发命令

```bash
pnpm dev          # 开发服务器
pnpm build        # 生产构建
pnpm test         # 运行测试
pnpm db:migrate   # 数据库迁移
pnpm db:studio    # 数据库管理
```

## 部署

### Vercel 部署
https://vercel.com

### Cloudflare 部署
https://cloudflare.com
```bash
# 切换到 Cloudflare 分支
git checkout cloudflare

# 配置环境变量
cp .env.example .env.production
cp wrangler.toml.example wrangler.toml

# 部署
npm run cf:deploy
```

## 文档

- 📖 [完整文档](docs/README.md)
- 🎯 [项目总结](docs/PROJECT_SUMMARY.md)
- 🤖 [AI 开发指南](CLAUDE.md)

## 社区

- [LoopCraft 官网](https://loopcraft.app)
- [LoopCraft Discord](XXXXXXXXXXXXXXXXXXXXXXXXXXXX)
- [LoopCraft Twitter](XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX)
- [LoopCraft GitHub](XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX)