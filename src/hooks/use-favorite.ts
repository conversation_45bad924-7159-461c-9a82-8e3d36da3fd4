import { useState, useEffect, useRef, useCallback } from "react";
import { useSession } from "next-auth/react";
import { toast } from "sonner";

// Global cache for favorite status to prevent unnecessary API calls
const favoriteCache = new Map<string, boolean>();
const loadingTracks = new Set<string>();

// Function to clear cache for a specific track or all tracks
export function clearFavoriteCache(trackUuid?: string) {
  if (trackUuid) {
    favoriteCache.delete(trackUuid);
  } else {
    favoriteCache.clear();
  }
}

export function useFavorite(trackUuid: string) {
  const { data: session } = useSession();
  const [isLiked, setIsLiked] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const isMountedRef = useRef(true);

  // Debug logging
  console.log("useFavorite hook:", {
    trackUuid,
    hasSession: !!session,
    hasUser: !!session?.user,
    userUuid: session?.user?.uuid,
    isLiked,
    isLoading,
    isInitialized
  });

  // Check favorite status on mount
  useEffect(() => {
    if (!session?.user || !trackUuid || trackUuid.trim() === '') {
      setIsInitialized(true);
      return;
    }
    
    // Check cache first
    if (favoriteCache.has(trackUuid)) {
      setIsLiked(favoriteCache.get(trackUuid)!);
      setIsInitialized(true);
      return;
    }

    const checkFavoriteStatus = async () => {
      if (loadingTracks.has(trackUuid)) return;
      
      try {
        setIsLoading(true);
        loadingTracks.add(trackUuid);
        
        console.log("Checking favorite status for track:", trackUuid);
        
        const response = await fetch(`/api/music/tracks/${trackUuid}/favorite`, {
          method: 'GET',
          headers: {
            'Cache-Control': 'no-cache',
          },
        });
        
        console.log("Favorite status response:", response.status, response.statusText);
        
        if (!isMountedRef.current) return;
        
        if (response.ok) {
          const data = await response.json();
          console.log("Favorite status data:", data);
          const isFavorite = data.is_favorite;
          
          // Update cache and state
          favoriteCache.set(trackUuid, isFavorite);
          setIsLiked(isFavorite);
        } else {
          const errorText = await response.text();
          console.error("Failed to check favorite status:", response.status, response.statusText, errorText);
          setIsLiked(false);
          favoriteCache.set(trackUuid, false);
        }
      } catch (error) {
        if (!isMountedRef.current) return;
        console.error("Error checking favorite status:", error);
        setIsLiked(false);
        favoriteCache.set(trackUuid, false);
      } finally {
        if (isMountedRef.current) {
          setIsInitialized(true);
          setIsLoading(false);
        }
        loadingTracks.delete(trackUuid);
      }
    };

    checkFavoriteStatus();
  }, [session?.user, trackUuid]);

  useEffect(() => {
    isMountedRef.current = true;
    
    // Cleanup on unmount
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const toggleFavorite = useCallback(async () => {
    if (!session?.user) {
      toast.error("Please sign in to add favorites");
      return;
    }

    if (!trackUuid || trackUuid.trim() === '') {
      console.error("No track UUID provided for favorite toggle");
      toast.error("Invalid track");
      return;
    }

    if (isLoading) return;

    setIsLoading(true);
    const previousState = isLiked;
    const newState = !isLiked;

    try {
      console.log("Toggling favorite for track:", trackUuid, "from", previousState, "to", newState);
      
      // Optimistic update
      setIsLiked(newState);
      favoriteCache.set(trackUuid, newState);

      const response = await fetch(`/api/music/tracks/${trackUuid}/favorite`, {
        method: previousState ? "DELETE" : "POST",
        headers: {
          "Content-Type": "application/json",
          'Cache-Control': 'no-cache',
        },
      });

      console.log("Toggle favorite response:", response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Toggle favorite error:", errorText);
        throw new Error(`Failed to update favorite status: ${response.status}`);
      }

      const data = await response.json();
      console.log("Toggle favorite data:", data);
      
      // Update state and cache based on server response
      const serverState = data.is_favorite;
      setIsLiked(serverState);
      favoriteCache.set(trackUuid, serverState);
      
      toast.success(
        serverState 
          ? "Added to favorites" 
          : "Removed from favorites"
      );
    } catch (error) {
      // Revert optimistic update on error
      setIsLiked(previousState);
      favoriteCache.set(trackUuid, previousState);
      console.error("Error toggling favorite:", error);
      toast.error("Failed to update favorites");
    } finally {
      setIsLoading(false);
    }
  }, [session?.user, trackUuid, isLiked, isLoading]);

  return {
    isLiked,
    isLoading,
    isInitialized,
    toggleFavorite,
  };
}