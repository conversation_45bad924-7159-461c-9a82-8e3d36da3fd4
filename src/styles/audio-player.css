/* Custom slider styles for audio player */
.slider {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

.slider::-webkit-slider-track {
  background: hsl(var(--muted));
  height: 8px;
  border-radius: 4px;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: hsl(var(--primary));
  height: 16px;
  width: 16px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 0 0 4px hsl(var(--primary) / 0.2);
}

.slider::-moz-range-track {
  background: hsl(var(--muted));
  height: 8px;
  border-radius: 4px;
  border: none;
}

.slider::-moz-range-thumb {
  background: hsl(var(--primary));
  height: 16px;
  width: 16px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.slider::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 0 0 4px hsl(var(--primary) / 0.2);
}

/* Progress bar specific styles */
.progress-slider::-webkit-slider-track {
  background: linear-gradient(
    to right,
    hsl(var(--primary)) 0%,
    hsl(var(--primary)) var(--progress, 0%),
    hsl(var(--muted)) var(--progress, 0%),
    hsl(var(--muted)) 100%
  );
}