"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";

import { Label } from "@/components/ui/label";
import { 
  Shuffle, 
  Music, 
  Loader2, 
  CheckCircle, 
  AlertCircle,
  Play,
  Pause,
  Download,
  Settings,
  Zap,
  Clock,
  Volume2
} from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

interface VariationData {
  uuid: string;
  variation_type: string;
  variation_params: any;
  file_url: string;
  file_size: number;
  file_format: string;
  created_at: string;
  status: "pending" | "processing" | "completed" | "failed";
}

interface VariationManagerProps {
  track: {
    uuid: string;
    title?: string;
    duration: number;
    style?: string;
    mood?: string;
    bpm?: number;
    user_uuid?: string;
  };
  className?: string;
}

const VARIATION_TYPES = [
  { 
    id: "tempo", 
    label: "Tempo", 
    icon: "⏱️", 
    description: "Change speed while maintaining pitch",
    color: "bg-blue-500"
  },
  { 
    id: "style", 
    label: "Style", 
    icon: "🎨", 
    description: "Transform musical style",
    color: "bg-purple-500"
  },
  { 
    id: "mood", 
    label: "Mood", 
    icon: "😊", 
    description: "Adjust emotional tone",
    color: "bg-green-500"
  },
  { 
    id: "key", 
    label: "Key", 
    icon: "🎹", 
    description: "Shift pitch/key",
    color: "bg-yellow-500"
  },
  { 
    id: "arrangement", 
    label: "Arrangement", 
    icon: "🎼", 
    description: "Modify instrumental arrangement",
    color: "bg-red-500"
  },
];

export default function VariationManager({ track, className }: VariationManagerProps) {
  const [variations, setVariations] = useState<VariationData[]>([]);
  const [selectedVariationType, setSelectedVariationType] = useState<string>("");
  const [variationParams, setVariationParams] = useState<any>({});
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [generationId, setGenerationId] = useState<string | null>(null);
  const [playingVariations, setPlayingVariations] = useState<Set<string>>(new Set());

  useEffect(() => {
    loadExistingVariations();
  }, [track.uuid]);

  const loadExistingVariations = async () => {
    try {
      const response = await fetch(`/api/music/variations/generate?original_track_uuid=${track.uuid}`);
      const data = await response.json();
      
      if (data.code === 0 && data.data.variations) {
        setVariations(data.data.variations);
      }
    } catch (error) {
      console.error("Failed to load existing variations:", error);
    }
  };

  const handleVariationTypeChange = (type: string) => {
    setSelectedVariationType(type);
    setVariationParams({});
  };

  const renderVariationParams = () => {
    switch (selectedVariationType) {
      case "tempo":
        return (
          <div className="space-y-4">
            <Label>Tempo Change (BPM)</Label>
            <Slider
              value={[variationParams.tempo_change || 0]}
              onValueChange={(value) => setVariationParams({ tempo_change: value[0] })}
              min={-50}
              max={50}
              step={5}
              className="w-full"
            />
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>-50 BPM</span>
              <span>{variationParams.tempo_change || 0} BPM</span>
              <span>+50 BPM</span>
            </div>
          </div>
        );
      
      case "style":
        return (
          <div className="space-y-4">
            <Label>New Style</Label>
            <Select
              value={variationParams.new_style || ""}
              onValueChange={(value) => setVariationParams({ new_style: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a style" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="electronic">Electronic</SelectItem>
                <SelectItem value="acoustic">Acoustic</SelectItem>
                <SelectItem value="jazz">Jazz</SelectItem>
                <SelectItem value="rock">Rock</SelectItem>
                <SelectItem value="classical">Classical</SelectItem>
                <SelectItem value="ambient">Ambient</SelectItem>
                <SelectItem value="hip-hop">Hip-Hop</SelectItem>
                <SelectItem value="folk">Folk</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );
      
      case "mood":
        return (
          <div className="space-y-4">
            <Label>New Mood</Label>
            <Select
              value={variationParams.new_mood || ""}
              onValueChange={(value) => setVariationParams({ new_mood: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a mood" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="upbeat">Upbeat</SelectItem>
                <SelectItem value="calm">Calm</SelectItem>
                <SelectItem value="energetic">Energetic</SelectItem>
                <SelectItem value="melancholic">Melancholic</SelectItem>
                <SelectItem value="mysterious">Mysterious</SelectItem>
                <SelectItem value="romantic">Romantic</SelectItem>
                <SelectItem value="dramatic">Dramatic</SelectItem>
                <SelectItem value="peaceful">Peaceful</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );
      
      case "key":
        return (
          <div className="space-y-4">
            <Label>Key Shift (Semitones)</Label>
            <Slider
              value={[variationParams.key_shift || 0]}
              onValueChange={(value) => setVariationParams({ key_shift: value[0] })}
              min={-12}
              max={12}
              step={1}
              className="w-full"
            />
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>-12 semitones</span>
              <span>{variationParams.key_shift || 0} semitones</span>
              <span>+12 semitones</span>
            </div>
          </div>
        );
      
      case "arrangement":
        return (
          <div className="space-y-4">
            <Label>Arrangement Type</Label>
            <Select
              value={variationParams.arrangement_type || ""}
              onValueChange={(value) => setVariationParams({ arrangement_type: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select arrangement" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="minimal">Minimal</SelectItem>
                <SelectItem value="full">Full</SelectItem>
                <SelectItem value="acoustic">Acoustic</SelectItem>
                <SelectItem value="electronic">Electronic</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );
      
      default:
        return null;
    }
  };

  const generateVariation = async () => {
    if (!selectedVariationType) {
      toast.error("Please select a variation type");
      return;
    }

    setIsGenerating(true);
    setGenerationProgress(0);

    try {
      const response = await fetch("/api/music/variations/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          original_track_uuid: track.uuid,
          variation_type: selectedVariationType,
          variation_params: variationParams,
          quality: "standard",
        }),
      });

      const data = await response.json();

      if (data.code === 0) {
        setGenerationId(data.data.generation_id);
        toast.success("Variation generation started!");
        
        // Poll for progress
        pollGenerationProgress(data.data.variation_uuid, data.data.estimated_time);
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      console.error("Variation generation failed:", error);
      toast.error("Failed to start variation generation");
      setIsGenerating(false);
    }
  };

  const pollGenerationProgress = async (variationUuid: string, estimatedTime: number) => {
    const startTime = Date.now();
    const pollInterval = 3000; // 3 seconds

    const poll = async () => {
      try {
        const response = await fetch(
          `/api/music/variations/generate?variation_uuid=${variationUuid}`
        );
        const data = await response.json();

        if (data.code === 0) {
          if (data.data.status === "completed") {
            setGenerationProgress(100);
            setIsGenerating(false);
            toast.success("Variation generation completed!");
            await loadExistingVariations();
            return;
          } else if (data.data.status === "failed") {
            setIsGenerating(false);
            toast.error("Variation generation failed");
            return;
          }
        }

        // Update progress based on elapsed time
        const elapsed = Date.now() - startTime;
        const progress = Math.min((elapsed / (estimatedTime * 1000)) * 100, 95);
        setGenerationProgress(progress);

        // Continue polling
        setTimeout(poll, pollInterval);
      } catch (error) {
        console.error("Failed to poll generation progress:", error);
        setTimeout(poll, pollInterval);
      }
    };

    poll();
  };

  const downloadVariation = async (variation: VariationData) => {
    try {
      const link = document.createElement("a");
      link.href = variation.file_url;
      link.download = `${track.title || "track"}-${variation.variation_type}.${variation.file_format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast.success(`Downloaded ${variation.variation_type} variation`);
    } catch (error) {
      console.error("Download failed:", error);
      toast.error("Download failed");
    }
  };

  const toggleVariationPlayback = (variationUuid: string) => {
    setPlayingVariations(prev => {
      const newSet = new Set(prev);
      if (newSet.has(variationUuid)) {
        newSet.delete(variationUuid);
      } else {
        newSet.add(variationUuid);
      }
      return newSet;
    });
  };

  const formatFileSize = (bytes: number) => {
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(1)} MB`;
  };

  const getVariationTypeInfo = (type: string) => {
    return VARIATION_TYPES.find(t => t.id === type) || VARIATION_TYPES[0];
  };

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shuffle className="h-5 w-5" />
          Variations
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Existing Variations */}
        {variations.length > 0 && (
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Generated Variations</h3>
            
            <div className="space-y-3">
              {variations.map((variation) => {
                const typeInfo = getVariationTypeInfo(variation.variation_type);
                const isPlaying = playingVariations.has(variation.uuid);
                
                return (
                  <div
                    key={variation.uuid}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <div className={cn("w-8 h-8 rounded-full flex items-center justify-center text-white text-sm", typeInfo.color)}>
                        {typeInfo.icon}
                      </div>
                      <div>
                        <div className="font-medium text-sm">{typeInfo.label} Variation</div>
                        <div className="text-xs text-muted-foreground">
                          {formatFileSize(variation.file_size)} • {variation.file_format.toUpperCase()}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Badge
                        variant={variation.status === "completed" ? "default" : "secondary"}
                      >
                        {variation.status}
                      </Badge>
                      {variation.status === "completed" && (
                        <>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleVariationPlayback(variation.uuid)}
                          >
                            {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => downloadVariation(variation)}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Generation Progress */}
        {isGenerating && (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-sm font-medium">Generating variation...</span>
            </div>
            <Progress value={generationProgress} className="w-full" />
            <p className="text-xs text-muted-foreground">
              This may take a few minutes depending on the variation type.
            </p>
          </div>
        )}

        {/* Variation Generator */}
        {!isGenerating && (
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Generate New Variation</h3>

            {/* Variation Type Selection */}
            <div className="space-y-3">
              <Label>Variation Type</Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {VARIATION_TYPES.map((type) => (
                  <div
                    key={type.id}
                    className={cn(
                      "flex items-center space-x-3 p-3 border rounded-lg cursor-pointer transition-colors",
                      selectedVariationType === type.id
                        ? "border-primary bg-primary/5"
                        : "hover:bg-muted"
                    )}
                    onClick={() => handleVariationTypeChange(type.id)}
                  >
                    <div className={cn("w-8 h-8 rounded-full flex items-center justify-center text-white text-sm", type.color)}>
                      {type.icon}
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-sm">{type.label}</div>
                      <div className="text-xs text-muted-foreground">{type.description}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Variation Parameters */}
            {selectedVariationType && (
              <div className="space-y-4">
                <Label>Parameters</Label>
                {renderVariationParams()}
              </div>
            )}

            <Button
              onClick={generateVariation}
              disabled={!selectedVariationType || isGenerating}
              className="w-full"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Zap className="mr-2 h-4 w-4" />
                  Generate {selectedVariationType ? getVariationTypeInfo(selectedVariationType).label : ""} Variation
                </>
              )}
            </Button>
          </div>
        )}

        {variations.length === 0 && !isGenerating && (
          <div className="text-center py-8 text-muted-foreground">
            <Shuffle className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No variations generated yet.</p>
            <p className="text-sm">Select a variation type above to get started.</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
