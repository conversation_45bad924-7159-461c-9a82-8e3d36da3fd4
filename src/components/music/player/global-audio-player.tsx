"use client";

import { useAudioPlayer } from "@/contexts/audio-player-context";
import { useFavorite } from "@/hooks/use-favorite";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Play, 
  Pause, 
  Repeat,
  Heart,
  Share2,
  ExternalLink,
  Minimize2
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useCallback } from "react";
import { generateTrackUrl } from "@/lib/track-slug";
import { useLocale } from "next-intl";
import Link from "next/link";
import { toast } from "sonner";

export default function GlobalAudioPlayer() {
  const {
    currentTrack,
    isPlaying,
    currentTime,
    duration,
    isLooping,
    isLoading,
    pause,
    resume,
    stop,
    seek,
    toggleLoop,
  } = useAudioPlayer();

  const locale = useLocale();
  
  // Use favorite hook for the current track
  const trackUuid = currentTrack?.uuid || '';
  const { isLiked, isLoading: isFavoriteLoading, toggleFavorite } = useFavorite(trackUuid);

  // Define callback functions first
  const handlePlayPause = useCallback(() => {
    try {
      if (isPlaying) {
        pause();
      } else {
        resume();
      }
    } catch (error) {
      console.error('Play/pause error:', error);
      toast.error('Playback error occurred');
    }
  }, [isPlaying, pause, resume]);

  const handleSeek = useCallback((value: number[]) => {
    try {
      if (duration > 0) {
        const newTime = (value[0] / 100) * duration;
        seek(Math.max(0, Math.min(duration, newTime)));
      }
    } catch (error) {
      console.error('Seek error:', error);
    }
  }, [duration, seek]);

  // Simplified share function - consistent with track detail page
  const handleShare = useCallback(async () => {
    if (!currentTrack) return;

    const trackUrl = `${window.location.origin}${generateTrackUrl(currentTrack, locale)}`;
    const shareTitle = currentTrack.title || "AI Music Loop";
    const shareText = currentTrack.prompt || "Check out this AI-generated music loop!";

    try {
      if (navigator.share) {
        await navigator.share({
          title: shareTitle,
          text: shareText,
          url: trackUrl,
        });
        toast.success("Shared successfully!");
      } else {
        // Fallback: copy to clipboard
        await navigator.clipboard.writeText(trackUrl);
        toast.success("Link copied to clipboard!");
      }
    } catch (error) {
      // Check if user cancelled the share
      if (error instanceof Error && error.name === 'AbortError') {
        // User cancelled sharing, don't show error
        return;
      }

      // For other errors, try clipboard fallback
      try {
        await navigator.clipboard.writeText(trackUrl);
        toast.success("Link copied to clipboard!");
      } catch (clipboardError) {
        toast.error("Sharing failed. Please try again.");
        console.error("Share error:", error);
      }
    }
  }, [currentTrack, locale]);



  // Don't render if no track is loaded
  if (!currentTrack) {
    return null;
  }

  // Error boundary for track data
  if (!currentTrack.uuid || !currentTrack.file_url) {
    console.error('Invalid track data:', currentTrack);
    return null;
  }



  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  const progress = duration > 0 ? (currentTime / duration) * 100 : 0;

  return (
    <Card className="fixed bottom-0 left-0 right-0 z-50 rounded-none border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 shadow-lg">
      <div className="container mx-auto px-2 sm:px-4 py-2 sm:py-3 max-w-7xl">
        <div className="flex items-center gap-1 sm:gap-2 md:gap-4">
          {/* Track Info */}
          <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-lg flex items-center justify-center flex-shrink-0">
              <div className="w-5 h-5 sm:w-6 sm:h-6 bg-primary/40 rounded-sm"></div>
            </div>
            
            <div className="min-w-0 flex-1">
              <div className="flex items-center gap-1 sm:gap-2">
                <Link 
                  href={generateTrackUrl(currentTrack, locale)}
                  className="font-medium text-sm truncate hover:text-primary transition-colors"
                >
                  {currentTrack.title || "Untitled Track"}
                </Link>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-5 w-5 sm:h-6 sm:w-6 opacity-60 hover:opacity-100 flex-shrink-0"
                  asChild
                >
                  <Link href={generateTrackUrl(currentTrack, locale)}>
                    <ExternalLink className="h-2.5 w-2.5 sm:h-3 sm:w-3" />
                  </Link>
                </Button>
              </div>
              <div className="flex items-center gap-1 sm:gap-2 text-xs text-muted-foreground">
                {currentTrack.style && (
                  <Badge variant="secondary" className="text-xs px-1 sm:px-1.5 py-0.5 hidden sm:inline-flex">
                    {currentTrack.style}
                  </Badge>
                )}
                {currentTrack.bpm && (
                  <span className="hidden sm:inline">{currentTrack.bpm} BPM</span>
                )}
                <span>{formatTime(duration)}</span>
              </div>
            </div>
          </div>

          {/* Controls */}
          <div className="flex items-center gap-1 sm:gap-2">
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 sm:h-8 sm:w-8 hidden sm:flex"
              onClick={toggleLoop}
              title={`Loop: ${isLooping ? 'On' : 'Off'}`}
            >
              <Repeat className={cn("h-3.5 w-3.5 sm:h-4 sm:w-4", isLooping && "text-primary")} />
            </Button>

            <Button
              variant="default"
              size="icon"
              className="h-9 w-9 sm:h-10 sm:w-10 rounded-full focus:ring-2 focus:ring-primary focus:ring-offset-2"
              onClick={handlePlayPause}
              disabled={isLoading}
              title={isPlaying ? 'Pause' : 'Play'}
              aria-label={isPlaying ? 'Pause track' : 'Play track'}
            >
              {isLoading ? (
                <div className="animate-spin rounded-full h-3.5 w-3.5 sm:h-4 sm:w-4 border-2 border-background border-t-transparent" />
              ) : isPlaying ? (
                <Pause className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
              ) : (
                <Play className="h-3.5 w-3.5 sm:h-4 sm:w-4 ml-0.5" />
              )}
            </Button>

            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 sm:h-8 sm:w-8 hover:bg-destructive/10 hover:text-destructive"
              onClick={stop}
              title="Close player"
            >
              <Minimize2 className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
            </Button>
          </div>

          {/* Progress */}
          <div className="flex items-center gap-1 sm:gap-2 flex-1 max-w-xs sm:max-w-md">
            <span className="text-xs text-muted-foreground min-w-[30px] sm:min-w-[35px] text-right font-mono">
              {formatTime(currentTime)}
            </span>
            
            <div className="flex-1 relative group">
              <Slider
                value={[progress]}
                onValueChange={handleSeek}
                max={100}
                step={0.1}
                className="flex-1"
                title="Seek"
              />
              {/* Progress indicator */}
              <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 px-2 py-1 bg-popover border rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none">
                {Math.round(progress)}%
              </div>
            </div>
            
            <span className="text-xs text-muted-foreground min-w-[30px] sm:min-w-[35px] font-mono">
              {formatTime(duration)}
            </span>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-1 sm:gap-2">

            {/* Favorite Button */}
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "h-7 w-7 sm:h-8 sm:w-8 hover:bg-red-50 hover:text-red-500 transition-colors relative",
                isLiked && "text-red-500 hover:text-red-600"
              )}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                if (typeof toggleFavorite === 'function') {
                  toggleFavorite();
                } else {
                  console.error("toggleFavorite is not a function:", toggleFavorite);
                }
              }}
              disabled={isFavoriteLoading}
              title={isLiked ? "Remove from favorites" : "Add to favorites"}
            >
              <Heart className={cn("h-3.5 w-3.5 sm:h-4 sm:w-4", isLiked && "fill-current")} />
              {isFavoriteLoading && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-2.5 h-2.5 sm:w-3 sm:h-3 border border-current border-t-transparent rounded-full animate-spin" />
                </div>
              )}
            </Button>

            {/* Share Button */}
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 sm:h-8 sm:w-8 hover:bg-blue-50 hover:text-blue-600 transition-colors"
              onClick={handleShare}
              title="Share track"
            >
              <Share2 className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
}
