"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Search,
  Filter,
  Music,
  Sparkles
} from "lucide-react";
import TrackCard from "@/components/music/track/track-card";
import { Track } from "@/types/music";

// API response interface
interface PublicTracksResponse {
  tracks: Track[];
  pagination: {
    total: number;
    page: number;
    per_page: number;
    total_pages: number;
    has_more: boolean;
  };
  filters: {
    available_styles: string[];
    available_moods: string[];
    bpm_range: { min: number; max: number };
  };
}

export default function ExploreMusic() {
  const [tracks, setTracks] = useState<Track[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedStyle, setSelectedStyle] = useState("all");
  const [selectedMood, setSelectedMood] = useState("all");
  const [selectedDuration, setSelectedDuration] = useState("all");
  const [sortBy, setSortBy] = useState("download_count"); // download_count, created_at, title
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [hasMore, setHasMore] = useState(false);

  // Filter options from API
  const [availableStyles, setAvailableStyles] = useState<string[]>([]);
  const [availableMoods, setAvailableMoods] = useState<string[]>([]);

  // Fetch tracks from API
  const fetchTracks = useCallback(async (page: number = 1, append: boolean = false) => {
    try {
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: page.toString(),
        per_page: "12",
        sort_by: sortBy,
        sort_order: "desc",
      });

      if (searchQuery.trim()) {
        params.append("search", searchQuery.trim());
      }
      if (selectedStyle !== "all") {
        params.append("style", selectedStyle);
      }
      if (selectedMood !== "all") {
        params.append("mood", selectedMood);
      }
      if (selectedDuration !== "all") {
        params.append("duration", selectedDuration);
      }

      const response = await fetch(`/api/tracks/public?${params}`);
      if (!response.ok) {
        throw new Error("Failed to fetch tracks");
      }

      const result = await response.json();
      if (result.code !== 0) {
        throw new Error(result.message || "Failed to fetch tracks");
      }

      const data: PublicTracksResponse = result.data;

      if (append) {
        setTracks(prev => [...prev, ...data.tracks]);
      } else {
        setTracks(data.tracks);
      }

      setCurrentPage(data.pagination.page);
      setTotalPages(data.pagination.total_pages);
      setHasMore(data.pagination.has_more);

      // Update filter options
      setAvailableStyles(data.filters.available_styles);
      setAvailableMoods(data.filters.available_moods);

    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load tracks");
      console.error("Failed to fetch tracks:", err);
    } finally {
      setIsLoading(false);
    }
  }, [searchQuery, selectedStyle, selectedMood, selectedDuration, sortBy]);

  // Initial load
  useEffect(() => {
    fetchTracks(1, false);
  }, [fetchTracks]);

  // Handle load more
  const handleLoadMore = () => {
    if (hasMore && !isLoading) {
      fetchTracks(currentPage + 1, true);
    }
  };

  const clearFilters = () => {
    setSearchQuery("");
    setSelectedStyle("all");
    setSelectedMood("all");
    setSelectedDuration("all");
    setSortBy("download_count");
  };

  const handleTrackPlay = (track: Track) => {
    // Global audio player will handle this through TrackCard
  };

  return (
    <div className="container py-8">
      {/* Header */}
      <div className="text-center mb-12">
        <div className="flex items-center justify-center mb-4">
          <Badge variant="secondary" className="px-4 py-2 text-sm">
            <Sparkles className="mr-2 h-4 w-4" />
            Community Showcase
          </Badge>
        </div>

        <h1 className="text-4xl font-bold mb-4">
          Explore AI Music{" "}
          <span className="bg-gradient-to-r from-primary via-primary to-secondary bg-clip-text text-transparent">
            Loops
          </span>
        </h1>

        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Discover amazing AI-generated music loops created by our community.
          Find inspiration and explore different styles, moods, and genres.
        </p>
      </div>

      {/* Filters and Search */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filter & Search
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search tracks..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Style Filter */}
            <Select value={selectedStyle} onValueChange={setSelectedStyle}>
              <SelectTrigger>
                <SelectValue placeholder="All Styles" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Styles</SelectItem>
                {availableStyles.map((style) => (
                  <SelectItem key={style} value={style}>
                    {style.charAt(0).toUpperCase() + style.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Mood Filter */}
            <Select value={selectedMood} onValueChange={setSelectedMood}>
              <SelectTrigger>
                <SelectValue placeholder="All Moods" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Moods</SelectItem>
                {availableMoods.map((mood) => (
                  <SelectItem key={mood} value={mood}>
                    {mood.charAt(0).toUpperCase() + mood.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Duration Filter */}
            <Select value={selectedDuration} onValueChange={setSelectedDuration}>
              <SelectTrigger>
                <SelectValue placeholder="All Durations" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Durations</SelectItem>
                <SelectItem value="15">15 seconds</SelectItem>
                <SelectItem value="30">30 seconds</SelectItem>
                <SelectItem value="60">60 seconds</SelectItem>
              </SelectContent>
            </Select>

            {/* Sort Filter */}
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger>
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="download_count">Most Popular</SelectItem>
                <SelectItem value="created_at">Most Recent</SelectItem>
                <SelectItem value="title">Title A-Z</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              Showing {tracks.length} tracks
            </div>
            <Button variant="outline" onClick={clearFilters}>
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Error State */}
      {error && (
        <div className="text-center py-12">
          <div className="text-red-500 mb-4">
            <Music className="h-12 w-12 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Error Loading Tracks</h3>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={() => fetchTracks(1, false)}>
              Try Again
            </Button>
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoading && tracks.length === 0 && (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading tracks...</p>
        </div>
      )}

      {/* Empty State */}
      {!isLoading && !error && tracks.length === 0 && (
        <div className="text-center py-12">
          <Music className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No tracks found</h3>
          <p className="text-muted-foreground mb-4">
            Try adjusting your filters or search terms
          </p>
          <Button variant="outline" onClick={clearFilters}>
            Clear Filters
          </Button>
        </div>
      )}

      {/* Results */}
      {tracks.length > 0 && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {tracks.map((track) => (
              <TrackCard
                key={track.uuid}
                track={track}
                onPlay={handleTrackPlay}
              />
            ))}
          </div>

          {/* Load More Button */}
          {hasMore && (
            <div className="text-center mt-8">
              <Button
                onClick={handleLoadMore}
                disabled={isLoading}
                variant="outline"
                size="lg"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent mr-2" />
                    Loading...
                  </>
                ) : (
                  "Load More Tracks"
                )}
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}
