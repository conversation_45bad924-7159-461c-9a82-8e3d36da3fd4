"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import { Progress } from "@/components/ui/progress";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Download, 
  Music, 
  Loader2, 
  CheckCircle, 
  AlertCircle,
  Play,
  Pause,
  Volume2,
  Package
} from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

interface StemData {
  uuid: string;
  stem_type: string;
  file_url: string;
  file_size: number;
  file_format: string;
  created_at: string;
}

interface StemManagerProps {
  track: {
    uuid: string;
    title?: string;
    duration: number;
    user_uuid?: string;
  };
  className?: string;
}

const STEM_TYPES = [
  { id: "drums", label: "Drums", icon: "🥁", color: "bg-red-500" },
  { id: "bass", label: "Bass", icon: "🎸", color: "bg-blue-500" },
  { id: "melody", label: "Melody", icon: "🎹", color: "bg-green-500" },
  { id: "harmony", label: "Harmony", icon: "🎵", color: "bg-purple-500" },
  { id: "vocals", label: "Vocals", icon: "🎤", color: "bg-pink-500" },
  { id: "percussion", label: "Percussion", icon: "🥁", color: "bg-orange-500" },
  { id: "lead", label: "Lead", icon: "🎺", color: "bg-yellow-500" },
  { id: "pad", label: "Pad", icon: "🎹", color: "bg-indigo-500" },
];

export default function StemManager({ track, className }: StemManagerProps) {
  const [stems, setStems] = useState<StemData[]>([]);
  const [selectedStemTypes, setSelectedStemTypes] = useState<string[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [generationId, setGenerationId] = useState<string | null>(null);
  const [playingStems, setPlayingStems] = useState<Set<string>>(new Set());

  useEffect(() => {
    loadExistingStems();
  }, [track.uuid]);

  const loadExistingStems = async () => {
    try {
      const response = await fetch(`/api/music/stems/generate?track_uuid=${track.uuid}`);
      const data = await response.json();
      
      if (data.code === 0 && data.data.stems) {
        setStems(data.data.stems);
      }
    } catch (error) {
      console.error("Failed to load existing stems:", error);
    }
  };

  const handleStemTypeToggle = (stemType: string) => {
    setSelectedStemTypes(prev => 
      prev.includes(stemType)
        ? prev.filter(type => type !== stemType)
        : [...prev, stemType]
    );
  };

  const selectAllStemTypes = () => {
    setSelectedStemTypes(STEM_TYPES.map(type => type.id));
  };

  const clearStemSelection = () => {
    setSelectedStemTypes([]);
  };

  const generateStems = async () => {
    if (selectedStemTypes.length === 0) {
      toast.error("Please select at least one stem type");
      return;
    }

    setIsGenerating(true);
    setGenerationProgress(0);

    try {
      const response = await fetch("/api/music/stems/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          track_uuid: track.uuid,
          stem_types: selectedStemTypes,
          quality: "standard",
        }),
      });

      const data = await response.json();

      if (data.code === 0) {
        setGenerationId(data.data.generation_id);
        toast.success("Stem generation started!");
        
        // Poll for progress
        pollGenerationProgress(data.data.generation_id, data.data.estimated_time);
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      console.error("Stem generation failed:", error);
      toast.error("Failed to start stem generation");
      setIsGenerating(false);
    }
  };

  const pollGenerationProgress = async (genId: string, estimatedTime: number) => {
    const startTime = Date.now();
    const pollInterval = 2000; // 2 seconds

    const poll = async () => {
      try {
        const response = await fetch(
          `/api/music/stems/generate?track_uuid=${track.uuid}&generation_id=${genId}`
        );
        const data = await response.json();

        if (data.code === 0) {
          if (data.data.status === "completed") {
            setStems(data.data.stems || []);
            setGenerationProgress(100);
            setIsGenerating(false);
            toast.success("Stem generation completed!");
            return;
          } else if (data.data.status === "failed") {
            setIsGenerating(false);
            toast.error("Stem generation failed");
            return;
          }
        }

        // Update progress based on elapsed time
        const elapsed = Date.now() - startTime;
        const progress = Math.min((elapsed / (estimatedTime * 1000)) * 100, 95);
        setGenerationProgress(progress);

        // Continue polling
        setTimeout(poll, pollInterval);
      } catch (error) {
        console.error("Failed to poll generation progress:", error);
        setTimeout(poll, pollInterval);
      }
    };

    poll();
  };

  const downloadStem = async (stem: StemData) => {
    try {
      const link = document.createElement("a");
      link.href = stem.file_url;
      link.download = `${track.title || "track"}-${stem.stem_type}.${stem.file_format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast.success(`Downloaded ${stem.stem_type} stem`);
    } catch (error) {
      console.error("Download failed:", error);
      toast.error("Download failed");
    }
  };

  const downloadAllStems = async () => {
    if (stems.length === 0) {
      toast.error("No stems available for download");
      return;
    }

    try {
      const response = await fetch("/api/music/stems/download", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          track_uuid: track.uuid,
          format: "zip",
        }),
      });

      const data = await response.json();

      if (data.code === 0) {
        const link = document.createElement("a");
        link.href = data.data.download_url;
        link.download = `${track.title || "track"}-stems.zip`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        toast.success("Downloading all stems as ZIP");
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      console.error("Bulk download failed:", error);
      toast.error("Failed to download stems");
    }
  };

  const toggleStemPlayback = (stemUuid: string) => {
    setPlayingStems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(stemUuid)) {
        newSet.delete(stemUuid);
      } else {
        newSet.add(stemUuid);
      }
      return newSet;
    });
  };

  const formatFileSize = (bytes: number) => {
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(1)} MB`;
  };

  const existingStemTypes = stems.map(stem => stem.stem_type);
  const availableStemTypes = STEM_TYPES.filter(type => !existingStemTypes.includes(type.id));

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Music className="h-5 w-5" />
          Stem Separation
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Existing Stems */}
        {stems.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium">Available Stems</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={downloadAllStems}
                className="gap-2"
              >
                <Package className="h-4 w-4" />
                Download All
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {stems.map((stem) => {
                const stemType = STEM_TYPES.find(type => type.id === stem.stem_type);
                const isPlaying = playingStems.has(stem.uuid);
                
                return (
                  <div
                    key={stem.uuid}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <div className={cn("w-8 h-8 rounded-full flex items-center justify-center text-white text-sm", stemType?.color || "bg-gray-500")}>
                        {stemType?.icon || "🎵"}
                      </div>
                      <div>
                        <div className="font-medium text-sm">{stemType?.label || stem.stem_type}</div>
                        <div className="text-xs text-muted-foreground">
                          {formatFileSize(stem.file_size)}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleStemPlayback(stem.uuid)}
                      >
                        {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => downloadStem(stem)}
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Generation Progress */}
        {isGenerating && (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-sm font-medium">Generating stems...</span>
            </div>
            <Progress value={generationProgress} className="w-full" />
            <p className="text-xs text-muted-foreground">
              This may take a few minutes depending on track length and selected stems.
            </p>
          </div>
        )}

        {/* Stem Type Selection */}
        {availableStemTypes.length > 0 && !isGenerating && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium">Generate New Stems</h3>
              <div className="flex gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={selectAllStemTypes}
                >
                  Select All
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearStemSelection}
                >
                  Clear
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {availableStemTypes.map((stemType) => (
                <div
                  key={stemType.id}
                  className={cn(
                    "flex items-center space-x-2 p-3 border rounded-lg cursor-pointer transition-colors",
                    selectedStemTypes.includes(stemType.id)
                      ? "border-primary bg-primary/5"
                      : "hover:bg-muted"
                  )}
                  onClick={() => handleStemTypeToggle(stemType.id)}
                >
                  <Checkbox
                    checked={selectedStemTypes.includes(stemType.id)}
                    onChange={() => handleStemTypeToggle(stemType.id)}
                  />
                  <div className="flex items-center gap-2">
                    <span className="text-lg">{stemType.icon}</span>
                    <span className="text-sm font-medium">{stemType.label}</span>
                  </div>
                </div>
              ))}
            </div>

            <Button
              onClick={generateStems}
              disabled={selectedStemTypes.length === 0 || isGenerating}
              className="w-full"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Music className="mr-2 h-4 w-4" />
                  Generate Selected Stems ({selectedStemTypes.length})
                </>
              )}
            </Button>
          </div>
        )}

        {stems.length === 0 && !isGenerating && (
          <div className="text-center py-8 text-muted-foreground">
            <Music className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No stems generated yet.</p>
            <p className="text-sm">Select stem types above to get started.</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
