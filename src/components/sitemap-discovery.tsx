import { SITEMAP_CONFIG } from "@/constants/sitemap";

/**
 * Sitemap Discovery Component
 * Adds sitemap discovery meta tags to HTML head
 */
export function SitemapDiscovery() {
  const baseUrl = SITEMAP_CONFIG.baseUrl;

  return (
    <>
      {/* Main sitemap */}
      <link rel="sitemap" type="application/xml" href={`${baseUrl}/sitemap.xml`} />
      
      {/* Additional sitemaps for better discovery */}
      <link rel="sitemap" type="application/xml" href={`${baseUrl}/api/sitemap/static`} />
      <link rel="sitemap" type="application/xml" href={`${baseUrl}/api/sitemap/posts`} />
      <link rel="sitemap" type="application/xml" href={`${baseUrl}/api/sitemap/loops`} />
      
      {/* Alternate language sitemaps */}
      {SITEMAP_CONFIG.locales.map((locale) => (
        <link
          key={locale}
          rel="alternate"
          type="application/xml"
          hrefLang={locale}
          href={`${baseUrl}${locale === SITEMAP_CONFIG.defaultLocale ? '' : `/${locale}`}/sitemap.xml`}
        />
      ))}
    </>
  );
}

/**
 * Sitemap Discovery for specific content types
 */
export function ContentSitemapDiscovery({ contentType }: { contentType: 'static' | 'posts' | 'loops' }) {
  const baseUrl = SITEMAP_CONFIG.baseUrl;

  return (
    <link 
      rel="sitemap" 
      type="application/xml" 
      href={`${baseUrl}/api/sitemap/${contentType}`} 
    />
  );
}