import { Badge } from "@/components/ui/badge";

import { TechButton } from "@/components/ui/tech-button";
import HappyUsers from "./happy-users";
import HeroBg from "./bg";
import { Hero as HeroType } from "@/types/blocks/hero";
import Icon from "@/components/icon";
import { Link } from "@/i18n/navigation";
import { <PERSON><PERSON><PERSON>, Zap, Music } from "lucide-react";

export default function Hero({ hero }: { hero: HeroType }) {
  if (hero.disabled) {
    return null;
  }

  const highlightText = hero.highlight_text;
  let texts = null;
  if (highlightText) {
    texts = hero.title?.split(highlightText, 2);
  }

  return (
    <>
      <HeroBg />
      <section className="relative py-24 overflow-hidden">
        {/* Tech grid background */}
        <div className="absolute inset-0 tech-grid opacity-30" />

        {/* Floating elements */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-primary/10 rounded-full blur-xl floating" />
        <div className="absolute top-40 right-20 w-32 h-32 bg-neon-cyan/10 rounded-full blur-xl floating" style={{ animationDelay: '1s' }} />
        <div className="absolute bottom-20 left-1/4 w-24 h-24 bg-neon-purple/10 rounded-full blur-xl floating" style={{ animationDelay: '2s' }} />

        <div className="container relative z-10">
          {hero.show_badge && (
            <div className="flex items-center justify-center mb-8">
              <div className="glass-card px-4 py-2 rounded-full">
                <img
                  src="/imgs/badges/phdaily.svg"
                  alt="phdaily"
                  className="h-8 object-cover"
                />
              </div>
            </div>
          )}
          <div className="text-center">
            {hero.announcement && (
              <Link
                href={hero.announcement.url as any}
                className="mx-auto mb-6 inline-flex items-center gap-3 glass-card rounded-full px-4 py-2 text-sm hover:bg-white/10 dark:hover:bg-white/5 transition-all duration-300 group"
              >
                {hero.announcement.label && (
                  <Badge className="bg-primary/20 text-primary border-primary/30">
                    <Sparkles className="w-3 h-3 mr-1" />
                    {hero.announcement.label}
                  </Badge>
                )}
                <span className="group-hover:text-primary transition-colors">
                  {hero.announcement.title}
                </span>
                <Zap className="w-4 h-4 text-primary group-hover:scale-110 transition-transform" />
              </Link>
            )}

            {texts && texts.length > 1 ? (
              <h1 className="mx-auto mb-6 mt-4 max-w-6xl text-balance text-4xl font-bold lg:mb-8 lg:text-7xl leading-tight">
                {texts[0]}
                <span className="bg-gradient-to-r from-primary via-neon-purple to-neon-cyan bg-clip-text text-transparent animate-pulse">
                  {highlightText}
                </span>
                {texts[1]}
              </h1>
            ) : (
              <h1 className="mx-auto mb-6 mt-4 max-w-6xl text-balance text-4xl font-bold lg:mb-8 lg:text-7xl leading-tight">
                <span className="bg-gradient-to-r from-foreground via-primary to-foreground bg-clip-text text-transparent">
                  {hero.title}
                </span>
              </h1>
            )}

            <p
              className="mx-auto max-w-3xl text-muted-foreground lg:text-xl leading-relaxed"
              dangerouslySetInnerHTML={{ __html: hero.description || "" }}
            />
            {hero.buttons && (
              <div className="mt-10 flex flex-col justify-center gap-4 sm:flex-row">
                {hero.buttons.map((item, i) => {
                  const isPrimary = i === 0;
                  return (
                    <Link
                      key={i}
                      href={item.url as any}
                      target={item.target || ""}
                      className="flex items-center"
                    >
                      <TechButton
                        className="w-full min-w-[200px]"
                        size="xl"
                        variant={isPrimary ? "neon" : item.variant === "outline" ? "glass" : "default"}
                        pulse={isPrimary}
                      >
                        {item.icon ? (
                          <Icon name={item.icon} className="w-5 h-5" />
                        ) : isPrimary ? (
                          <Music className="w-5 h-5" />
                        ) : null}
                        {item.title}
                      </TechButton>
                    </Link>
                  );
                })}
              </div>
            )}
            {hero.tip && (
              <div className="mt-8 glass-card inline-block px-6 py-3 rounded-full">
                <p className="text-sm text-muted-foreground flex items-center gap-2">
                  <Sparkles className="w-4 h-4 text-primary" />
                  {hero.tip}
                </p>
              </div>
            )}
            {hero.show_happy_users && (
              <div className="mt-12">
                <HappyUsers />
              </div>
            )}
          </div>
        </div>
      </section>
    </>
  );
}
