"use client";

import { Button } from "@/components/ui/button";
import { useAppContext } from "@/contexts/app";
import { useTranslations } from "next-intl";

export default function SignIn() {
  const t = useTranslations("user");
  const { setShowSignModal } = useAppContext();

  return (
    <Button
      variant="default"
      onClick={() => setShowSignModal(true)}
      className="cursor-pointer"
    >
      {t("sign_in")}
    </Button>
  );
}
