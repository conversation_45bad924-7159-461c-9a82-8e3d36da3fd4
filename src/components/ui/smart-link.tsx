"use client";

import React from "react";
import { Link } from "@/i18n/navigation";

interface SmartLinkProps {
  href: string;
  target?: string;
  className?: string;
  children: React.ReactNode;
  onClick?: () => void;
}

export default function SmartLink({
  href,
  target,
  className,
  children,
  onClick
}: SmartLinkProps) {
  const isAnchorLink = href.startsWith('#') || href.includes('/#');
  const isExternalLink = target === '_blank' || href.startsWith('http') || href.startsWith('mailto:');

  const scrollToElement = (anchorId: string) => {
    const element = document.getElementById(anchorId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      if (window.location.hash !== `#${anchorId}`) {
        window.history.pushState(null, '', `#${anchorId}`);
      }
      return true;
    }
    return false;
  };

  const handleAnchorClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();

    const anchorId = href.startsWith('#') ? href.substring(1) : href.split('/#')[1];
    if (!anchorId) return;

    // 立即尝试滚动，如果失败则延迟重试
    if (!scrollToElement(anchorId)) {
      setTimeout(() => scrollToElement(anchorId), 100);
    }
  };

  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    onClick?.();

    if (isAnchorLink) {
      handleAnchorClick(e);
    }
  };

  // 外部链接和锚点链接使用原生 a 标签
  if (isExternalLink || isAnchorLink) {
    return (
      <a
        href={href}
        target={target}
        className={className}
        onClick={handleClick}
      >
        {children}
      </a>
    );
  }

  // 内部路由链接使用 Next.js Link
  return (
    <Link href={href as any} className={className}>
      {children}
    </Link>
  );
}
