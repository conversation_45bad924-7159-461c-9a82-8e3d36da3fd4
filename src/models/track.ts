import { tracks } from "@/db/schema";
import { db } from "@/db";
import { desc, eq, and, or, like, gte, lte, inArray, sql } from "drizzle-orm";
import { Track, TrackSearchParams } from "@/types/music";

// Insert new track
export async function insertTrack(
  data: typeof tracks.$inferInsert
): Promise<typeof tracks.$inferSelect | undefined> {
  const [track] = await db()
    .insert(tracks)
    .values(data)
    .returning();

  return track;
}

// Find track by UUID
export async function findTrackByUuid(
  uuid: string
): Promise<typeof tracks.$inferSelect | undefined> {
  const [track] = await db()
    .select()
    .from(tracks)
    .where(eq(tracks.uuid, uuid))
    .limit(1);

  return track;
}

// Find track by slug
export async function findTrackBySlug(
  slug: string
): Promise<typeof tracks.$inferSelect | undefined> {
  const [track] = await db()
    .select()
    .from(tracks)
    .where(eq(tracks.slug, slug))
    .limit(1);

  return track;
}

// Find track by generation UUID
export async function findTrackByGenerationUuid(
  generation_uuid: string
): Promise<typeof tracks.$inferSelect | undefined> {
  const [track] = await db()
    .select()
    .from(tracks)
    .where(eq(tracks.generation_uuid, generation_uuid))
    .limit(1);

  return track;
}

// 更新音轨文件信息
export async function updateTrackFileInfo(
  track_uuid: string,
  fileInfo: {
    file_url?: string;
    file_path?: string;
    file_size?: number;
    file_format?: string;
    original_file_url?: string;
  }
): Promise<typeof tracks.$inferSelect | undefined> {
  const [updatedTrack] = await db()
    .update(tracks)
    .set({
      ...fileInfo,
      updated_at: new Date(),
    })
    .where(eq(tracks.uuid, track_uuid))
    .returning();

  return updatedTrack;
}

// Update track
export async function updateTrack(
  uuid: string,
  data: Partial<typeof tracks.$inferInsert>
): Promise<typeof tracks.$inferSelect | undefined> {
  const [track] = await db()
    .update(tracks)
    .set({
      ...data,
      updated_at: new Date(),
    })
    .where(eq(tracks.uuid, uuid))
    .returning();

  return track;
}

// Increment download count
export async function incrementTrackDownloadCount(
  uuid: string
): Promise<typeof tracks.$inferSelect | undefined> {
  const [track] = await db()
    .update(tracks)
    .set({
      download_count: sql`${tracks.download_count} + 1`,
      updated_at: new Date(),
    })
    .where(eq(tracks.uuid, uuid))
    .returning();

  return track;
}

// Get user's tracks
export async function getUserTracks(
  user_uuid: string,
  limit: number = 20,
  offset: number = 0
): Promise<typeof tracks.$inferSelect[]> {
  const userTracks = await db()
    .select()
    .from(tracks)
    .where(eq(tracks.user_uuid, user_uuid))
    .orderBy(desc(tracks.created_at))
    .limit(limit)
    .offset(offset);

  return userTracks;
}

// Get public tracks
export async function getPublicTracks(
  limit: number = 20,
  offset: number = 0
): Promise<typeof tracks.$inferSelect[]> {
  const publicTracks = await db()
    .select()
    .from(tracks)
    .where(eq(tracks.is_public, true))
    .orderBy(desc(tracks.created_at))
    .limit(limit)
    .offset(offset);

  return publicTracks;
}

// Search tracks with filters
export async function searchTracks(
  params: TrackSearchParams
): Promise<{
  tracks: typeof tracks.$inferSelect[];
  total: number;
}> {
  const {
    query,
    style,
    mood,
    bpm_min,
    bpm_max,
    duration,
    user_uuid,
    is_public,
    sort_by = "created_at",
    sort_order = "desc",
    page = 1,
    per_page = 20,
  } = params;

  let whereConditions = [];

  // Text search in title and prompt
  if (query) {
    whereConditions.push(
      or(
        like(tracks.title, `%${query}%`),
        like(tracks.prompt, `%${query}%`)
      )
    );
  }

  // Style filter
  if (style) {
    whereConditions.push(eq(tracks.style, style));
  }

  // Mood filter
  if (mood) {
    whereConditions.push(eq(tracks.mood, mood));
  }

  // BPM range filter
  if (bpm_min !== undefined) {
    whereConditions.push(gte(tracks.bpm, bpm_min));
  }
  if (bpm_max !== undefined) {
    whereConditions.push(lte(tracks.bpm, bpm_max));
  }

  // Duration filter
  if (duration) {
    whereConditions.push(eq(tracks.duration, duration));
  }

  // User filter
  if (user_uuid) {
    whereConditions.push(eq(tracks.user_uuid, user_uuid));
  }

  // Public filter
  if (is_public !== undefined) {
    whereConditions.push(eq(tracks.is_public, is_public));
  }

  const whereClause = whereConditions.length > 0 
    ? and(...whereConditions) 
    : undefined;

  // Count total results
  const countResult = await db()
    .select({ count: sql`count(*)` })
    .from(tracks)
    .where(whereClause);

  const total = Number(countResult[0]?.count || 0);

  // Get tracks with pagination
  const offset = (page - 1) * per_page;
  
  let orderBy;
  switch (sort_by) {
    case "title":
      orderBy = sort_order === "asc" ? tracks.title : desc(tracks.title);
      break;
    case "download_count":
      orderBy = sort_order === "asc" ? tracks.download_count : desc(tracks.download_count);
      break;
    case "bpm":
      orderBy = sort_order === "asc" ? tracks.bpm : desc(tracks.bpm);
      break;
    default:
      orderBy = sort_order === "asc" ? tracks.created_at : desc(tracks.created_at);
  }

  const trackResults = await db()
    .select()
    .from(tracks)
    .where(whereClause)
    .orderBy(orderBy)
    .limit(per_page)
    .offset(offset);

  return {
    tracks: trackResults,
    total,
  };
}

// Get popular tracks
export async function getPopularTracks(
  limit: number = 10
): Promise<typeof tracks.$inferSelect[]> {
  const popularTracks = await db()
    .select()
    .from(tracks)
    .where(eq(tracks.is_public, true))
    .orderBy(desc(tracks.download_count))
    .limit(limit);

  return popularTracks;
}

// Get recent tracks
export async function getRecentTracks(
  limit: number = 10
): Promise<typeof tracks.$inferSelect[]> {
  const recentTracks = await db()
    .select()
    .from(tracks)
    .where(eq(tracks.is_public, true))
    .orderBy(desc(tracks.created_at))
    .limit(limit);

  return recentTracks;
}

// Get tracks by style
export async function getTracksByStyle(
  style: string,
  limit: number = 20
): Promise<typeof tracks.$inferSelect[]> {
  const styleTracks = await db()
    .select()
    .from(tracks)
    .where(
      and(
        eq(tracks.style, style),
        eq(tracks.is_public, true)
      )
    )
    .orderBy(desc(tracks.created_at))
    .limit(limit);

  return styleTracks;
}

// Get available styles
export async function getAvailableStyles(): Promise<string[]> {
  const result = await db()
    .selectDistinct({ style: tracks.style })
    .from(tracks)
    .where(
      and(
        eq(tracks.is_public, true),
        sql`${tracks.style} IS NOT NULL`
      )
    );

  return result.map(r => r.style).filter((style): style is string => Boolean(style));
}

// Get available moods
export async function getAvailableMoods(): Promise<string[]> {
  const result = await db()
    .selectDistinct({ mood: tracks.mood })
    .from(tracks)
    .where(
      and(
        eq(tracks.is_public, true),
        sql`${tracks.mood} IS NOT NULL`
      )
    );

  return result.map(r => r.mood).filter((mood): mood is string => Boolean(mood));
}

// Delete track
export async function deleteTrack(uuid: string): Promise<boolean> {
  const result = await db()
    .delete(tracks)
    .where(eq(tracks.uuid, uuid));

  return result.length > 0;
}

// Get track statistics
export async function getTrackStats(user_uuid?: string): Promise<{
  total: number;
  public: number;
  private: number;
  total_downloads: number;
}> {
  const trackResults = user_uuid
    ? await db().select().from(tracks).where(eq(tracks.user_uuid, user_uuid))
    : await db().select().from(tracks);

  const stats = {
    total: trackResults.length,
    public: 0,
    private: 0,
    total_downloads: 0,
  };

  trackResults.forEach((track) => {
    if (track.is_public) {
      stats.public++;
    } else {
      stats.private++;
    }
    stats.total_downloads += track.download_count;
  });

  return stats;
}
