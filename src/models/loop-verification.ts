import { loopVerifications } from "@/db/schema";
import { db } from "@/db";
import { desc, eq, and, gte, lte } from "drizzle-orm";


// Insert new loop verification
export async function insertLoopVerification(
  data: typeof loopVerifications.$inferInsert
): Promise<typeof loopVerifications.$inferSelect | undefined> {
  const [verification] = await db()
    .insert(loopVerifications)
    .values(data)
    .returning();

  return verification;
}

// Find loop verification by track UUID
export async function findLoopVerificationByTrackUuid(
  track_uuid: string
): Promise<typeof loopVerifications.$inferSelect | undefined> {
  const [verification] = await db()
    .select()
    .from(loopVerifications)
    .where(eq(loopVerifications.track_uuid, track_uuid))
    .orderBy(desc(loopVerifications.created_at))
    .limit(1);

  return verification;
}

// Get all verifications for a track
export async function getTrackVerifications(
  track_uuid: string
): Promise<typeof loopVerifications.$inferSelect[]> {
  const verifications = await db()
    .select()
    .from(loopVerifications)
    .where(eq(loopVerifications.track_uuid, track_uuid))
    .orderBy(desc(loopVerifications.created_at));

  return verifications;
}

// Get seamless tracks
export async function getSeamlessTracks(
  limit: number = 100
): Promise<typeof loopVerifications.$inferSelect[]> {
  const seamlessVerifications = await db()
    .select()
    .from(loopVerifications)
    .where(eq(loopVerifications.is_seamless, true))
    .orderBy(desc(loopVerifications.verification_score))
    .limit(limit);

  return seamlessVerifications;
}

// Get verifications by score range
export async function getVerificationsByScoreRange(
  min_score: number,
  max_score: number = 1.0
): Promise<typeof loopVerifications.$inferSelect[]> {
  const verifications = await db()
    .select()
    .from(loopVerifications)
    .where(
      and(
        gte(loopVerifications.verification_score, min_score.toString()),
        lte(loopVerifications.verification_score, max_score.toString())
      )
    )
    .orderBy(desc(loopVerifications.verification_score));

  return verifications;
}

// Get verifications by method
export async function getVerificationsByMethod(
  method: "ml" | "signal_processing"
): Promise<typeof loopVerifications.$inferSelect[]> {
  const verifications = await db()
    .select()
    .from(loopVerifications)
    .where(eq(loopVerifications.verification_method, method))
    .orderBy(desc(loopVerifications.created_at));

  return verifications;
}

// Update verification result
export async function updateLoopVerification(
  id: number,
  data: Partial<typeof loopVerifications.$inferInsert>
): Promise<typeof loopVerifications.$inferSelect | undefined> {
  const [verification] = await db()
    .update(loopVerifications)
    .set(data)
    .where(eq(loopVerifications.id, id))
    .returning();

  return verification;
}

// Delete loop verification
export async function deleteLoopVerification(id: number): Promise<boolean> {
  const result = await db()
    .delete(loopVerifications)
    .where(eq(loopVerifications.id, id));

  return result.length > 0;
}

// Delete verifications by track UUID
export async function deleteTrackVerifications(
  track_uuid: string
): Promise<boolean> {
  const result = await db()
    .delete(loopVerifications)
    .where(eq(loopVerifications.track_uuid, track_uuid));

  return result.length > 0;
}

// Get verification statistics
export async function getVerificationStats(): Promise<{
  total: number;
  seamless: number;
  non_seamless: number;
  average_score: number;
  ml_verifications: number;
  signal_processing_verifications: number;
}> {
  const verifications = await db()
    .select()
    .from(loopVerifications);

  const stats = {
    total: verifications.length,
    seamless: 0,
    non_seamless: 0,
    average_score: 0,
    ml_verifications: 0,
    signal_processing_verifications: 0,
  };

  let totalScore = 0;

  verifications.forEach((verification) => {
    if (verification.is_seamless) {
      stats.seamless++;
    } else {
      stats.non_seamless++;
    }

    if (verification.verification_score) {
      totalScore += Number(verification.verification_score);
    }

    if (verification.verification_method === "ml") {
      stats.ml_verifications++;
    } else if (verification.verification_method === "signal_processing") {
      stats.signal_processing_verifications++;
    }
  });

  if (stats.total > 0) {
    stats.average_score = totalScore / stats.total;
  }

  return stats;
}

// Get recent verifications
export async function getRecentVerifications(
  limit: number = 20
): Promise<typeof loopVerifications.$inferSelect[]> {
  const recentVerifications = await db()
    .select()
    .from(loopVerifications)
    .orderBy(desc(loopVerifications.created_at))
    .limit(limit);

  return recentVerifications;
}

// Check if track needs verification
export async function trackNeedsVerification(
  track_uuid: string,
  max_age_hours: number = 24
): Promise<boolean> {
  const cutoffTime = new Date(Date.now() - max_age_hours * 60 * 60 * 1000);
  
  const [recentVerification] = await db()
    .select()
    .from(loopVerifications)
    .where(
      and(
        eq(loopVerifications.track_uuid, track_uuid),
        gte(loopVerifications.created_at, cutoffTime)
      )
    )
    .limit(1);

  return !recentVerification;
}
