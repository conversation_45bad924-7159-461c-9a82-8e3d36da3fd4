"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { useFavorite } from "@/hooks/use-favorite";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Heart } from "lucide-react";

// Prevent SSR for this test page
import dynamic from "next/dynamic";

function TestFavoritePage() {
  const { data: session } = useSession();
  const [testTrackUuid, setTestTrackUuid] = useState("");
  const { isLiked, isLoading, toggleFavorite } = useFavorite(testTrackUuid);

  const handleTest = async () => {
    if (!testTrackUuid) {
      alert("Please enter a track UUID");
      return;
    }
    
    console.log("Testing favorite for track:", testTrackUuid);
    await toggleFavorite();
  };

  const handleDebugTest = async () => {
    try {
      const response = await fetch("/api/debug/favorite-test");
      const data = await response.json();
      console.log("Debug test result:", data);
      alert(JSON.stringify(data, null, 2));
    } catch (error) {
      console.error("Debug test error:", error);
      alert("Debug test failed: " + error);
    }
  };

  return (
    <div className="container mx-auto p-8">
      <Card className="p-6 max-w-md mx-auto">
        <h1 className="text-2xl font-bold mb-4">Favorite Function Test</h1>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              Session Status:
            </label>
            <p className="text-sm text-muted-foreground">
              {session ? `Logged in as ${session.user?.email}` : "Not logged in"}
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              Track UUID:
            </label>
            <input
              type="text"
              value={testTrackUuid}
              onChange={(e) => setTestTrackUuid(e.target.value)}
              placeholder="Enter track UUID to test"
              className="w-full p-2 border rounded"
            />
          </div>

          <div className="flex items-center gap-2">
            <Button
              onClick={handleTest}
              disabled={isLoading || !testTrackUuid}
              className="flex items-center gap-2"
            >
              <Heart className={`h-4 w-4 ${isLiked ? "fill-current text-red-500" : ""}`} />
              {isLoading ? "Loading..." : isLiked ? "Remove Favorite" : "Add Favorite"}
            </Button>
          </div>

          <div>
            <p className="text-sm">
              Status: {isLiked ? "Favorited" : "Not favorited"}
            </p>
          </div>

          <Button onClick={handleDebugTest} variant="outline">
            Run Debug Test
          </Button>
        </div>
      </Card>
    </div>
  );
}

// Export with dynamic import to prevent SSR
export default dynamic(() => Promise.resolve(TestFavoritePage), {
  ssr: false,
  loading: () => <div className="container mx-auto p-8">Loading test page...</div>
});