import { ImageResponse } from "next/og";

export const runtime = "edge";

export async function GET() {
  try {
    return new ImageResponse(
      (
        <div
          style={{
            height: "100%",
            width: "100%",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            backgroundColor: "#0a0a0a",
            backgroundImage: "linear-gradient(45deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)",
            fontFamily: "system-ui, sans-serif",
          }}
        >
          {/* Logo and Title */}
          <div
            style={{
              display: "flex",
              alignItems: "center",
              marginBottom: "40px",
            }}
          >
            <div
              style={{
                fontSize: "72px",
                fontWeight: "bold",
                color: "#ffffff",
                marginRight: "20px",
              }}
            >
              🎵
            </div>
            <div
              style={{
                fontSize: "64px",
                fontWeight: "bold",
                color: "#ffffff",
              }}
            >
              My Music Library
            </div>
          </div>

          {/* Subtitle */}
          <div
            style={{
              fontSize: "32px",
              color: "#e2e8f0",
              textAlign: "center",
              marginBottom: "30px",
              maxWidth: "800px",
            }}
          >
            Your Personal Collection of AI-Generated Music Loops
          </div>

          {/* Features */}
          <div
            style={{
              display: "flex",
              gap: "40px",
              fontSize: "20px",
              color: "#94a3b8",
            }}
          >
            <div style={{ display: "flex", alignItems: "center" }}>
              📚 Organize Tracks
            </div>
            <div style={{ display: "flex", alignItems: "center" }}>
              ⬇️ Download Music
            </div>
            <div style={{ display: "flex", alignItems: "center" }}>
              🎧 Preview & Play
            </div>
          </div>

          {/* Bottom accent */}
          <div
            style={{
              position: "absolute",
              bottom: "0",
              left: "0",
              right: "0",
              height: "8px",
              background: "linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899)",
            }}
          />
        </div>
      ),
      {
        width: 1200,
        height: 630,
      }
    );
  } catch (error) {
    console.error("Error generating OG image:", error);
    return new Response("Failed to generate image", { status: 500 });
  }
}