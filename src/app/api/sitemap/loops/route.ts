import { NextResponse } from "next/server";
import { SitemapIndexGenerator } from "@/services/sitemap-index-generator";
import { URLBuilder } from "@/services/sitemap-url-builder";
import { XMLSerializer } from "@/services/sitemap-xml-serializer";
import { SitemapCacheManager, MemorySitemapCache } from "@/services/sitemap-cache";
import { SitemapErrorHandler } from "@/services/sitemap-error-handler";
import { SITEMAP_CONFIG, SITEMAP_HEADERS } from "@/constants/sitemap";
import { extractLocaleFromPath } from "@/lib/sitemap-utils";

// Initialize sitemap components
const urlBuilder = new URLBuilder({
  baseUrl: SITEMAP_CONFIG.baseUrl,
  locales: SITEMAP_CONFIG.locales,
  defaultLocale: SITEMAP_CONFIG.defaultLocale,
  localePrefix: "as-needed",
});

const xmlSerializer = new XMLSerializer({
  includeHreflang: SITEMAP_CONFIG.enableHreflang,
  prettyPrint: process.env.NODE_ENV === "development",
  validateXML: true,
});

const cacheManager = new SitemapCacheManager(new MemorySitemapCache());
const sitemapGenerator = new SitemapIndexGenerator(urlBuilder, xmlSerializer, cacheManager);
const errorHandler = new SitemapErrorHandler(cacheManager, xmlSerializer, urlBuilder);

export async function GET(request: Request) {
  const startTime = Date.now();

  try {
    // Extract locale from request URL or use default
    const url = new URL(request.url);
    const locale = extractLocaleFromPath(url.pathname, SITEMAP_CONFIG.locales) || SITEMAP_CONFIG.defaultLocale;

    // Log request for monitoring
    console.log(`[Sitemap Loops] Generating music loops sitemap for locale: ${locale}`);

    // Check if we should use cached version based on request headers
    const ifModifiedSince = request.headers.get('if-modified-since');
    if (ifModifiedSince) {
      const lastModified = await getLastModifiedDate();
      const ifModifiedSinceDate = new Date(ifModifiedSince);

      if (lastModified <= ifModifiedSinceDate) {
        return new NextResponse(null, {
          status: 304,
          headers: {
            "Last-Modified": lastModified.toUTCString(),
            "Cache-Control": `public, max-age=${SITEMAP_CONFIG.cacheTimeout}`,
          },
        });
      }
    }

    // Generate music loops sitemap
    const result = await sitemapGenerator.generateContentTypeSitemap('loops', locale);

    if (!result.isValid) {
      console.error("[Sitemap Loops] Generated invalid XML:", result.validationErrors);
      throw new Error(`Generated sitemap XML is invalid: ${result.validationErrors?.join(", ")}`);
    }

    // Log generation metrics
    const totalTime = Date.now() - startTime;
    console.log(`[Sitemap Loops] Generated successfully in ${totalTime}ms with ${result.urlCount} URLs`);

    // Return XML response with proper headers
    return new NextResponse(result.xml, {
      status: 200,
      headers: {
        "Content-Type": SITEMAP_HEADERS.CONTENT_TYPE,
        "Cache-Control": `public, max-age=${SITEMAP_CONFIG.cacheTimeout}, s-maxage=${SITEMAP_CONFIG.cacheTimeout}`,
        "X-Sitemap-Type": "loops",
        "X-Sitemap-URLs": result.urlCount.toString(),
        "X-Generation-Time": totalTime.toString(),
        "X-Locale": locale,
        "Vary": SITEMAP_HEADERS.VARY,
        "Last-Modified": new Date().toUTCString(),
      },
    });
  } catch (error) {
    const totalTime = Date.now() - startTime;

    // Log error with context
    console.error("[Sitemap Loops] Generation failed:", {
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
      generationTime: totalTime,
      url: request.url,
    });

    // Try to return cached version as fallback
    try {
      const url = new URL(request.url);
      const locale = extractLocaleFromPath(url.pathname, SITEMAP_CONFIG.locales) || SITEMAP_CONFIG.defaultLocale;
      const fallbackResult = await getFallbackSitemap(locale);
      if (fallbackResult) {
        console.log("[Sitemap Loops] Returning fallback cached sitemap");
        return new NextResponse(fallbackResult, {
          status: 200,
          headers: {
            "Content-Type": SITEMAP_HEADERS.CONTENT_TYPE,
            "Cache-Control": "public, max-age=300, s-maxage=300",
            "X-Sitemap-Fallback": "true",
            "X-Generation-Time": totalTime.toString(),
            "Vary": SITEMAP_HEADERS.VARY,
          },
        });
      }
    } catch (fallbackError) {
      console.error("[Sitemap Loops] Fallback also failed:", fallbackError);
    }

    // Return empty sitemap for loops if no fallback available
    const emptySitemap = generateEmptyLoopsSitemap();
    return new NextResponse(emptySitemap, {
      status: 200,
      headers: {
        "Content-Type": SITEMAP_HEADERS.CONTENT_TYPE,
        "Cache-Control": "public, max-age=300, s-maxage=300",
        "X-Sitemap-Empty": "true",
        "X-Generation-Time": totalTime.toString(),
        "Vary": SITEMAP_HEADERS.VARY,
      },
    });
  }
}



/**
 * Gets the last modified date for music loops
 */
async function getLastModifiedDate(): Promise<Date> {
  try {
    const generator = sitemapGenerator.getContentGenerator('loops');
    if (generator) {
      return await generator.getLastModified();
    }
    return new Date();
  } catch (error) {
    console.error("[Sitemap Loops] Error getting last modified date:", error);
    return new Date();
  }
}

/**
 * Attempts to get a fallback sitemap from cache
 */
async function getFallbackSitemap(locale: string): Promise<string | null> {
  try {
    const cached = await cacheManager.getCachedSitemap('loops', locale);
    return cached;
  } catch (error) {
    console.error("[Sitemap Loops] Error getting fallback sitemap:", error);
    return null;
  }
}

/**
 * Generates an empty sitemap for loops when no content is available
 */
function generateEmptyLoopsSitemap(): string {
  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
</urlset>`;
}

/**
 * Health check endpoint for music loops sitemap
 */
export async function HEAD() {
  try {
    const generator = sitemapGenerator.getContentGenerator('loops');
    if (!generator) {
      throw new Error('Music loops generator not found');
    }

    const [estimatedCount, lastModified] = await Promise.all([
      generator.getEstimatedCount(),
      generator.getLastModified(),
    ]);

    return new NextResponse(null, {
      status: 200,
      headers: {
        "X-Sitemap-Health": "ok",
        "X-Content-Type": "loops",
        "X-Estimated-URLs": estimatedCount.toString(),
        "X-Last-Modified": lastModified.toISOString(),
        "X-Cache-Hit-Rate": cacheManager.getStats().hitRate.toString(),
      },
    });
  } catch (error) {
    return new NextResponse(null, {
      status: 503,
      headers: {
        "X-Sitemap-Health": "error",
        "X-Content-Type": "loops",
        "X-Error": error instanceof Error ? error.message : "Unknown error",
      },
    });
  }
}