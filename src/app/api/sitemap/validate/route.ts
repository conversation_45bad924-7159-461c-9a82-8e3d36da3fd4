import { NextResponse } from "next/server";
import { SitemapValidator } from "@/services/sitemap-validator";
import { SitemapQualityAssurance } from "@/services/sitemap-quality-assurance";
import { SitemapIndexGenerator } from "@/services/sitemap-index-generator";
import { URLBuilder } from "@/services/sitemap-url-builder";
import { XMLSerializer } from "@/services/sitemap-xml-serializer";
import { SitemapCacheManager, MemorySitemapCache } from "@/services/sitemap-cache";
import { SITEMAP_CONFIG, SITEMAP_HEADERS } from "@/constants/sitemap";
import type { SitemapContentType, SitemapEntry } from "@/types/sitemap";

// Initialize components
const urlBuilder = new URLBuilder({
  baseUrl: SITEMAP_CONFIG.baseUrl,
  locales: SITEMAP_CONFIG.locales,
  defaultLocale: SITEMAP_CONFIG.defaultLocale,
  localePrefix: "as-needed",
});

const xmlSerializer = new XMLSerializer({
  includeHreflang: SITEMAP_CONFIG.enableHreflang,
  prettyPrint: process.env.NODE_ENV === "development",
  validateXML: true,
});

const cacheManager = new SitemapCacheManager(new MemorySitemapCache());
const sitemapGenerator = new SitemapIndexGenerator(urlBuilder, xmlSerializer, cacheManager);
const validator = new SitemapValidator();
const qualityAssurance = new SitemapQualityAssurance(validator);

export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const contentType = (url.searchParams.get("type") as SitemapContentType) || "static";
    const locale = url.searchParams.get("locale") || SITEMAP_CONFIG.defaultLocale;
    const checkAccessibility = url.searchParams.get("accessibility") === "true";
    const maxUrlsToCheck = parseInt(url.searchParams.get("maxUrls") || "50");
    const format = url.searchParams.get("format") || "json";

    console.log(`[Sitemap Validation] Validating ${contentType} sitemap for locale: ${locale}`);

    // Generate sitemap for validation
    let xmlResult;
    let entries: SitemapEntry[];

    if (contentType === "index") {
      xmlResult = await sitemapGenerator.generateSitemap(locale);
      entries = []; // Index doesn't have individual URL entries
    } else {
      xmlResult = await sitemapGenerator.generateContentTypeSitemap(contentType, locale);
      const generator = sitemapGenerator.getContentGenerator(contentType);
      entries = generator ? await generator.generate(locale) : [];
    }

    // Perform validation
    const validationResult = await validator.validateSitemap(xmlResult, entries, {
      checkAccessibility,
      maxUrlsToCheck,
    });

    // Perform quality assurance if requested
    let qaReport;
    if (url.searchParams.get("qa") === "true") {
      qaReport = await qualityAssurance.performQualityAssurance(
        xmlResult,
        entries,
        contentType,
        { checkAccessibility, maxUrlsToCheck }
      );
    }

    // Return different formats
    switch (format) {
      case "summary":
        if (!qaReport) {
          return NextResponse.json(
            { error: "Quality assurance report required for summary format. Add ?qa=true" },
            { status: 400 }
          );
        }
        
        const summaryText = qualityAssurance.generateSummaryReport(qaReport);
        return new NextResponse(summaryText, {
          status: 200,
          headers: {
            "Content-Type": "text/plain; charset=utf-8",
            "Cache-Control": "no-cache",
          },
        });

      case "report":
        if (!qaReport) {
          return NextResponse.json(
            { error: "Quality assurance report required for report format. Add ?qa=true" },
            { status: 400 }
          );
        }
        
        return NextResponse.json(qaReport, {
          status: qaReport.overall.status === "critical" ? 422 : 200,
          headers: {
            "Cache-Control": "no-cache",
          },
        });

      case "validation":
        return NextResponse.json(validationResult, {
          status: validationResult.isValid ? 200 : 422,
          headers: {
            "Cache-Control": "no-cache",
          },
        });

      default:
        // Default: return comprehensive validation info
        const response = {
          validation: validationResult,
          qualityAssurance: qaReport,
          metadata: {
            contentType,
            locale,
            checkAccessibility,
            maxUrlsToCheck,
            timestamp: new Date().toISOString(),
          },
        };

        return NextResponse.json(response, {
          status: validationResult.isValid ? 200 : 422,
          headers: {
            "Cache-Control": "no-cache",
          },
        });
    }
  } catch (error) {
    console.error("[Sitemap Validation] Error:", error);
    
    return NextResponse.json(
      {
        error: "Validation failed",
        message: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      },
      {
        status: 500,
        headers: {
          "Cache-Control": "no-cache",
        },
      }
    );
  }
}

// POST endpoint for validating external sitemaps
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { xml, entries, options = {} } = body;

    if (!xml) {
      return NextResponse.json(
        { error: "XML content is required" },
        { status: 400 }
      );
    }

    // Create mock XML result for validation
    const xmlResult = {
      xml,
      urlCount: entries?.length || 0,
      isValid: true,
      generationTime: 0,
    };

    // Perform validation
    const validationResult = await validator.validateSitemap(
      xmlResult,
      entries || [],
      options
    );

    return NextResponse.json(validationResult, {
      status: validationResult.isValid ? 200 : 422,
      headers: {
        "Cache-Control": "no-cache",
      },
    });
  } catch (error) {
    console.error("[Sitemap Validation] POST Error:", error);
    
    return NextResponse.json(
      {
        error: "Validation failed",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// PUT endpoint for updating validator configuration
export async function PUT(request: Request) {
  try {
    const body = await request.json();
    const { config } = body;

    if (!config) {
      return NextResponse.json(
        { error: "Configuration is required" },
        { status: 400 }
      );
    }

    // Update validator configuration
    validator.updateConfig(config);
    qualityAssurance.updateValidatorConfig(config);

    return NextResponse.json({
      success: true,
      message: "Validator configuration updated",
      config: validator.getConfig(),
    });
  } catch (error) {
    console.error("[Sitemap Validation] PUT Error:", error);
    
    return NextResponse.json(
      {
        error: "Configuration update failed",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}