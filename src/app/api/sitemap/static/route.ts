import { NextResponse } from "next/server";
import { SitemapIndexGenerator } from "@/services/sitemap-index-generator";
import { URLBuilder } from "@/services/sitemap-url-builder";
import { XMLSerializer } from "@/services/sitemap-xml-serializer";
import { SitemapCacheManager, MemorySitemapCache } from "@/services/sitemap-cache";
import { SitemapErrorHandler } from "@/services/sitemap-error-handler";
import { SITEMAP_CONFIG, SITEMAP_HEADERS } from "@/constants/sitemap";
import { extractLocaleFromPath } from "@/lib/sitemap-utils";

// Force static generation for this route
export const dynamic = 'force-static';

// Initialize sitemap components
const urlBuilder = new URLBuilder({
  baseUrl: SITEMAP_CONFIG.baseUrl,
  locales: SITEMAP_CONFIG.locales,
  defaultLocale: SITEMAP_CONFIG.defaultLocale,
  localePrefix: "as-needed",
});

const xmlSerializer = new XMLSerializer({
  includeHreflang: SITEMAP_CONFIG.enableHreflang,
  prettyPrint: process.env.NODE_ENV === "development",
  validateXML: true,
});

const cacheManager = new SitemapCacheManager(new MemorySitemapCache());
const sitemapGenerator = new SitemapIndexGenerator(urlBuilder, xmlSerializer, cacheManager);
const errorHandler = new SitemapErrorHandler(cacheManager, xmlSerializer, urlBuilder);

export async function GET() {
  const startTime = Date.now();
  
  try {
    // Use default locale for static generation
    const locale = SITEMAP_CONFIG.defaultLocale;
    
    // Log request for monitoring
    console.log(`[Sitemap Static] Generating static pages sitemap for locale: ${locale}`);
    
    // Skip conditional requests for static generation
    
    // Generate static pages sitemap
    const result = await sitemapGenerator.generateContentTypeSitemap('static', locale);
    
    if (!result.isValid) {
      console.error("[Sitemap Static] Generated invalid XML:", result.validationErrors);
      throw new Error(`Generated sitemap XML is invalid: ${result.validationErrors?.join(", ")}`);
    }

    // Log generation metrics
    const totalTime = Date.now() - startTime;
    console.log(`[Sitemap Static] Generated successfully in ${totalTime}ms with ${result.urlCount} URLs`);

    // Return XML response with proper headers
    return new NextResponse(result.xml, {
      status: 200,
      headers: {
        "Content-Type": SITEMAP_HEADERS.CONTENT_TYPE,
        "Cache-Control": `public, max-age=${SITEMAP_CONFIG.cacheTimeout}, s-maxage=${SITEMAP_CONFIG.cacheTimeout}`,
        "X-Sitemap-Type": "static",
        "X-Sitemap-URLs": result.urlCount.toString(),
        "X-Generation-Time": totalTime.toString(),
        "X-Locale": locale,
        "Vary": SITEMAP_HEADERS.VARY,
        "Last-Modified": new Date().toUTCString(),
      },
    });
  } catch (error) {
    const totalTime = Date.now() - startTime;
    
    // Use error handler to recover from errors
    try {
      // Use default locale for static generation
      const locale = SITEMAP_CONFIG.defaultLocale;
      
      // Handle the error with recovery mechanisms
      const recoveryResult = await errorHandler.handleError(
        error instanceof Error ? error : new Error(String(error)),
        {
          contentType: "static",
          locale: locale
        }
      );
      
      // Return the recovered sitemap
      return new NextResponse(recoveryResult.xml, {
        status: 200,
        headers: {
          "Content-Type": SITEMAP_HEADERS.CONTENT_TYPE,
          "Cache-Control": "public, max-age=300, s-maxage=300", // Shorter cache for recovered content
          "X-Sitemap-Recovery": "true",
          "X-Sitemap-URLs": recoveryResult.urlCount.toString(),
          "X-Generation-Time": totalTime.toString(),
          "Vary": SITEMAP_HEADERS.VARY,
        },
      });
    } catch (recoveryError) {
      console.error("[Sitemap Static] Recovery also failed:", recoveryError);
      const minimalSitemap = xmlSerializer.createMinimalSitemap(SITEMAP_CONFIG.baseUrl);
      return new NextResponse(minimalSitemap, {
        status: 200,
        headers: {
          "Content-Type": SITEMAP_HEADERS.CONTENT_TYPE,
          "Cache-Control": "public, max-age=300, s-maxage=300",
          "X-Sitemap-Minimal": "true",
          "X-Generation-Time": totalTime.toString(),
          "X-Recovery-Failed": "true",
          "Vary": SITEMAP_HEADERS.VARY,
        },
      });
    }
  }
}



/**
 * Gets the last modified date for static pages
 */
async function getLastModifiedDate(): Promise<Date> {
  try {
    const generator = sitemapGenerator.getContentGenerator('static');
    if (generator) {
      return await generator.getLastModified();
    }
    return new Date();
  } catch (error) {
    console.error("[Sitemap Static] Error getting last modified date:", error);
    return new Date();
  }
}

/**
 * Attempts to get a fallback sitemap from cache
 */
async function getFallbackSitemap(): Promise<string | null> {
  try {
    const cached = await cacheManager.getCachedSitemap('static', SITEMAP_CONFIG.defaultLocale);
    return cached;
  } catch (error) {
    console.error("[Sitemap Static] Error getting fallback sitemap:", error);
    return null;
  }
}

/**
 * Health check endpoint for static pages sitemap
 */
export async function HEAD() {
  try {
    const generator = sitemapGenerator.getContentGenerator('static');
    if (!generator) {
      throw new Error('Static pages generator not found');
    }

    const [estimatedCount, lastModified] = await Promise.all([
      generator.getEstimatedCount(),
      generator.getLastModified(),
    ]);
    
    return new NextResponse(null, {
      status: 200,
      headers: {
        "X-Sitemap-Health": "ok",
        "X-Content-Type": "static",
        "X-Estimated-URLs": estimatedCount.toString(),
        "X-Last-Modified": lastModified.toISOString(),
        "X-Cache-Hit-Rate": cacheManager.getStats().hitRate.toString(),
      },
    });
  } catch (error) {
    return new NextResponse(null, {
      status: 503,
      headers: {
        "X-Sitemap-Health": "error",
        "X-Content-Type": "static",
        "X-Error": error instanceof Error ? error.message : "Unknown error",
      },
    });
  }
}