import { NextResponse } from "next/server";
import { SitemapAdmin } from "@/services/sitemap-admin";
import { SitemapIndexGenerator } from "@/services/sitemap-index-generator";
import { URLBuilder } from "@/services/sitemap-url-builder";
import { XMLSerializer } from "@/services/sitemap-xml-serializer";
import { SitemapCacheManager, MemorySitemapCache } from "@/services/sitemap-cache";
import { SitemapValidator } from "@/services/sitemap-validator";
import { SitemapQualityAssurance } from "@/services/sitemap-quality-assurance";
import { SITEMAP_CONFIG } from "@/constants/sitemap";
import type { SitemapContentType } from "@/types/sitemap";

// Initialize components
const urlBuilder = new URLBuilder({
  baseUrl: SITEMAP_CONFIG.baseUrl,
  locales: SITEMAP_CONFIG.locales,
  defaultLocale: SITEMAP_CONFIG.defaultLocale,
  localePrefix: "as-needed",
});

const xmlSerializer = new XMLSerializer({
  includeHreflang: SITEMAP_CONFIG.enableHreflang,
  prettyPrint: process.env.NODE_ENV === "development",
  validateXML: true,
});

const cacheManager = new SitemapCacheManager(new MemorySitemapCache());
const sitemapGenerator = new SitemapIndexGenerator(urlBuilder, xmlSerializer, cacheManager);
const validator = new SitemapValidator();
const qualityAssurance = new SitemapQualityAssurance(validator);
const admin = new SitemapAdmin(sitemapGenerator, cacheManager, validator, qualityAssurance);

// Authentication middleware (basic implementation)
function isAuthorized(request: Request): boolean {
  // In production, implement proper authentication
  const authHeader = request.headers.get('authorization');
  const adminKey = process.env.SITEMAP_ADMIN_KEY;
  
  if (!adminKey) {
    // If no admin key is set, allow in development
    return process.env.NODE_ENV === 'development';
  }
  
  return authHeader === `Bearer ${adminKey}`;
}

export async function GET(request: Request) {
  if (!isAuthorized(request)) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    const url = new URL(request.url);
    const action = url.searchParams.get("action") || "status";

    switch (action) {
      case "status":
        const healthCheck = await admin.performHealthCheck();
        return NextResponse.json(healthCheck, {
          status: healthCheck.overall === "critical" ? 503 : 200,
          headers: { "Cache-Control": "no-cache" },
        });

      case "statistics":
        const stats = await admin.getSystemStatistics();
        return NextResponse.json(stats, {
          headers: { "Cache-Control": "no-cache" },
        });

      case "debug":
        const includeCache = url.searchParams.get("cache") === "true";
        const includeAnalytics = url.searchParams.get("analytics") === "true";
        const includeValidation = url.searchParams.get("validation") === "true";
        const contentType = url.searchParams.get("contentType") as SitemapContentType;
        const locale = url.searchParams.get("locale") || undefined;

        const debugReport = await admin.createDebugReport({
          includeCache,
          includeAnalytics,
          includeValidation,
          contentType,
          locale,
        });

        return NextResponse.json(debugReport, {
          headers: { "Cache-Control": "no-cache" },
        });

      default:
        return NextResponse.json(
          { error: "Unknown action" },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error("[Sitemap Admin] GET Error:", error);
    
    return NextResponse.json(
      {
        error: "Admin operation failed",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  if (!isAuthorized(request)) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    const body = await request.json();
    const { action, options = {} } = body;

    switch (action) {
      case "warm-cache":
        console.log("[Sitemap Admin] Starting cache warming operation");
        const warmResult = await admin.warmCache(options);
        
        return NextResponse.json(warmResult, {
          status: warmResult.success ? 200 : 207, // 207 Multi-Status for partial success
          headers: { "Cache-Control": "no-cache" },
        });

      case "regenerate":
        console.log("[Sitemap Admin] Starting sitemap regeneration");
        const regenResult = await admin.regenerateAllSitemaps(options);
        
        return NextResponse.json(regenResult, {
          status: regenResult.success ? 200 : 207,
          headers: { "Cache-Control": "no-cache" },
        });

      case "maintenance":
        console.log("[Sitemap Admin] Starting maintenance operation");
        const maintenanceResult = await admin.performMaintenance(options);
        
        return NextResponse.json(maintenanceResult, {
          status: maintenanceResult.errors.length === 0 ? 200 : 207,
          headers: { "Cache-Control": "no-cache" },
        });

      case "clear-cache":
        console.log("[Sitemap Admin] Clearing cache");
        await cacheManager.invalidateCache();
        
        return NextResponse.json({
          success: true,
          message: "Cache cleared successfully",
          timestamp: new Date().toISOString(),
        });

      case "health-check":
        console.log("[Sitemap Admin] Performing health check");
        const healthResult = await admin.performHealthCheck();
        
        return NextResponse.json(healthResult, {
          status: healthResult.overall === "critical" ? 503 : 200,
          headers: { "Cache-Control": "no-cache" },
        });

      default:
        return NextResponse.json(
          { error: "Unknown action" },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error("[Sitemap Admin] POST Error:", error);
    
    return NextResponse.json(
      {
        error: "Admin operation failed",
        message: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// PUT endpoint for configuration updates
export async function PUT(request: Request) {
  if (!isAuthorized(request)) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    const body = await request.json();
    const { component, config } = body;

    if (!component || !config) {
      return NextResponse.json(
        { error: "Component and config are required" },
        { status: 400 }
      );
    }

    switch (component) {
      case "validator":
        validator.updateConfig(config);
        return NextResponse.json({
          success: true,
          message: "Validator configuration updated",
          config: validator.getConfig(),
        });

      case "cache":
        // Cache configuration updates would go here
        return NextResponse.json({
          success: true,
          message: "Cache configuration updated",
          config: cacheManager.getStats(),
        });

      default:
        return NextResponse.json(
          { error: "Unknown component" },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error("[Sitemap Admin] PUT Error:", error);
    
    return NextResponse.json(
      {
        error: "Configuration update failed",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// DELETE endpoint for cleanup operations
export async function DELETE(request: Request) {
  if (!isAuthorized(request)) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    const url = new URL(request.url);
    const target = url.searchParams.get("target");

    switch (target) {
      case "cache":
        console.log("[Sitemap Admin] Clearing all cache");
        await cacheManager.invalidateCache();
        
        return NextResponse.json({
          success: true,
          message: "Cache cleared successfully",
          timestamp: new Date().toISOString(),
        });

      case "analytics":
        console.log("[Sitemap Admin] Clearing analytics data");
        const cleared = require("@/services/sitemap-analytics").sitemapAnalytics.clearOldMetrics(0);
        
        return NextResponse.json({
          success: true,
          message: `Cleared ${cleared} analytics entries`,
          clearedCount: cleared,
          timestamp: new Date().toISOString(),
        });

      case "old-data":
        const days = parseInt(url.searchParams.get("days") || "7");
        console.log(`[Sitemap Admin] Clearing data older than ${days} days`);
        
        const maintenanceResult = await admin.performMaintenance({
          clearOldCache: true,
          clearOldAnalytics: true,
          cacheMaxAge: days,
          analyticsMaxAge: days,
        });
        
        return NextResponse.json({
          success: maintenanceResult.errors.length === 0,
          message: `Maintenance completed`,
          cacheCleared: maintenanceResult.cacheCleared,
          analyticsCleared: maintenanceResult.analyticsCleared,
          errors: maintenanceResult.errors,
          timestamp: new Date().toISOString(),
        });

      default:
        return NextResponse.json(
          { error: "Unknown target" },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error("[Sitemap Admin] DELETE Error:", error);
    
    return NextResponse.json(
      {
        error: "Delete operation failed",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}