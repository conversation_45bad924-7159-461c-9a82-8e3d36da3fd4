import { NextResponse } from "next/server";
import { SitemapIndexGenerator } from "@/services/sitemap-index-generator";
import { URLBuilder } from "@/services/sitemap-url-builder";
import { XMLSerializer } from "@/services/sitemap-xml-serializer";
import { SitemapCacheManager, MemorySitemapCache } from "@/services/sitemap-cache";
import { SitemapErrorHandler, SitemapError, SitemapErrorType } from "@/services/sitemap-error-handler";
import { sitemapMonitor } from "@/services/sitemap-monitor";
import { SITEMAP_CONFIG, SITEMAP_HEADERS } from "@/constants/sitemap";
import { extractLocaleFromPath, getLastModifiedDate } from "@/lib/sitemap-utils";

// Force static generation for this route
export const dynamic = 'force-static';

// Initialize sitemap components (could be moved to a singleton or DI container)
const urlBuilder = new URLBuilder({
  baseUrl: SITEMAP_CONFIG.baseUrl,
  locales: SITEMAP_CONFIG.locales,
  defaultLocale: SITEMAP_CONFIG.defaultLocale,
  localePrefix: "as-needed",
});

const xmlSerializer = new XMLSerializer({
  includeHreflang: SITEMAP_CONFIG.enableHreflang,
  prettyPrint: process.env.NODE_ENV === "development",
  validateXML: true,
});

const cacheManager = new SitemapCacheManager(new MemorySitemapCache());
const sitemapGenerator = new SitemapIndexGenerator(urlBuilder, xmlSerializer, cacheManager);
const errorHandler = new SitemapErrorHandler(cacheManager, xmlSerializer, urlBuilder);

export async function GET() {
  const startTime = Date.now();
  
  try {
    // Use default locale for static generation
    const locale = SITEMAP_CONFIG.defaultLocale;
    
    // Log request for monitoring
    console.log(`[Sitemap] Generating sitemap for locale: ${locale}`);
    
    // Skip conditional requests for static generation
    
    // Generate sitemap (index or single based on content size)
    const result = await sitemapGenerator.generateSitemap(locale);
    
    if (!result.isValid) {
      console.error("[Sitemap] Generated invalid XML:", result.validationErrors);
      throw new SitemapError(
        `Generated sitemap XML is invalid: ${result.validationErrors?.join(", ")}`,
        SitemapErrorType.VALIDATION_ERROR,
        { validationErrors: result.validationErrors }
      );
    }

    // Log generation metrics
    const totalTime = Date.now() - startTime;
    console.log(`[Sitemap] Generated successfully in ${totalTime}ms with ${result.urlCount} URLs/sitemaps`);

    // Record monitoring metrics
    sitemapMonitor.recordGeneration(
      "index",
      locale,
      result.urlCount,
      totalTime,
      Buffer.byteLength(result.xml, 'utf8'),
      false // Not from cache since we just generated it
    );

    // Return XML response with proper headers
    return new NextResponse(result.xml, {
      status: 200,
      headers: {
        "Content-Type": SITEMAP_HEADERS.CONTENT_TYPE,
        "Cache-Control": `public, max-age=${SITEMAP_CONFIG.cacheTimeout}, s-maxage=${SITEMAP_CONFIG.cacheTimeout}`,
        "X-Sitemap-Type": result.xml.includes("<sitemapindex") ? "index" : "urlset",
        "X-Sitemap-URLs": result.urlCount.toString(),
        "X-Generation-Time": totalTime.toString(),
        "X-Locale": locale,
        "Vary": SITEMAP_HEADERS.VARY,
        "Last-Modified": new Date().toUTCString(),
      },
    });
  } catch (error) {
    const totalTime = Date.now() - startTime;
    
    // Use error handler to recover from errors
    try {
      // Use default locale for static generation
      const locale = SITEMAP_CONFIG.defaultLocale;
      
      // Handle the error with recovery mechanisms
      const recoveryResult = await errorHandler.handleError(
        error instanceof Error ? error : new Error(String(error)),
        {
          contentType: "index", // Main sitemap is index type
          locale: locale
        }
      );
      
      // Record error metrics
      sitemapMonitor.recordGeneration(
        "index",
        locale,
        recoveryResult.urlCount,
        totalTime,
        Buffer.byteLength(recoveryResult.xml, 'utf8'),
        true, // This is likely from cache recovery
        error instanceof Error ? error : new Error(String(error))
      );

      // Return the recovered sitemap
      return new NextResponse(recoveryResult.xml, {
        status: 200,
        headers: {
          "Content-Type": SITEMAP_HEADERS.CONTENT_TYPE,
          "Cache-Control": "public, max-age=300, s-maxage=300", // Shorter cache for recovered content
          "X-Sitemap-Recovery": "true",
          "X-Sitemap-URLs": recoveryResult.urlCount.toString(),
          "X-Generation-Time": totalTime.toString(),
          "X-Error-Type": error instanceof SitemapError ? error.type : "UNKNOWN_ERROR",
          "Vary": SITEMAP_HEADERS.VARY,
        },
      });
    } catch (recoveryError) {
      // If recovery also fails, return minimal sitemap
      console.error("[Sitemap] Recovery also failed:", recoveryError);
      const minimalSitemap = xmlSerializer.createMinimalSitemap(SITEMAP_CONFIG.baseUrl);
      
      // Record critical error metrics
      const locale = SITEMAP_CONFIG.defaultLocale;
      sitemapMonitor.recordGeneration(
        "index",
        locale,
        1, // Minimal sitemap has 1 URL
        totalTime,
        Buffer.byteLength(minimalSitemap, 'utf8'),
        false,
        error instanceof Error ? error : new Error(String(error))
      );
      
      return new NextResponse(minimalSitemap, {
        status: 200,
        headers: {
          "Content-Type": SITEMAP_HEADERS.CONTENT_TYPE,
          "Cache-Control": "public, max-age=300, s-maxage=300",
          "X-Sitemap-Minimal": "true",
          "X-Generation-Time": totalTime.toString(),
          "X-Recovery-Failed": "true",
          "Vary": SITEMAP_HEADERS.VARY,
        },
      });
    }
  }
}



/**
 * Health check endpoint for sitemap functionality
 */
export async function HEAD() {
  try {
    const stats = await sitemapGenerator.getGenerationStats();
    
    return new NextResponse(null, {
      status: 200,
      headers: {
        "X-Sitemap-Health": "ok",
        "X-Estimated-URLs": Object.values(stats.estimatedCounts).reduce((sum, count) => (sum as number) + (count as number), 0).toString(),
        "X-Should-Use-Index": stats.shouldUseIndex.toString(),
        "X-Cache-Hit-Rate": stats.cacheStats.hitRate.toString(),
      },
    });
  } catch (error) {
    return new NextResponse(null, {
      status: 503,
      headers: {
        "X-Sitemap-Health": "error",
        "X-Error": error instanceof Error ? error.message : "Unknown error",
      },
    });
  }
}
