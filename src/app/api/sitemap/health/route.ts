import { NextResponse } from "next/server";
import { sitemapMonitor } from "@/services/sitemap-monitor";
import { sitemapAnalytics } from "@/services/sitemap-analytics";

export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const format = url.searchParams.get("format") || "json";
    const timeWindow = parseInt(url.searchParams.get("window") || "60");

    // Handle different response formats
    switch (format) {
      case "prometheus":
        return new NextResponse(sitemapMonitor.getPrometheusMetrics(), {
          status: 200,
          headers: {
            "Content-Type": "text/plain; charset=utf-8",
            "Cache-Control": "no-cache",
          },
        });

      case "health":
        const healthCheck = sitemapMonitor.performHealthCheck();
        const statusCode = healthCheck.status === "critical" ? 503 : 200;
        
        return NextResponse.json(healthCheck, {
          status: statusCode,
          headers: {
            "Cache-Control": "no-cache",
          },
        });

      case "dashboard":
        const dashboardData = sitemapMonitor.getDashboardData();
        return NextResponse.json(dashboardData, {
          status: 200,
          headers: {
            "Cache-Control": "no-cache",
          },
        });

      case "analytics":
        const analytics = sitemapAnalytics.getAnalyticsSummary();
        return NextResponse.json(analytics, {
          status: 200,
          headers: {
            "Cache-Control": "no-cache",
          },
        });

      case "performance":
        const performance = sitemapAnalytics.getPerformanceMetrics(timeWindow);
        return NextResponse.json(performance, {
          status: 200,
          headers: {
            "Cache-Control": "no-cache",
          },
        });

      case "coverage":
        const coverage = sitemapAnalytics.getContentCoverage();
        return NextResponse.json(coverage, {
          status: 200,
          headers: {
            "Cache-Control": "no-cache",
          },
        });

      case "errors":
        const errors = sitemapAnalytics.getRecentErrors(timeWindow);
        return NextResponse.json(errors, {
          status: 200,
          headers: {
            "Cache-Control": "no-cache",
          },
        });

      case "export":
        const exportFormat = url.searchParams.get("export") as "json" | "csv" || "json";
        const exportData = sitemapAnalytics.exportMetrics(exportFormat);
        const contentType = exportFormat === "csv" 
          ? "text/csv; charset=utf-8"
          : "application/json; charset=utf-8";
        
        return new NextResponse(exportData, {
          status: 200,
          headers: {
            "Content-Type": contentType,
            "Content-Disposition": `attachment; filename="sitemap-metrics.${exportFormat}"`,
            "Cache-Control": "no-cache",
          },
        });

      default:
        // Default: return basic health and performance info
        const basicInfo = {
          health: sitemapMonitor.performHealthCheck(),
          performance: sitemapAnalytics.getPerformanceMetrics(timeWindow),
          timestamp: new Date().toISOString(),
        };

        return NextResponse.json(basicInfo, {
          status: 200,
          headers: {
            "Cache-Control": "no-cache",
          },
        });
    }
  } catch (error) {
    console.error("[Sitemap Health] Error in health endpoint:", error);
    
    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      },
      {
        status: 500,
        headers: {
          "Cache-Control": "no-cache",
        },
      }
    );
  }
}

// POST endpoint for manual operations
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { action, config } = body;

    switch (action) {
      case "check-alerts":
        sitemapMonitor.checkForAlerts();
        return NextResponse.json({ 
          success: true, 
          message: "Alert check triggered",
          timestamp: new Date().toISOString(),
        });

      case "update-alert-config":
        if (!config) {
          return NextResponse.json(
            { error: "Missing config parameter" },
            { status: 400 }
          );
        }
        sitemapMonitor.updateAlertConfig(config);
        return NextResponse.json({ 
          success: true, 
          message: "Alert configuration updated",
          config: sitemapMonitor.getAlertConfig(),
        });

      case "clear-old-metrics":
        const days = body.days || 7;
        const cleared = sitemapAnalytics.clearOldMetrics(days);
        return NextResponse.json({ 
          success: true, 
          message: `Cleared ${cleared} old metrics`,
          clearedCount: cleared,
        });

      default:
        return NextResponse.json(
          { error: "Unknown action" },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error("[Sitemap Health] Error in POST endpoint:", error);
    
    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}