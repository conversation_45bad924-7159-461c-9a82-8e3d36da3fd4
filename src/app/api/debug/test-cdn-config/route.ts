/**
 * 测试CDN配置的API端点
 */


import { respData, respErr } from '@/lib/resp';
import { isCDNConfigured, getDefaultCDNConfig } from '@/lib/cdn-config';
import { generateSecureDownloadUrl } from '@/services/cdn';

export async function GET() {
  try {
    console.log('=== CDN Configuration Test ===');
    
    // 检查环境变量
    const envVars = {
      CDN_BASE_URL: process.env.CDN_BASE_URL,
      CDN_SECRET_KEY: process.env.CDN_SECRET_KEY ? `${process.env.CDN_SECRET_KEY.substring(0, 8)}...` : 'NOT SET',
      CDN_ENABLED: process.env.CDN_ENABLED,
      CDN_DEFAULT_EXPIRY_HOURS: process.env.CDN_DEFAULT_EXPIRY_HOURS,
    };
    
    console.log('Environment variables:', envVars);
    
    // 检查CDN是否配置
    const isConfigured = isCDNConfigured();
    console.log('CDN configured:', isConfigured);
    
    if (!isConfigured) {
      return respData({
        configured: false,
        envVars,
        error: 'CDN not configured'
      });
    }
    
    // 获取配置
    const config = getDefaultCDNConfig();
    console.log('CDN config:', {
      baseUrl: config.baseUrl,
      secretKeyLength: config.secretKey.length,
      defaultExpiryHours: config.defaultExpiryHours
    });
    
    // 测试生成下载URL
    const testS3Url = 'https://loopcraft-music.tos-cn-beijing.volces.com/music_ai/test-file.wav';
    const testResult = generateSecureDownloadUrl(testS3Url, {
      customFilename: 'test-download.wav',
      expiryHours: 1,
      forceDownload: true
    });
    
    console.log('Test download URL generated:', testResult.downloadUrl);
    
    return respData({
      configured: true,
      envVars,
      config: {
        baseUrl: config.baseUrl,
        secretKeyLength: config.secretKey.length,
        defaultExpiryHours: config.defaultExpiryHours
      },
      testDownloadUrl: testResult.downloadUrl,
      testExpiresAt: testResult.expiresAt
    });
    
  } catch (error) {
    console.error('CDN config test error:', error);
    return respErr(`CDN test failed: ${error instanceof Error ? error.message : String(error)}`);
  }
}