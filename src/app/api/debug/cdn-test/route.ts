/**
 * CDN 测试 API 端点
 */


import { respData, respErr } from '@/lib/resp';
import { testCDNConfiguration } from '@/lib/test-cdn';

export async function GET() {
  try {
    const result = await testCDNConfiguration();
    
    if (result.isConfigured) {
      return respData({
        status: 'success',
        message: 'CDN configuration is valid',
        ...result
      });
    } else {
      return respErr(result.error || 'CDN configuration failed');
    }
  } catch (error) {
    console.error('CDN test error:', error);
    return respErr('Failed to test CDN configuration');
  }
}