import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { getUserCollections } from "@/models/collection";

export async function GET() {
  try {
    console.log("=== Debug Favorite Test ===");
    
    // Test authentication
    const session = await auth();
    console.log("Session:", {
      hasSession: !!session,
      hasUser: !!session?.user,
      userUuid: session?.user?.uuid,
      userEmail: session?.user?.email,
    });
    
    if (!session?.user?.uuid) {
      return NextResponse.json({ 
        error: "Unauthorized",
        debug: {
          hasSession: !!session,
          hasUser: !!session?.user,
          userKeys: session?.user ? Object.keys(session.user) : [],
        }
      }, { status: 401 });
    }

    // Test database connection
    try {
      const collections = await getUserCollections(session.user.uuid);
      console.log("Collections found:", collections.length);
      
      return NextResponse.json({
        success: true,
        debug: {
          userUuid: session.user.uuid,
          userEmail: session.user.email,
          collectionsCount: collections.length,
          collections: collections.map(c => ({ name: c.name, uuid: c.uuid })),
        }
      });
    } catch (dbError) {
      console.error("Database error:", dbError);
      return NextResponse.json({
        error: "Database error",
        debug: {
          userUuid: session.user.uuid,
          dbError: dbError instanceof Error ? dbError.message : String(dbError),
        }
      }, { status: 500 });
    }
    
  } catch (error) {
    console.error("Debug test error:", error);
    return NextResponse.json({
      error: "Internal server error",
      debug: {
        error: error instanceof Error ? error.message : String(error),
      }
    }, { status: 500 });
  }
}