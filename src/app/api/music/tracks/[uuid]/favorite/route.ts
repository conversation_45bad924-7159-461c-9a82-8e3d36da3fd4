import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import {
    addTrackToCollection,
    removeTrackFromCollection,
    isTrackInCollection,
    getUserCollections,
    insertUserTrackCollection
} from "@/models/collection";
import { findTrackByUuid } from "@/models/track";
import { v4 as uuidv4 } from "uuid";

// GET - Check if track is favorited
export async function GET(
    request: NextRequest,
    { params }: { params: Promise<{ uuid: string }> }
) {
    try {
        const session = await auth();
        if (!session?.user?.uuid) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        const { uuid: trackUuid } = await params;
        const userUuid = session.user.uuid;

        // Get user's default favorites collection
        const collections = await getUserCollections(userUuid);
        const favoritesCollection = collections.find(c => c.name === "Favorites") ||
            collections.find(c => c.name === "收藏夹");

        if (!favoritesCollection) {
            return NextResponse.json({ is_favorite: false }, {
                headers: {
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                }
            });
        }

        const isFavorite = await isTrackInCollection(favoritesCollection.uuid, trackUuid);

        return NextResponse.json({ is_favorite: isFavorite }, {
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
            }
        });
    } catch (error) {
        console.error("Error checking favorite status:", error);
        return NextResponse.json(
            { error: "Failed to check favorite status", details: error instanceof Error ? error.message : String(error) },
            { status: 500 }
        );
    }
}

// POST - Add track to favorites
export async function POST(
    request: NextRequest,
    { params }: { params: Promise<{ uuid: string }> }
) {
    try {
        const session = await auth();
        if (!session?.user?.uuid) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        const { uuid: trackUuid } = await params;
        const userUuid = session.user.uuid;

        // Verify track exists
        const track = await findTrackByUuid(trackUuid);
        if (!track) {
            return NextResponse.json({ error: "Track not found" }, { status: 404 });
        }

        // Get or create user's default favorites collection
        let collections = await getUserCollections(userUuid);
        let favoritesCollection = collections.find(c => c.name === "Favorites") ||
            collections.find(c => c.name === "收藏夹");

        if (!favoritesCollection) {
            try {
                // Create default favorites collection
                favoritesCollection = await insertUserTrackCollection({
                    uuid: uuidv4(),
                    user_uuid: userUuid,
                    name: "Favorites",
                    description: "My favorite tracks",
                    is_public: false,
                });

                if (!favoritesCollection) {
                    return NextResponse.json(
                        { error: "Failed to create favorites collection" },
                        { status: 500 }
                    );
                }
            } catch (createError) {
                // Handle race condition - collection might have been created by another request
                console.warn("Collection creation failed, retrying fetch:", createError);
                collections = await getUserCollections(userUuid);
                favoritesCollection = collections.find(c => c.name === "Favorites") ||
                    collections.find(c => c.name === "收藏夹");

                if (!favoritesCollection) {
                    return NextResponse.json(
                        { error: "Failed to create or find favorites collection" },
                        { status: 500 }
                    );
                }
            }
        }

        // Add track to favorites
        const result = await addTrackToCollection(favoritesCollection.uuid, trackUuid);

        if (!result) {
            return NextResponse.json(
                { error: "Failed to add track to favorites" },
                { status: 500 }
            );
        }

        return NextResponse.json({
            success: true,
            is_favorite: true,
            message: "Track added to favorites"
        }, {
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
            }
        });
    } catch (error) {
        console.error("Error adding to favorites:", error);
        return NextResponse.json(
            { error: "Failed to add to favorites" },
            { status: 500 }
        );
    }
}

// DELETE - Remove track from favorites
export async function DELETE(
    request: NextRequest,
    { params }: { params: Promise<{ uuid: string }> }
) {
    try {
        const session = await auth();
        if (!session?.user?.uuid) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        const { uuid: trackUuid } = await params;
        const userUuid = session.user.uuid;

        // Get user's default favorites collection
        const collections = await getUserCollections(userUuid);
        const favoritesCollection = collections.find(c => c.name === "Favorites") ||
            collections.find(c => c.name === "收藏夹");

        if (!favoritesCollection) {
            return NextResponse.json({
                success: true,
                is_favorite: false,
                message: "Track not in favorites"
            }, {
                headers: {
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                }
            });
        }

        // Remove track from favorites
        const result = await removeTrackFromCollection(favoritesCollection.uuid, trackUuid);

        return NextResponse.json({
            success: true,
            is_favorite: false,
            message: result ? "Track removed from favorites" : "Track not in favorites"
        }, {
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
            }
        });
    } catch (error) {
        console.error("Error removing from favorites:", error);
        return NextResponse.json(
            { error: "Failed to remove from favorites" },
            { status: 500 }
        );
    }
}