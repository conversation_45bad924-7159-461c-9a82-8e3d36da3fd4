import { respData, respErr } from "@/lib/resp";
import { NextRequest } from "next/server";

/**
 * Volcano音乐生成回调处理端点
 * 接收来自Volcengine的音乐生成完成通知
 */

export interface VolcanoCallbackPayload {
  TaskID: string;
  Status: number; // 0->等待中, 1->处理中, 2->成功, 3->失败
  Progress: number;
  FailureReason?: {
    Code: number;
    Msg: string;
  };
  SongDetail?: {
    AudioUrl: string;
    Prompt: string;
    Duration: number;
    CallbackURL: string;
    Theme: string;
    Instrument: string;
    Mood: string;
    Genre: string;
    TosPath: string;
  };
  // 可能的签名字段用于验证
  Signature?: string;
  Timestamp?: string;
}

/**
 * 验证回调请求的真实性
 */
function validateCallbackRequest(payload: VolcanoCallbackPayload, headers: Headers): boolean {
  try {
    // 使用配置验证工具
    const { validateCallbackSignature } = require("@/lib/config-validator");
    
    // 检查必需字段
    if (!payload.TaskID) {
      console.error("Callback validation failed: Missing TaskID");
      return false;
    }

    if (typeof payload.Status !== 'number') {
      console.error("Callback validation failed: Invalid Status");
      return false;
    }

    // 验证签名（如果提供）
    const signature = payload.Signature || headers.get('x-volcano-signature');
    const timestamp = payload.Timestamp || headers.get('x-volcano-timestamp');
    
    if (!validateCallbackSignature(payload, signature, timestamp)) {
      console.error("Callback validation failed: Invalid signature");
      return false;
    }

    // 检查User-Agent或其他头部信息（如果Volcano提供）
    const userAgent = headers.get('user-agent');
    console.log(`Callback User-Agent: ${userAgent}`);

    // 基本验证通过
    return true;
  } catch (error) {
    console.error("Callback validation error:", error);
    return false;
  }
}

/**
 * 映射Volcano状态到标准状态
 */
function mapVolcanoStatus(volcanoStatus: number): "pending" | "processing" | "completed" | "failed" {
  switch (volcanoStatus) {
    case 0:
      return "pending";
    case 1:
      return "processing";
    case 2:
      return "completed";
    case 3:
      return "failed";
    default:
      return "pending";
  }
}

export async function POST(req: NextRequest) {
  try {
    console.log("Received Volcano callback");

    // 解析请求体
    let payload: VolcanoCallbackPayload;
    try {
      payload = await req.json();
    } catch (error) {
      console.error("Failed to parse callback payload:", error);
      return respErr("Invalid JSON payload");
    }

    console.log("Volcano callback payload:", {
      TaskID: payload.TaskID,
      Status: payload.Status,
      Progress: payload.Progress,
      HasSongDetail: !!payload.SongDetail,
      TosPath: payload.SongDetail?.TosPath,
    });

    // 验证请求
    if (!validateCallbackRequest(payload, req.headers)) {
      console.error("Callback validation failed");
      return respErr("Invalid callback request");
    }

    // 处理回调
    try {
      const { CallbackHandler } = await import("@/services/callback-handler");
      const handler = CallbackHandler.getInstance();
      
      await handler.handleVolcanoCallback(payload);
      
      console.log(`Successfully processed callback for task ${payload.TaskID}`);
      
      return respData({
        message: "Callback processed successfully",
        task_id: payload.TaskID,
        status: mapVolcanoStatus(payload.Status),
      });
      
    } catch (error) {
      console.error("Failed to process callback:", error);
      
      // 即使处理失败，也返回成功状态给Volcano，避免重复回调
      // 错误会被记录，可以通过日志排查
      return respData({
        message: "Callback received (processing failed)",
        task_id: payload.TaskID,
        error: error instanceof Error ? error.message : String(error),
      });
    }

  } catch (error) {
    console.error("Callback endpoint error:", error);
    
    // 返回成功状态避免Volcano重复发送回调
    return respData({
      message: "Callback endpoint error",
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

// GET方法用于健康检查
export async function GET() {
  return respData({
    message: "Volcano callback endpoint is active",
    timestamp: new Date().toISOString(),
  });
}