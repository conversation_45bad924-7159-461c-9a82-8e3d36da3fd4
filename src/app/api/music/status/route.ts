import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { findMusicGenerationByUuid, updateMusicGenerationStatus } from "@/models/music-generation";
import { findTrackByGenerationUuid } from "@/models/track";
import { MusicStatusRequest, MusicStatusResponse } from "@/types/music-api";
import { MusicProviderFactory } from "@/services/music-provider";
import { TrackCreationService } from "@/services/track-creation-service";
import { audioFileProcessor } from "@/services/audio-file-processor";
import { TaskContext, ProviderLogger } from "@/lib/provider-logger";

/**
 * 主动查询提供商状态并更新数据库
 */
async function updateGenerationStatus(generation: any) {
  // 如果已经完成或失败，不需要再查询
  if (generation.status === "completed" || generation.status === "failed") {
    return generation;
  }

  const taskContext: TaskContext = {
    generation_uuid: generation.uuid,
    provider_task_id: generation.provider_task_id,
    user_uuid: generation.user_uuid
  };

  const logger = ProviderLogger.createSafeLogger(taskContext);

  try {
    // 获取提供商实例
    await MusicProviderFactory.initialize();
    const provider = MusicProviderFactory.getProvider(generation.provider || "volcano");

    if (!provider) {
      logger.logError(new Error(`Provider not found: ${generation.provider}`), 'provider lookup');
      return generation;
    }

    // 检查是否有 provider_task_id
    if (!generation.provider_task_id) {
      logger.logWarning('No provider_task_id, skipping provider check');
      return generation;
    }

    // 创建任务上下文
    const taskContext = {
      generation_uuid: generation.uuid,
      provider_task_id: generation.provider_task_id,
      user_uuid: generation.user_uuid
    };

    // 查询提供商状态
    const providerStatus = await provider.checkStatus(generation.provider_task_id, taskContext);

    // 更新数据库状态
    const updatedGeneration = await updateMusicGenerationStatus(
      generation.uuid,
      providerStatus.status,
      providerStatus.error
    );

    logger.logOperationComplete('status update', `${generation.status} -> ${providerStatus.status}`);

    // 如果完成了，创建音轨记录
    if (providerStatus.status === "completed" && providerStatus.result) {
      const existingTrack = await findTrackByGenerationUuid(generation.uuid);
      if (!existingTrack) {
        // 确定文件URL和TOS路径
        let fileUrl = providerStatus.result.file_url;
        let filePath = providerStatus.result.file_path;

        // Use the file_url from the optimized provider response (already contains TOS URL)
        console.log(`Using file URL for track creation: ${fileUrl}`);

        // 使用优化的track创建服务
        const track = generation.provider === 'volcano' && providerStatus.result.metadata
          ? await TrackCreationService.createTrackFromVolcanoResponse(generation, {
              AudioUrl: providerStatus.result.original_file_url,
              TosPath: providerStatus.result.file_path,
              Duration: providerStatus.result.metadata.duration,
              Prompt: providerStatus.result.metadata.prompt,
              Genre: providerStatus.result.metadata.genre,
              Mood: providerStatus.result.metadata.mood,
              Theme: providerStatus.result.metadata.theme,
              Instrument: providerStatus.result.metadata.instrument
            })
          : await TrackCreationService.createTrack({
              generation,
              file_url: fileUrl,
              file_path: filePath, // 传递TOS路径
              file_size: providerStatus.result.file_size || 0,
              metadata: {
                ...providerStatus.result.metadata || {},
                original_provider_url: providerStatus.result.original_file_url,
              },
              is_public: true, // 默认公开
            });

        logger.logOperationComplete('track creation', track?.uuid);

        // 立即尝试处理文件（TOS文件会跳过下载/上传）
        if (track && providerStatus.result) {
          const finalFileUrl = fileUrl;
          const fileSize = providerStatus.result.file_size;

          // 使用 setImmediate 确保在下一个事件循环中执行，但尽快开始
          setImmediate(async () => {
            try {
              const result = await audioFileProcessor.processAudioFile(
                track.uuid,
                finalFileUrl,
                fileSize,
                filePath // 传递TOS路径
              );

              if (result.success) {
                console.log(`Audio file processed successfully for track ${track.uuid} (TOS: ${result.isTOSFile})`);
              } else {
                console.error(`Audio file processing failed for track ${track.uuid}:`, result.error);
                // 如果文件处理失败，至少确保track记录中有原始URL
                console.log(`Using original URL as fallback for track ${track.uuid}`);
              }
            } catch (error) {
              console.error(`Audio file processing error for track ${track.uuid}:`, error);
              // 文件处理失败时，track仍然可以使用原始URL播放
              console.log(`Track ${track.uuid} will use original URL for playback`);
            }
          });
        }
      }
    }

    return updatedGeneration || generation;
  } catch (error) {
    logger.logError(error, 'generation status update');
    return generation;
  }
}

export async function POST(req: Request) {
  try {
    const body: MusicStatusRequest = await req.json();
    const { generation_uuid } = body;

    if (!generation_uuid) {
      return respErr("Missing generation_uuid parameter");
    }

    // Get user authentication
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    // Find the generation record
    let generation = await findMusicGenerationByUuid(generation_uuid);
    if (!generation) {
      return respErr("Generation not found");
    }

    // Check if user owns this generation
    if (generation.user_uuid !== user_uuid) {
      return respErr("Access denied");
    }

    // 主动查询并更新状态
    generation = await updateGenerationStatus(generation);

    // Check if generation still exists after update
    if (!generation) {
      return respErr("Generation not found after status update");
    }

    // Calculate progress based on status and time elapsed
    let progress = 0;
    switch (generation.status) {
      case "pending":
        progress = 0;
        break;
      case "processing":
        // Estimate progress based on time elapsed
        const elapsed = Date.now() - new Date(generation.created_at!).getTime();
        const estimatedTotal = generation.duration * 2 * 1000; // 2x duration in ms
        progress = Math.min(90, Math.floor((elapsed / estimatedTotal) * 100));
        break;
      case "completed":
        progress = 100;
        break;
      case "failed":
        progress = 0;
        break;
    }

    // If completed, try to get the track
    let track = undefined;
    if (generation.status === "completed") {
      track = await findTrackByGenerationUuid(generation_uuid);
    }

    const response: MusicStatusResponse = {
      code: 0,
      message: "ok",
      data: {
        generation_uuid,
        status: generation.status,
        progress,
        track: track || undefined,
        error_message: generation.error_message || undefined,
        // 添加回调和TOS相关信息（用于调试）
        callback_received: !!generation.callback_received_at,
        callback_processed: !!generation.callback_processed,
        provider: generation.provider,
        created_at: generation.created_at?.toISOString(),
        updated_at: generation.updated_at?.toISOString(),
        completed_at: generation.completed_at?.toISOString(),
      },
    };

    return respData(response.data);
  } catch (error) {
    console.error("Music status error:", error);
    return respErr("Failed to get music generation status");
  }
}

// GET method for status check
export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    const generation_uuid = url.searchParams.get("generation_uuid");

    if (!generation_uuid) {
      return respErr("Missing generation_uuid parameter");
    }

    // Get user authentication
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    // Find the generation record
    let generation = await findMusicGenerationByUuid(generation_uuid);
    if (!generation) {
      return respErr("Generation not found");
    }

    // Check if user owns this generation
    if (generation.user_uuid !== user_uuid) {
      return respErr("Access denied");
    }

    // 主动查询并更新状态
    generation = await updateGenerationStatus(generation);

    // Check if generation still exists after update
    if (!generation) {
      return respErr("Generation not found after status update");
    }

    // Calculate progress
    let progress = 0;
    switch (generation.status) {
      case "pending":
        progress = 0;
        break;
      case "processing":
        const elapsed = Date.now() - new Date(generation.created_at!).getTime();
        const estimatedTotal = generation.duration * 2 * 1000;
        progress = Math.min(90, Math.floor((elapsed / estimatedTotal) * 100));
        break;
      case "completed":
        progress = 100;
        break;
      case "failed":
        progress = 0;
        break;
    }

    // If completed, get the track
    let track = undefined;
    if (generation.status === "completed") {
      track = await findTrackByGenerationUuid(generation_uuid);
    }

    return respData({
      generation_uuid,
      status: generation.status,
      progress,
      track: track || undefined,
      error_message: generation.error_message || undefined,
      // 添加回调和TOS相关信息（用于调试）
      callback_received: !!generation.callback_received_at,
      callback_processed: !!generation.callback_processed,
      provider: generation.provider,
      created_at: generation.created_at?.toISOString(),
      updated_at: generation.updated_at?.toISOString(),
      completed_at: generation.completed_at?.toISOString(),
    });
  } catch (error) {
    console.error("Music status error:", error);
    return respErr("Failed to get music generation status");
  }
}
