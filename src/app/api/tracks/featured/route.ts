import { respData, respErr } from "@/lib/resp";
import { searchTracks } from "@/models/track";
import { TrackSearchParams } from "@/types/music";

export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    
    // Parse query parameters for featured tracks
    const limit = parseInt(url.searchParams.get("limit") || "4"); // Default to 4 for homepage
    
    // Validate limit
    if (limit < 1 || limit > 20) {
      return respErr("Limit must be between 1 and 20");
    }

    // Search for high-quality public tracks for homepage showcase
    const searchParams: TrackSearchParams = {
      is_public: true,
      sort_by: "download_count", // Sort by popularity
      sort_order: "desc",
      page: 1,
      per_page: limit * 3, // Get more tracks to filter from
    };

    const { tracks } = await searchTracks(searchParams);

    // Filter and select the best tracks for homepage showcase
    const featuredTracks = tracks
      .filter(track => {
        // Only include tracks with good quality indicators
        return (
          track.file_url && // Must have valid file URL
          track.title && // Must have title
          track.duration && track.duration >= 15 && // Reasonable duration
          (track.download_count || 0) >= 0 && // Include all tracks, even with 0 downloads
          track.is_public === true // Must be public
        );
      })
      .slice(0, limit); // Take only the requested number

    // If we don't have enough high-quality tracks, get more with relaxed criteria
    if (featuredTracks.length < limit) {
      const additionalParams: TrackSearchParams = {
        is_public: true,
        sort_by: "created_at", // Sort by newest
        sort_order: "desc",
        page: 1,
        per_page: limit * 2,
      };

      const { tracks: additionalTracks } = await searchTracks(additionalParams);
      
      // Add tracks that we haven't already included
      const existingUuids = new Set(featuredTracks.map(t => t.uuid));
      const newTracks = additionalTracks
        .filter(track => 
          !existingUuids.has(track.uuid) &&
          track.file_url &&
          track.title &&
          track.is_public === true
        )
        .slice(0, limit - featuredTracks.length);

      featuredTracks.push(...newTracks);
    }

    // Ensure we have some tracks to show
    if (featuredTracks.length === 0) {
      // Return mock data as fallback if no real tracks available
      const mockTracks = [
        {
          uuid: "featured-demo-1",
          generation_uuid: "gen-featured-1",
          user_uuid: "user-featured-1",
          title: "Upbeat Electronic Loop",
          prompt: "Energetic electronic music with driving beats",
          style: "electronic",
          mood: "upbeat",
          bpm: 128,
          duration: 30,
          file_url: "/demo/track1.mp3",
          file_format: "mp3" as const,
          download_count: 1250,
          is_public: true,
          created_at: "2024-01-15T10:00:00Z",
          waveform_data: {
            peaks: Array.from({ length: 100 }, (_, i) => {
              const seed = (i * 11 + 7) % 100;
              return Math.sin(seed * 0.15) * 0.4 + 0.5;
            }),
            duration: 30,
            sample_rate: 44100,
          },
          loop_verification: {
            is_seamless: true,
            verification_score: "0.95",
          },
        },
        {
          uuid: "featured-demo-2",
          generation_uuid: "gen-featured-2",
          user_uuid: "user-featured-2",
          title: "Chill Ambient Background",
          prompt: "Relaxing ambient music for focus and productivity",
          style: "ambient",
          mood: "calm",
          bpm: 85,
          duration: 60,
          file_url: "/demo/track2.mp3",
          file_format: "mp3" as const,
          download_count: 890,
          is_public: true,
          created_at: "2024-01-14T15:30:00Z",
          waveform_data: {
            peaks: Array.from({ length: 100 }, (_, i) => {
              const seed = (i * 7 + 13) % 100;
              return (Math.sin(seed * 0.1) * 0.3 + 0.4) * 0.7;
            }),
            duration: 60,
            sample_rate: 44100,
          },
          loop_verification: {
            is_seamless: true,
            verification_score: "0.88",
          },
        },
      ].slice(0, limit);

      return respData({
        tracks: mockTracks,
        total: mockTracks.length,
        source: "fallback",
      });
    }

    return respData({
      tracks: featuredTracks,
      total: featuredTracks.length,
      source: "database",
    });
  } catch (error) {
    console.error("Get featured tracks error:", error);
    return respErr("Failed to get featured tracks");
  }
}
