import { respData, respErr } from "@/lib/resp";
import { searchTracks, getAvailableStyles, getAvailableMoods } from "@/models/track";
import { TrackSearchParams } from "@/types/music";

export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    
    // Parse query parameters for public tracks
    const searchParams: TrackSearchParams = {
      query: url.searchParams.get("search") || undefined,
      style: url.searchParams.get("style") || undefined,
      mood: url.searchParams.get("mood") || undefined,
      bpm_min: url.searchParams.get("bpm_min") ? parseInt(url.searchParams.get("bpm_min")!) : undefined,
      bpm_max: url.searchParams.get("bpm_max") ? parseInt(url.searchParams.get("bpm_max")!) : undefined,
      duration: url.searchParams.get("duration") ? parseInt(url.searchParams.get("duration")!) : undefined,
      sort_by: url.searchParams.get("sort_by") as any || "download_count", // Default to popular
      sort_order: url.searchParams.get("sort_order") as "asc" | "desc" || "desc",
      page: url.searchParams.get("page") ? parseInt(url.searchParams.get("page")!) : 1,
      per_page: url.searchParams.get("per_page") ? parseInt(url.searchParams.get("per_page")!) : 12, // Good for grid layout
      is_public: true, // Force public tracks only
    };

    // Validate pagination
    if (searchParams.page! < 1) {
      return respErr("Page must be >= 1");
    }
    if (searchParams.per_page! < 1 || searchParams.per_page! > 50) {
      return respErr("Per page must be between 1 and 50");
    }

    // Search public tracks
    const { tracks, total } = await searchTracks(searchParams);

    // Calculate pagination info
    const total_pages = Math.ceil(total / searchParams.per_page!);
    const has_more = searchParams.page! < total_pages;

    // Get filter options for the response
    const [available_styles, available_moods] = await Promise.all([
      getAvailableStyles(),
      getAvailableMoods(),
    ]);

    // Return simplified response for explore page
    return respData({
      tracks,
      pagination: {
        total,
        page: searchParams.page!,
        per_page: searchParams.per_page!,
        total_pages,
        has_more,
      },
      filters: {
        available_styles,
        available_moods,
        bpm_range: { min: 60, max: 200 },
      },
    });
  } catch (error) {
    console.error("Get public tracks error:", error);
    return respErr("Failed to get public tracks");
  }
}
