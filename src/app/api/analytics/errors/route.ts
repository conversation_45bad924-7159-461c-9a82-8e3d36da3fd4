import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { getCDNErrorLogger, CDNErrorType } from "@/lib/cdn-errors";

/**
 * 获取 CDN 错误日志和统计
 */
export async function GET(req: Request) {
  try {
    // 验证用户身份（可选：只允许管理员访问）
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    // TODO: 添加管理员权限检查
    // const isAdmin = await checkAdminPermission(user_uuid);
    // if (!isAdmin) {
    //   return respErr("Admin permission required");
    // }

    const url = new URL(req.url);
    const level = url.searchParams.get("level") as 'error' | 'warn' | 'info' | undefined;
    const type = url.searchParams.get("type") as CDNErrorType | undefined;
    const timeRange = url.searchParams.get("time_range") || "24h";
    const limit = parseInt(url.searchParams.get("limit") || "50");
    const format = url.searchParams.get("format") || "json";

    const logger = getCDNErrorLogger();

    // 计算时间范围
    let since: Date | undefined;
    switch (timeRange) {
      case "1h":
        since = new Date(Date.now() - 60 * 60 * 1000);
        break;
      case "24h":
        since = new Date(Date.now() - 24 * 60 * 60 * 1000);
        break;
      case "7d":
        since = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case "30d":
        since = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
    }

    if (format === "report") {
      // 返回文本报告
      const stats = logger.getErrorStats(since);
      const logs = logger.getLogs({ level, type, since, limit: 20 });
      
      const report = `
📊 CDN 错误日志报告
${'='.repeat(50)}

📈 统计概览 (${timeRange}):
  总错误数: ${stats.total}
  错误级别分布:
    - 错误: ${stats.byLevel.error || 0}
    - 警告: ${stats.byLevel.warn || 0}
    - 信息: ${stats.byLevel.info || 0}

🔍 错误类型分布:
${Object.entries(stats.byType)
  .sort(([,a], [,b]) => b - a)
  .slice(0, 10)
  .map(([type, count]) => `  - ${type}: ${count}`)
  .join('\n')}

📝 最近错误日志:
${logs.slice(0, 10).map(log => 
  `[${log.timestamp.toISOString()}] ${log.level.toUpperCase()}: ${log.message}${
    log.requestId ? ` (RequestID: ${log.requestId})` : ''
  }`
).join('\n')}
`;

      return new Response(report, {
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
        },
      });
    }

    // 返回 JSON 数据
    const stats = logger.getErrorStats(since);
    const logs = logger.getLogs({ level, type, since, limit });

    return respData({
      time_range: timeRange,
      period: {
        start: since?.toISOString(),
        end: new Date().toISOString()
      },
      stats,
      logs: logs.map(log => ({
        ...log,
        timestamp: log.timestamp.toISOString()
      })),
      filters: {
        level,
        type,
        limit
      }
    });

  } catch (error) {
    console.error("CDN error analytics error:", error);
    return respErr("Failed to get CDN error analytics");
  }
}

/**
 * 清理旧的错误日志
 */
export async function DELETE(req: Request) {
  try {
    // 验证用户身份（只允许管理员）
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    // TODO: 添加管理员权限检查
    // const isAdmin = await checkAdminPermission(user_uuid);
    // if (!isAdmin) {
    //   return respErr("Admin permission required");
    // }

    const url = new URL(req.url);
    const olderThan = url.searchParams.get("older_than") || "7d";

    let cutoffDate: Date;
    switch (olderThan) {
      case "1h":
        cutoffDate = new Date(Date.now() - 60 * 60 * 1000);
        break;
      case "24h":
        cutoffDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
        break;
      case "7d":
        cutoffDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case "30d":
        cutoffDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        cutoffDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    }

    const logger = getCDNErrorLogger();
    logger.cleanup(cutoffDate);

    return respData({
      message: "Old CDN error logs cleaned up",
      cutoff_date: cutoffDate.toISOString()
    });

  } catch (error) {
    console.error("CDN error log cleanup error:", error);
    return respErr("Failed to cleanup CDN error logs");
  }
}