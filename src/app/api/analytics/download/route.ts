import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { getDownloadAnalytics } from "@/services/download-analytics";

/**
 * 获取下载统计信息
 */
export async function GET(req: Request) {
  try {
    // 验证用户身份（可选：只允许管理员访问）
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    const url = new URL(req.url);
    const timeRange = url.searchParams.get("time_range") || "24h";
    const format = url.searchParams.get("format") || "json";

    const analytics = getDownloadAnalytics();

    // 计算时间范围
    let startDate: Date | undefined;
    let endDate: Date = new Date();

    switch (timeRange) {
      case "1h":
        startDate = new Date(Date.now() - 60 * 60 * 1000);
        break;
      case "24h":
        startDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
        break;
      case "7d":
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case "30d":
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
    }

    const timeRangeFilter = startDate ? { start: startDate, end: endDate } : undefined;

    if (format === "report") {
      // 返回文本报告
      const report = analytics.generateReport(timeRangeFilter);
      
      return new Response(report, {
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
        },
      });
    }

    // 返回 JSON 数据
    const stats = analytics.getDownloadStats(timeRangeFilter);
    const performance = analytics.getPerformanceMetrics(timeRangeFilter);
    const recentEvents = analytics.getRecentEvents(20);

    return respData({
      time_range: timeRange,
      period: {
        start: startDate?.toISOString(),
        end: endDate.toISOString()
      },
      stats,
      performance,
      recent_events: recentEvents.slice(0, 10), // 只返回最近10个事件
      summary: {
        total_downloads: stats.total_downloads,
        cdn_usage_rate: stats.total_downloads > 0 
          ? Math.round((stats.cdn_downloads / stats.total_downloads) * 100) 
          : 0,
        success_rate: stats.success_rate,
        avg_response_time: stats.average_response_time
      }
    });

  } catch (error) {
    console.error("Download analytics error:", error);
    return respErr("Failed to get download analytics");
  }
}

/**
 * 清理旧的下载统计数据
 */
export async function DELETE(req: Request) {
  try {
    // 验证用户身份（只允许管理员）
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    // TODO: 添加管理员权限检查
    // const isAdmin = await checkAdminPermission(user_uuid);
    // if (!isAdmin) {
    //   return respErr("Admin permission required");
    // }

    const url = new URL(req.url);
    const olderThan = url.searchParams.get("older_than") || "7d";

    let cutoffDate: Date;
    switch (olderThan) {
      case "1h":
        cutoffDate = new Date(Date.now() - 60 * 60 * 1000);
        break;
      case "24h":
        cutoffDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
        break;
      case "7d":
        cutoffDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case "30d":
        cutoffDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        cutoffDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    }

    const analytics = getDownloadAnalytics();
    analytics.cleanupOldEvents(cutoffDate);

    return respData({
      message: "Old download analytics data cleaned up",
      cutoff_date: cutoffDate.toISOString()
    });

  } catch (error) {
    console.error("Download analytics cleanup error:", error);
    return respErr("Failed to cleanup download analytics");
  }
}