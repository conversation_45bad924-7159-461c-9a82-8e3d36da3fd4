"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { useAudioPlayer } from "@/contexts/audio-player-context";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Play, TestTube } from "lucide-react";

// Prevent SSR for this test page
import dynamic from "next/dynamic";

// Mock track data for testing
const mockTrack = {
  id: 1,
  uuid: "test-track-uuid-123",
  generation_uuid: "test-gen-uuid",
  user_uuid: "test-user-uuid",
  title: "Test Track for Global Player",
  slug: "test-track-for-global-player",
  prompt: "A test track for testing the global player functionality",
  style: "Electronic",
  mood: "Energetic",
  bpm: 128,
  duration: 180, // 3 minutes
  file_url: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav", // Public test audio
  file_format: "wav" as const,
  download_count: 42,
  is_public: true,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
};

function TestPlayerFeaturesPage() {
  const { data: session } = useSession();
  const { currentTrack, isPlaying, play, stop } = useAudioPlayer();
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const runTests = async () => {
    setTestResults([]);
    addTestResult("Starting global player feature tests...");

    // Test 1: Load track
    try {
      await play(mockTrack);
      addTestResult("✅ Track loading successful");
    } catch (error) {
      addTestResult("❌ Track loading failed: " + error);
    }

    // Test 2: Check if global player appears
    setTimeout(() => {
      if (currentTrack) {
        addTestResult("✅ Global player appeared with track");
      } else {
        addTestResult("❌ Global player did not appear");
      }
    }, 1000);

    // Test 3: Test keyboard shortcuts
    addTestResult("🎹 Test keyboard shortcuts manually:");
    addTestResult("  - Space: Play/Pause");
    addTestResult("  - ← →: Seek backward/forward");
    addTestResult("  - ↑ ↓: Volume up/down");
    addTestResult("  - M: Toggle mute");
    addTestResult("  - L: Toggle loop");

    // Test 4: Test buttons
    addTestResult("🖱️ Test buttons manually:");
    addTestResult("  - Volume control (hover for slider)");
    addTestResult("  - Favorite button (heart icon)");
    addTestResult("  - Share button (dropdown menu)");
    addTestResult("  - Download button");
    addTestResult("  - Loop button");
    addTestResult("  - Close button");

    addTestResult("✨ All automated tests completed!");
  };

  const clearTests = () => {
    setTestResults([]);
    if (currentTrack) {
      stop();
    }
  };

  return (
    <div className="container mx-auto p-8 space-y-6">
      <Card className="p-6">
        <h1 className="text-2xl font-bold mb-4 flex items-center gap-2">
          <TestTube className="h-6 w-6" />
          Global Player Feature Test
        </h1>
        
        <div className="space-y-4">
          <div>
            <h3 className="font-semibold mb-2">Session Status:</h3>
            <Badge variant={session ? "default" : "secondary"}>
              {session ? `Logged in as ${session.user?.email}` : "Not logged in"}
            </Badge>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Current Track:</h3>
            <Badge variant={currentTrack ? "default" : "secondary"}>
              {currentTrack ? currentTrack.title : "No track loaded"}
            </Badge>
          </div>

          <div className="flex gap-2">
            <Button onClick={runTests} className="flex items-center gap-2">
              <Play className="h-4 w-4" />
              Run Tests
            </Button>
            <Button onClick={clearTests} variant="outline">
              Clear & Stop
            </Button>
          </div>
        </div>
      </Card>

      {testResults.length > 0 && (
        <Card className="p-6">
          <h2 className="text-lg font-semibold mb-4">Test Results:</h2>
          <div className="space-y-1 max-h-96 overflow-y-auto">
            {testResults.map((result, index) => (
              <div key={index} className="text-sm font-mono bg-muted p-2 rounded">
                {result}
              </div>
            ))}
          </div>
        </Card>
      )}

      <Card className="p-6">
        <h2 className="text-lg font-semibold mb-4">Manual Testing Instructions:</h2>
        <div className="space-y-2 text-sm">
          <p><strong>1. Load Test Track:</strong> Click "Run Tests" to load a test audio track</p>
          <p><strong>2. Global Player:</strong> The global player should appear at the bottom of the screen</p>
          <p><strong>3. Keyboard Shortcuts:</strong> Test all keyboard shortcuts listed in the results</p>
          <p><strong>4. Button Functions:</strong></p>
          <ul className="ml-4 space-y-1">
            <li>• <strong>Play/Pause:</strong> Should toggle playback</li>
            <li>• <strong>Volume:</strong> Hover to show slider, click to mute</li>
            <li>• <strong>Favorite:</strong> Should show heart icon, click to toggle</li>
            <li>• <strong>Share:</strong> Should show dropdown with copy/social options</li>
            <li>• <strong>Download:</strong> Should trigger file download</li>
            <li>• <strong>Loop:</strong> Should toggle repeat mode</li>
            <li>• <strong>Close:</strong> Should hide the global player</li>
          </ul>
          <p><strong>5. Responsive Design:</strong> Test on different screen sizes</p>
          <p><strong>6. Error Handling:</strong> Check browser console for any errors</p>
        </div>
      </Card>
    </div>
  );
}

// Export with dynamic import to prevent SSR
export default dynamic(() => Promise.resolve(TestPlayerFeaturesPage), {
  ssr: false,
  loading: () => <div className="container mx-auto p-8">Loading test page...</div>
});