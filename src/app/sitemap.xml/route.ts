import { NextResponse } from "next/server";
import { SitemapIndexGenerator } from "@/services/sitemap-index-generator";
import { URLBuilder } from "@/services/sitemap-url-builder";
import { XMLSerializer } from "@/services/sitemap-xml-serializer";
import { SitemapCacheManager, MemorySitemapCache } from "@/services/sitemap-cache";
import { SITEMAP_CONFIG, SITEMAP_HEADERS } from "@/constants/sitemap";
import { extractLocaleFromPath } from "@/lib/sitemap-utils";

// Initialize sitemap components
const urlBuilder = new URLBuilder({
  baseUrl: SITEMAP_CONFIG.baseUrl,
  locales: SITEMAP_CONFIG.locales,
  defaultLocale: SITEMAP_CONFIG.defaultLocale,
  localePrefix: "as-needed",
});

const xmlSerializer = new XMLSerializer({
  includeHreflang: SITEMAP_CONFIG.enableHreflang,
  prettyPrint: process.env.NODE_ENV === "development",
  validateXML: true,
});

const cacheManager = new SitemapCacheManager(new MemorySitemapCache());
const sitemapGenerator = new SitemapIndexGenerator(urlBuilder, xmlSerializer, cacheManager);

export async function GET(request: Request) {
  const startTime = Date.now();
  
  try {
    // Extract locale from request URL or use default
    const url = new URL(request.url);
    const locale = extractLocaleFromPath(url.pathname, SITEMAP_CONFIG.locales) || SITEMAP_CONFIG.defaultLocale;
    
    // Log request for monitoring
    console.log(`[Sitemap XML] Generating main sitemap for locale: ${locale}`);
    
    // Check if we should use cached version based on request headers
    const ifModifiedSince = request.headers.get('if-modified-since');
    if (ifModifiedSince) {
      const lastModified = await getLastModifiedDate();
      const ifModifiedSinceDate = new Date(ifModifiedSince);
      
      if (lastModified <= ifModifiedSinceDate) {
        return new NextResponse(null, {
          status: 304,
          headers: {
            "Last-Modified": lastModified.toUTCString(),
            "Cache-Control": `public, max-age=${SITEMAP_CONFIG.cacheTimeout}`,
          },
        });
      }
    }
    
    // Generate main sitemap (could be index or single sitemap based on content size)
    const result = await sitemapGenerator.generateSitemap(locale);
    
    if (!result.isValid) {
      console.error("[Sitemap XML] Generated invalid XML:", result.validationErrors);
      throw new Error(`Generated sitemap XML is invalid: ${result.validationErrors?.join(", ")}`);
    }

    // Log generation metrics
    const totalTime = Date.now() - startTime;
    console.log(`[Sitemap XML] Generated successfully in ${totalTime}ms with ${result.urlCount} URLs`);

    // Return XML response with proper headers
    return new NextResponse(result.xml, {
      status: 200,
      headers: {
        "Content-Type": SITEMAP_HEADERS.CONTENT_TYPE,
        "Cache-Control": `public, max-age=${SITEMAP_CONFIG.cacheTimeout}, s-maxage=${SITEMAP_CONFIG.cacheTimeout}`,
        "X-Sitemap-Type": "main",
        "X-Sitemap-URLs": result.urlCount.toString(),
        "X-Generation-Time": totalTime.toString(),
        "X-Locale": locale,
        "Vary": SITEMAP_HEADERS.VARY,
        "Last-Modified": new Date().toUTCString(),
      },
    });
  } catch (error) {
    const totalTime = Date.now() - startTime;
    
    // Log error with context
    console.error("[Sitemap XML] Generation failed:", {
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
      generationTime: totalTime,
      url: request.url,
    });

    // Return a minimal sitemap as fallback
    const minimalSitemap = generateMinimalSitemap();
    return new NextResponse(minimalSitemap, {
      status: 200,
      headers: {
        "Content-Type": SITEMAP_HEADERS.CONTENT_TYPE,
        "Cache-Control": "public, max-age=300, s-maxage=300",
        "X-Sitemap-Minimal": "true",
        "X-Generation-Time": totalTime.toString(),
        "Vary": SITEMAP_HEADERS.VARY,
      },
    });
  }
}

/**
 * Gets the last modified date for the main sitemap
 */
async function getLastModifiedDate(): Promise<Date> {
  try {
    const stats = await sitemapGenerator.getGenerationStats();
    const dates = Object.values(stats.lastModifiedDates);
    
    if (dates.length === 0) {
      return new Date();
    }
    
    // Return the most recent date
    return dates.reduce((latest, current) => current > latest ? current : latest);
  } catch (error) {
    console.error("[Sitemap XML] Error getting last modified date:", error);
    return new Date();
  }
}

/**
 * Generates a minimal sitemap as fallback
 */
function generateMinimalSitemap(): string {
  return xmlSerializer.createMinimalSitemap(SITEMAP_CONFIG.baseUrl);
}

/**
 * Health check endpoint for main sitemap
 */
export async function HEAD() {
  try {
    const stats = await sitemapGenerator.getGenerationStats();
    const totalUrls = Object.values(stats.estimatedCounts).reduce((sum, count) => sum + count, 0);
    
    return new NextResponse(null, {
      status: 200,
      headers: {
        "X-Sitemap-Health": "ok",
        "X-Content-Type": "main",
        "X-Estimated-URLs": totalUrls.toString(),
        "X-Should-Use-Index": stats.shouldUseIndex.toString(),
        "X-Cache-Hit-Rate": stats.cacheStats?.hitRate?.toString() || "0",
      },
    });
  } catch (error) {
    return new NextResponse(null, {
      status: 503,
      headers: {
        "X-Sitemap-Health": "error",
        "X-Content-Type": "main",
        "X-Error": error instanceof Error ? error.message : "Unknown error",
      },
    });
  }
}