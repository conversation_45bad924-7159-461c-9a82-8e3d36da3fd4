/**
 * CDN 测试页面
 */

"use client";

import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';

interface CDNTestResult {
  isConfigured: boolean;
  config?: {
    baseUrl: string;
    secretKeyLength: number;
    defaultExpiryHours: number;
  };
  testUrl?: string;
  error?: string;
}

export default function CDNTestPage() {
  const [testResult, setTestResult] = useState<CDNTestResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const runCDNTest = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/debug/cdn-test');
      const data = await response.json();
      
      if (response.ok) {
        setTestResult(data);
        toast.success('CDN test completed successfully!');
      } else {
        setTestResult({
          isConfigured: false,
          error: data.message || 'Test failed'
        });
        toast.error('CDN test failed');
      }
    } catch (error) {
      setTestResult({
        isConfigured: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      toast.error('Failed to run CDN test');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-2xl mx-auto space-y-6">
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold">CDN Configuration Test</h1>
          <p className="text-muted-foreground">
            Test your Cloudflare CDN configuration for audio downloads
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Test CDN Configuration</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={runCDNTest} 
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-background border-t-transparent mr-2" />
                  Testing...
                </>
              ) : (
                'Run CDN Test'
              )}
            </Button>

            {testResult && (
              <div className="space-y-4 pt-4 border-t">
                <div className="flex items-center gap-2">
                  <span className="font-medium">Status:</span>
                  <Badge variant={testResult.isConfigured ? "default" : "destructive"}>
                    {testResult.isConfigured ? "✅ Configured" : "❌ Not Configured"}
                  </Badge>
                </div>

                {testResult.isConfigured && testResult.config && (
                  <div className="space-y-2">
                    <h4 className="font-medium">Configuration Details:</h4>
                    <div className="bg-muted/50 p-3 rounded-lg space-y-1 text-sm">
                      <div><strong>Base URL:</strong> {testResult.config.baseUrl}</div>
                      <div><strong>Secret Key Length:</strong> {testResult.config.secretKeyLength} characters</div>
                      <div><strong>Default Expiry:</strong> {testResult.config.defaultExpiryHours} hours</div>
                    </div>
                  </div>
                )}

                {testResult.testUrl && (
                  <div className="space-y-2">
                    <h4 className="font-medium">Test Download URL:</h4>
                    <div className="bg-muted/50 p-3 rounded-lg">
                      <code className="text-xs break-all">{testResult.testUrl}</code>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      This is a sample secure download URL that would be generated for audio files.
                    </p>
                  </div>
                )}

                {testResult.error && (
                  <div className="space-y-2">
                    <h4 className="font-medium text-destructive">Error:</h4>
                    <div className="bg-destructive/10 p-3 rounded-lg">
                      <p className="text-sm text-destructive">{testResult.error}</p>
                    </div>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Environment Variables</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <p>Make sure these environment variables are set:</p>
              <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                <li><code>CDN_BASE_URL</code> - Your Cloudflare Worker URL</li>
                <li><code>CDN_SECRET_KEY</code> - Secret key for token generation</li>
                <li><code>CDN_DEFAULT_EXPIRY_HOURS</code> - Default expiry time (optional)</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}