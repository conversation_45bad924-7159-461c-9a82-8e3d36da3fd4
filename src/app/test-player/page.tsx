"use client";

import AudioPlayer from "@/components/music/player/audio-player";
import { AudioPlayerProvider } from "@/contexts/audio-player-context";
import GlobalAudioPlayer from "@/components/music/player/global-audio-player";

// Prevent SSR for this test page
import dynamic from "next/dynamic";

// Mock track data for testing
const mockTrack = {
  uuid: "test-track-123",
  title: "Test Loop Track",
  file_url: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav", // Public test audio
  duration: 30,
  style: "Electronic",
  mood: "Energetic",
  bpm: 120,
  waveform_data: {
    peaks: Array.from({ length: 100 }, (_, i) => Math.sin(i * 0.1) * 0.5 + 0.5),
    duration: 30,
    sample_rate: 44100,
  },
};

function TestPlayerPage() {
  return (
    <AudioPlayerProvider>
      <div className="container mx-auto p-8 space-y-8">
        <h1 className="text-2xl font-bold">Audio Player Test</h1>
        
        <div className="space-y-4">
          <h2 className="text-lg font-semibold">Individual Audio Player</h2>
          <AudioPlayer 
            track={mockTrack}
            showWaveform={true}
            showDownload={true}
          />
        </div>

        <div className="space-y-4">
          <h2 className="text-lg font-semibold">Instructions</h2>
          <div className="bg-muted p-4 rounded-lg">
            <ol className="list-decimal list-inside space-y-2">
              <li>点击播放按钮开始播放音频</li>
              <li>在音频播放过程中，点击循环播放按钮（Repeat图标）</li>
              <li>验证音频是否继续播放而不会停止</li>
              <li>验证循环播放功能是否正常工作</li>
            </ol>
          </div>
        </div>
      </div>
      
      <GlobalAudioPlayer />
    </AudioPlayerProvider>
  );
}

// Export with dynamic import to prevent SSR
export default dynamic(() => Promise.resolve(TestPlayerPage), {
  ssr: false,
  loading: () => <div className="container mx-auto p-8">Loading test page...</div>
});