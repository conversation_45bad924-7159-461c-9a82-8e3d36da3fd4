import type { SitemapConfig, StaticPageConfig } from "@/types/sitemap";

// Sitemap Configuration Constants
export const SITEMAP_CONFIG: SitemapConfig = {
  maxUrlsPerSitemap: 50000, // Google's recommended limit
  maxSitemapSize: 52428800, // 50MB in bytes
  baseUrl: process.env.NEXT_PUBLIC_WEB_URL || "https://loopcraft.app",
  locales: ["en", "zh"],
  defaultLocale: "en",
  cacheTimeout: 3600, // 1 hour in seconds
  enableSitemapIndex: true,
  enableHreflang: true,
  priorities: {
    static: 0.8,
    posts: 0.6,
    loops: 0.7,
  },
  changeFreq: {
    static: "weekly" as const,
    posts: "monthly" as const,
    loops: "weekly" as const,
  },
};

// Cache Configuration
export const SITEMAP_CACHE_CONFIG = {
  // Cache timeouts by content type (in seconds)
  STATIC_PAGES_CACHE_TIMEOUT: 86400, // 24 hours
  BLOG_POSTS_CACHE_TIMEOUT: 3600, // 1 hour
  MUSIC_LOOPS_CACHE_TIMEOUT: 1800, // 30 minutes
  SITEMAP_INDEX_CACHE_TIMEOUT: 3600, // 1 hour
  
  // Cache key prefixes
  CACHE_KEY_PREFIX: "sitemap:",
  STATIC_CACHE_KEY: "sitemap:static",
  POSTS_CACHE_KEY: "sitemap:posts",
  LOOPS_CACHE_KEY: "sitemap:loops",
  INDEX_CACHE_KEY: "sitemap:index",
} as const;

// Static Pages Configuration
export const STATIC_PAGES: StaticPageConfig[] = [
  {
    path: "",
    priority: "1.0",
    changefreq: "daily",
  },
  {
    path: "generate",
    priority: "0.9",
    changefreq: "daily",
  },
  {
    path: "explore",
    priority: "0.8",
    changefreq: "daily",
  },
  {
    path: "showcase",
    priority: "0.8",
    changefreq: "weekly",
  },
  {
    path: "posts",
    priority: "0.7",
    changefreq: "weekly",
  },
  {
    path: "pricing",
    priority: "0.6",
    changefreq: "monthly",
  },
];

// Content Type Priorities and Change Frequencies
export const CONTENT_TYPE_CONFIG = {
  STATIC_PAGES: {
    priority: "0.8",
    changefreq: "weekly" as const,
  },
  BLOG_POSTS: {
    priority: "0.6",
    changefreq: "monthly" as const,
  },
  MUSIC_LOOPS: {
    priority: "0.7",
    changefreq: "weekly" as const,
  },
} as const;

// XML Generation Constants
export const XML_CONSTANTS = {
  XML_HEADER: '<?xml version="1.0" encoding="UTF-8"?>',
  SITEMAP_NAMESPACE: "http://www.sitemaps.org/schemas/sitemap/0.9",
  HREFLANG_NAMESPACE: "http://www.w3.org/1999/xhtml",
  MAX_URL_LENGTH: 2048,
  MAX_SITEMAP_SIZE: 52428800, // 50MB in bytes
} as const;

// Error Messages
export const SITEMAP_ERROR_MESSAGES = {
  GENERATION_FAILED: "Failed to generate sitemap",
  CACHE_ERROR: "Sitemap cache operation failed",
  VALIDATION_ERROR: "Sitemap validation failed",
  DATABASE_ERROR: "Database query failed during sitemap generation",
  XML_GENERATION_ERROR: "Failed to generate XML sitemap",
  URL_TOO_LONG: "URL exceeds maximum length limit",
  SITEMAP_TOO_LARGE: "Sitemap exceeds maximum size limit",
} as const;

// Performance Thresholds
export const PERFORMANCE_THRESHOLDS = {
  MAX_GENERATION_TIME: 30000, // 30 seconds
  WARNING_GENERATION_TIME: 10000, // 10 seconds
  MAX_CACHE_SIZE: 100, // Maximum number of cached sitemaps
  BATCH_SIZE: 1000, // Number of URLs to process in each batch
} as const;

// Monitoring and Health Check Constants
export const HEALTH_CHECK_CONFIG = {
  TIMEOUT: 5000, // 5 seconds
  RETRY_ATTEMPTS: 3,
  HEALTHY_RESPONSE_TIME: 1000, // 1 second
  DEGRADED_RESPONSE_TIME: 5000, // 5 seconds
} as const;

// URL Validation Patterns
export const URL_VALIDATION = {
  ALLOWED_PROTOCOLS: ["http:", "https:"],
  DISALLOWED_PATHS: ["/api/", "/admin/", "/my-music", "/my-orders"],
  REQUIRED_EXTENSIONS: [], // Empty means all extensions allowed
  MAX_DEPTH: 10, // Maximum URL path depth
} as const;

// HTTP Headers for Sitemap Responses
export const SITEMAP_HEADERS = {
  CONTENT_TYPE: "application/xml; charset=utf-8",
  CACHE_CONTROL: "public, max-age=3600, s-maxage=3600",
  VARY: "Accept-Encoding",
} as const;