import {
  MusicGenerationRequest,
  MusicGenerationResult,
  MusicGenerationOptions,
  MusicModelConfig,
  MusicGenerationError,
} from "../types";
import { MusicModel } from "../music-model";

// Base provider class with common functionality
export abstract class BaseMusicProvider extends MusicModel {
  protected abstract readonly providerName: string;
  protected abstract readonly supportedDurations: number[];
  protected abstract readonly supportedStyles: string[];
  protected abstract readonly maxBpm: number;
  protected abstract readonly minBpm: number;

  constructor(config: MusicModelConfig) {
    super(config);
  }

  // Abstract methods that must be implemented by providers
  abstract generateMusic(
    request: MusicGenerationRequest,
    options?: MusicGenerationOptions
  ): Promise<MusicGenerationResult>;

  abstract checkGenerationStatus(task_id: string): Promise<MusicGenerationResult>;

  abstract cancelGeneration(task_id: string): Promise<boolean>;

  // Common validation logic
  protected validateRequest(request: MusicGenerationRequest): void {
    super.validateRequest(request);

    // Provider-specific validations
    if (!this.supportedDurations.includes(request.duration)) {
      throw new MusicGenerationError(
        `Duration ${request.duration}s not supported by ${this.providerName}. Supported: ${this.supportedDurations.join(", ")}s`,
        "UNSUPPORTED_DURATION",
        this.providerName
      );
    }

    if (request.bpm && (request.bpm < this.minBpm || request.bpm > this.maxBpm)) {
      throw new MusicGenerationError(
        `BPM ${request.bpm} not supported by ${this.providerName}. Range: ${this.minBpm}-${this.maxBpm}`,
        "UNSUPPORTED_BPM",
        this.providerName
      );
    }

    if (request.style && !this.supportedStyles.includes(request.style.toLowerCase())) {
      throw new MusicGenerationError(
        `Style "${request.style}" not supported by ${this.providerName}. Supported: ${this.supportedStyles.join(", ")}`,
        "UNSUPPORTED_STYLE",
        this.providerName
      );
    }
  }

  // Common request preparation
  protected prepareRequest(request: MusicGenerationRequest, options?: MusicGenerationOptions) {
    return {
      prompt: request.prompt.trim(),
      duration: request.duration,
      style: request.style?.toLowerCase(),
      mood: request.mood?.toLowerCase(),
      bpm: request.bpm,
      seed: request.seed || options?.seed,
      temperature: request.temperature || options?.temperature || 0.7,
      guidance_scale: request.guidance_scale || options?.guidance_scale || 7.5,
      quality: options?.quality || "standard",
      format: options?.format || "mp3",
      negative_prompt: options?.negative_prompt,
    };
  }

  // Common response processing
  protected processResponse(response: any, task_id: string): MusicGenerationResult {
    return {
      task_id,
      status: this.mapStatus(response.status),
      progress: response.progress,
      estimated_completion_time: response.estimated_time,
      file_url: response.file_url,
      file_size: response.file_size,
      metadata: response.metadata ? this.processMetadata(response.metadata) : undefined,
      error: response.error,
    };
  }

  // Map provider-specific status to standard status
  protected mapStatus(providerStatus: string): "pending" | "processing" | "completed" | "failed" {
    const statusMap: Record<string, "pending" | "processing" | "completed" | "failed"> = {
      // Common statuses
      "pending": "pending",
      "processing": "processing",
      "completed": "completed",
      "failed": "failed",
      "error": "failed",
      
      // Provider-specific mappings
      "queued": "pending",
      "running": "processing",
      "success": "completed",
      "finished": "completed",
      "done": "completed",
      "cancelled": "failed",
      "timeout": "failed",
    };

    return statusMap[providerStatus.toLowerCase()] || "pending";
  }

  // Process metadata into standard format
  protected processMetadata(metadata: any) {
    return {
      duration: metadata.duration || 0,
      format: metadata.format || "mp3",
      sample_rate: metadata.sample_rate || 44100,
      bit_rate: metadata.bit_rate || 128000,
      channels: metadata.channels || 2,
      bpm: metadata.bpm,
      key: metadata.key,
      genre: metadata.genre,
      energy_level: metadata.energy_level,
      danceability: metadata.danceability,
      valence: metadata.valence,
    };
  }

  // Common error handling
  protected handleError(error: any, task_id?: string): never {
    if (error instanceof MusicGenerationError) {
      throw error;
    }

    let message = "Unknown error occurred";
    let code = "UNKNOWN_ERROR";

    if (error.response) {
      // HTTP error
      message = error.response.data?.message || `HTTP ${error.response.status}: ${error.response.statusText}`;
      code = error.response.data?.code || "HTTP_ERROR";
    } else if (error.message) {
      message = error.message;
      code = error.code || "REQUEST_ERROR";
    }

    throw new MusicGenerationError(message, code, this.providerName, task_id);
  }

  // Get provider capabilities
  getCapabilities() {
    return {
      provider: this.providerName,
      supported_durations: this.supportedDurations,
      supported_styles: this.supportedStyles,
      bpm_range: { min: this.minBpm, max: this.maxBpm },
      supports_streaming: !!this.generateMusicStream,
      supports_analysis: !!this.analyzeAudio,
      supports_loop_verification: !!this.verifyLoop,
      supports_stem_separation: !!this.separateStems,
      supports_variations: !!this.generateVariation,
    };
  }

  // Common logging
  protected log(level: "info" | "warn" | "error", message: string, data?: any) {
    switch (level) {
      case "info":
        console.log(`[${this.providerName}]`, message, data || "");
        break;
      case "warn":
        console.warn(`[${this.providerName}]`, message, data || "");
        break;
      case "error":
        console.error(`[${this.providerName}]`, message, data || "");
        break;
    }
  }
}
