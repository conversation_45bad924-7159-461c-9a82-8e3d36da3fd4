"use client";

import React, { createContext, useContext, useRef, useState, useEffect, ReactNode } from "react";
import { Track } from "@/types/music";

interface AudioPlayerState {
  currentTrack: Track | null;
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  isMuted: boolean;
  isLooping: boolean;
  isLoading: boolean;
}

interface AudioPlayerActions {
  play: (track: Track) => Promise<void>;
  pause: () => void;
  resume: () => void;
  stop: () => void;
  seek: (time: number) => void;
  setVolume: (volume: number) => void;
  toggleMute: () => void;
  toggleLoop: () => void;
}

interface AudioPlayerContextType extends AudioPlayerState, AudioPlayerActions {}

const AudioPlayerContext = createContext<AudioPlayerContextType | undefined>(undefined);

interface AudioPlayerProviderProps {
  children: ReactNode;
}

export function AudioPlayerProvider({ children }: AudioPlayerProviderProps) {
  const audioRef = useRef<HTMLAudioElement | null>(null);
  
  const [state, setState] = useState<AudioPlayerState>({
    currentTrack: null,
    isPlaying: false,
    currentTime: 0,
    duration: 0,
    volume: 1,
    isMuted: false,
    isLooping: false,
    isLoading: false,
  });

  // Initialize audio element
  useEffect(() => {
    const audio = new Audio();
    audio.preload = "metadata";
    audioRef.current = audio;

    const handleTimeUpdate = () => {
      setState(prev => ({ ...prev, currentTime: audio.currentTime }));
    };

    const handleLoadedMetadata = () => {
      setState(prev => ({ ...prev, duration: audio.duration, isLoading: false }));
    };

    const handleLoadStart = () => {
      setState(prev => ({ ...prev, isLoading: true }));
    };

    const handleCanPlay = () => {
      setState(prev => ({ ...prev, isLoading: false }));
    };

    const handleEnded = () => {
      setState(prev => ({ 
        ...prev, 
        isPlaying: false,
        // Don't manually reset currentTime for looping tracks as the browser handles it
        currentTime: audio.loop ? prev.currentTime : 0
      }));
    };

    const handleError = () => {
      setState(prev => ({ ...prev, isLoading: false, isPlaying: false }));
      console.error("Audio playback error");
    };

    audio.addEventListener("timeupdate", handleTimeUpdate);
    audio.addEventListener("loadedmetadata", handleLoadedMetadata);
    audio.addEventListener("loadstart", handleLoadStart);
    audio.addEventListener("canplay", handleCanPlay);
    audio.addEventListener("ended", handleEnded);
    audio.addEventListener("error", handleError);

    return () => {
      audio.removeEventListener("timeupdate", handleTimeUpdate);
      audio.removeEventListener("loadedmetadata", handleLoadedMetadata);
      audio.removeEventListener("loadstart", handleLoadStart);
      audio.removeEventListener("canplay", handleCanPlay);
      audio.removeEventListener("ended", handleEnded);
      audio.removeEventListener("error", handleError);
      audio.pause();
    };
  }, []); // Remove state.isLooping dependency

  // Update audio properties when state changes
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.volume = state.isMuted ? 0 : state.volume;
    audio.loop = state.isLooping;
  }, [state.volume, state.isMuted, state.isLooping]);

  const play = async (track: Track) => {
    const audio = audioRef.current;
    if (!audio) return;

    try {
      // If same track is already playing, just resume
      if (state.currentTrack?.uuid === track.uuid && audio.src) {
        if (audio.paused) {
          await audio.play();
          setState(prev => ({ ...prev, isPlaying: true }));
        }
        return;
      }

      // Load new track
      setState(prev => ({ 
        ...prev, 
        currentTrack: track, 
        isLoading: true,
        currentTime: 0 
      }));

      audio.src = track.file_url;
      audio.load();
      
      // Wait for audio to be ready and play
      await audio.play();
      setState(prev => ({ ...prev, isPlaying: true }));
    } catch (error) {
      console.error("Failed to play audio:", error);
      setState(prev => ({ ...prev, isLoading: false, isPlaying: false }));
    }
  };

  const pause = () => {
    const audio = audioRef.current;
    if (audio && !audio.paused) {
      audio.pause();
      setState(prev => ({ ...prev, isPlaying: false }));
    }
  };

  const resume = () => {
    const audio = audioRef.current;
    if (audio && audio.paused && state.currentTrack) {
      audio.play().then(() => {
        setState(prev => ({ ...prev, isPlaying: true }));
      }).catch(error => {
        console.error("Failed to resume audio:", error);
      });
    }
  };

  const stop = () => {
    const audio = audioRef.current;
    if (audio) {
      audio.pause();
      audio.currentTime = 0;
      setState(prev => ({ 
        ...prev, 
        isPlaying: false, 
        currentTime: 0,
        currentTrack: null 
      }));
    }
  };

  const seek = (time: number) => {
    const audio = audioRef.current;
    if (audio && state.duration > 0) {
      const seekTime = Math.max(0, Math.min(time, state.duration));
      audio.currentTime = seekTime;
      setState(prev => ({ ...prev, currentTime: seekTime }));
    }
  };

  const setVolume = (volume: number) => {
    const clampedVolume = Math.max(0, Math.min(1, volume));
    setState(prev => ({ ...prev, volume: clampedVolume, isMuted: false }));
  };

  const toggleMute = () => {
    setState(prev => ({ ...prev, isMuted: !prev.isMuted }));
  };

  const toggleLoop = () => {
    const audio = audioRef.current;
    if (!audio) return;
    
    setState(prev => {
      const newLoopState = !prev.isLooping;
      audio.loop = newLoopState;
      return { ...prev, isLooping: newLoopState };
    });
  };

  const contextValue: AudioPlayerContextType = {
    ...state,
    play,
    pause,
    resume,
    stop,
    seek,
    setVolume,
    toggleMute,
    toggleLoop,
  };

  return (
    <AudioPlayerContext.Provider value={contextValue}>
      {children}
    </AudioPlayerContext.Provider>
  );
}

export function useAudioPlayer() {
  const context = useContext(AudioPlayerContext);
  if (context === undefined) {
    throw new Error("useAudioPlayer must be used within an AudioPlayerProvider");
  }
  return context;
}
