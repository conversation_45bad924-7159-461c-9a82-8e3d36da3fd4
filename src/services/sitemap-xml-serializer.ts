import type { 
  SitemapEntry, 
  SitemapIndexEntry, 
  XMLGenerationOptions,
  SitemapXMLResult 
} from "@/types/sitemap";
import { XML_CONSTANTS } from "@/constants/sitemap";
import { 
  escapeXML, 
  validateSitemap, 
  measureExecutionTime 
} from "@/lib/sitemap-utils";

/**
 * XML Serializer for Sitemap Generation
 * Converts sitemap entries to valid XML format according to sitemap protocol
 */
export class XMLSerializer {
  private options: XMLGenerationOptions;

  constructor(options?: Partial<XMLGenerationOptions>) {
    this.options = {
      includeHreflang: true,
      prettyPrint: false,
      validateXML: true,
      ...options,
    };
  }

  /**
   * Serializes sitemap entries to XML format
   */
  async serializeSitemap(entries: SitemapEntry[]): Promise<SitemapXMLResult> {
    const { result: xml, executionTime } = await measureExecutionTime(async () => {
      return this.generateSitemapXML(entries);
    });

    let isValid = true;
    let validationErrors: string[] = [];

    if (this.options.validateXML) {
      const validation = validateSitemap(entries);
      isValid = validation.isValid;
      validationErrors = validation.errors;
    }

    return {
      xml,
      urlCount: entries.length,
      generationTime: executionTime,
      isValid,
      validationErrors: validationErrors.length > 0 ? validationErrors : undefined,
    };
  }

  /**
   * Serializes sitemap index entries to XML format
   */
  async serializeSitemapIndex(entries: SitemapIndexEntry[]): Promise<SitemapXMLResult> {
    const { result: xml, executionTime } = await measureExecutionTime(async () => {
      return this.generateSitemapIndexXML(entries);
    });

    let isValid = true;
    let validationErrors: string[] = [];

    if (this.options.validateXML) {
      const validation = this.validateSitemapIndexXML(xml);
      isValid = validation.isValid;
      validationErrors = validation.errors;
    }

    return {
      xml,
      urlCount: entries.length,
      generationTime: executionTime,
      isValid,
      validationErrors: validationErrors.length > 0 ? validationErrors : undefined,
    };
  }

  /**
   * Generates XML for regular sitemap
   */
  private generateSitemapXML(entries: SitemapEntry[]): string {
    const indent = this.options.prettyPrint ? "  " : "";
    const newline = this.options.prettyPrint ? "\n" : "";
    
    let xml = XML_CONSTANTS.XML_HEADER + newline;
    
    // Add urlset opening tag with namespaces
    xml += `<urlset xmlns="${XML_CONSTANTS.SITEMAP_NAMESPACE}"`;
    if (this.options.includeHreflang) {
      xml += ` xmlns:xhtml="${XML_CONSTANTS.HREFLANG_NAMESPACE}"`;
    }
    xml += `>${newline}`;

    // Add URL entries
    for (const entry of entries) {
      xml += this.generateUrlEntry(entry, indent, newline);
    }

    // Close urlset
    xml += `</urlset>${newline}`;

    return xml;
  }

  /**
   * Generates XML for sitemap index
   */
  private generateSitemapIndexXML(entries: SitemapIndexEntry[]): string {
    const indent = this.options.prettyPrint ? "  " : "";
    const newline = this.options.prettyPrint ? "\n" : "";
    
    let xml = XML_CONSTANTS.XML_HEADER + newline;
    
    // Add sitemapindex opening tag
    xml += `<sitemapindex xmlns="${XML_CONSTANTS.SITEMAP_NAMESPACE}">${newline}`;

    // Add sitemap entries
    for (const entry of entries) {
      xml += `${indent}<sitemap>${newline}`;
      xml += `${indent}${indent}<loc>${escapeXML(entry.loc)}</loc>${newline}`;
      xml += `${indent}${indent}<lastmod>${entry.lastmod}</lastmod>${newline}`;
      xml += `${indent}</sitemap>${newline}`;
    }

    // Close sitemapindex
    xml += `</sitemapindex>${newline}`;

    return xml;
  }

  /**
   * Generates a single URL entry XML
   */
  private generateUrlEntry(entry: SitemapEntry, indent: string, newline: string): string {
    let xml = `${indent}<url>${newline}`;
    
    // Required elements
    xml += `${indent}${indent}<loc>${escapeXML(entry.url)}</loc>${newline}`;
    xml += `${indent}${indent}<lastmod>${entry.lastmod}</lastmod>${newline}`;
    xml += `${indent}${indent}<changefreq>${entry.changefreq}</changefreq>${newline}`;
    xml += `${indent}${indent}<priority>${entry.priority}</priority>${newline}`;

    // Add hreflang alternates if enabled and available
    if (this.options.includeHreflang && entry.alternates && entry.alternates.length > 0) {
      for (const alternate of entry.alternates) {
        xml += `${indent}${indent}<xhtml:link rel="alternate" hreflang="${alternate.hreflang}" href="${escapeXML(alternate.href)}" />${newline}`;
      }
    }

    xml += `${indent}</url>${newline}`;
    
    return xml;
  }

  /**
   * Validates sitemap index XML structure
   */
  private validateSitemapIndexXML(xml: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Basic XML structure validation
    if (!xml.includes(XML_CONSTANTS.XML_HEADER)) {
      errors.push('Missing XML declaration');
    }
    
    if (!xml.includes('<sitemapindex')) {
      errors.push('Missing sitemapindex element');
    }
    
    if (!xml.includes(XML_CONSTANTS.SITEMAP_NAMESPACE)) {
      errors.push('Missing sitemap namespace');
    }
    
    // Check for proper closing tags
    const openTags = (xml.match(/<[^\/][^>]*>/g) || []).length;
    const closeTags = (xml.match(/<\/[^>]*>/g) || []).length;
    const selfClosingTags = (xml.match(/<[^>]*\/>/g) || []).length;
    
    if (openTags !== closeTags + selfClosingTags) {
      errors.push('Mismatched XML tags');
    }
    
    // Validate sitemap URLs within XML
    const locMatches = xml.match(/<loc>(.*?)<\/loc>/g);
    if (locMatches) {
      locMatches.forEach((match, index) => {
        const url = match.replace(/<\/?loc>/g, '');
        try {
          new URL(url);
        } catch {
          errors.push(`Invalid sitemap URL at position ${index}: ${url}`);
        }
      });
    }
    
    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Estimates the size of generated XML in bytes
   */
  estimateXMLSize(entries: SitemapEntry[]): number {
    const baseSize = 200; // XML header and wrapper elements
    const averageEntrySize = 200; // Average size per URL entry
    const hreflangSize = this.options.includeHreflang ? 100 : 0; // Additional size for hreflang
    
    return baseSize + (entries.length * (averageEntrySize + hreflangSize));
  }

  /**
   * Checks if XML would exceed size limits
   */
  wouldExceedSizeLimit(entries: SitemapEntry[]): boolean {
    const estimatedSize = this.estimateXMLSize(entries);
    return estimatedSize > XML_CONSTANTS.MAX_SITEMAP_SIZE;
  }

  /**
   * Splits entries into chunks that won't exceed size limits
   */
  chunkEntriesForSize(entries: SitemapEntry[]): SitemapEntry[][] {
    const chunks: SitemapEntry[][] = [];
    let currentChunk: SitemapEntry[] = [];
    
    for (const entry of entries) {
      currentChunk.push(entry);
      
      // Check if adding this entry would exceed the size limit
      if (this.wouldExceedSizeLimit(currentChunk)) {
        // Remove the last entry and start a new chunk with it
        const lastEntry = currentChunk.pop()!;
        if (currentChunk.length > 0) {
          chunks.push([...currentChunk]);
        }
        currentChunk = [lastEntry];
      }
    }
    
    // Add the remaining entries as the last chunk
    if (currentChunk.length > 0) {
      chunks.push(currentChunk);
    }
    
    return chunks;
  }

  /**
   * Updates serializer options
   */
  updateOptions(options: Partial<XMLGenerationOptions>): void {
    this.options = { ...this.options, ...options };
  }

  /**
   * Gets current serializer options
   */
  getOptions(): XMLGenerationOptions {
    return { ...this.options };
  }

  /**
   * Creates a minimal sitemap XML for testing
   */
  createMinimalSitemap(baseUrl: string): string {
    const entry: SitemapEntry = {
      url: baseUrl,
      lastmod: new Date().toISOString(),
      changefreq: 'daily',
      priority: '1.0',
    };

    return this.generateSitemapXML([entry]);
  }

  /**
   * Validates that a URL is properly formatted for sitemap
   */
  static validateSitemapUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      
      // Check URL length
      if (url.length > XML_CONSTANTS.MAX_URL_LENGTH) {
        return false;
      }
      
      // Check protocol
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return false;
      }
      
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Sanitizes a URL for use in sitemap
   */
  static sanitizeUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      
      // Remove fragment
      urlObj.hash = '';
      
      // Remove trailing slash except for root
      if (urlObj.pathname !== '/' && urlObj.pathname.endsWith('/')) {
        urlObj.pathname = urlObj.pathname.slice(0, -1);
      }
      
      return urlObj.toString();
    } catch {
      return url; // Return original if parsing fails
    }
  }
}