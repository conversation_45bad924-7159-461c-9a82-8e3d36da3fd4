/**
 * 音频文件处理服务
 * 专门处理音乐生成后的文件下载、存储和数据库更新
 */

import { fileStorageService, FileDownloadResult } from './file-storage';
import { updateTrackFileInfo } from '@/models/track';

export interface AudioProcessResult {
  success: boolean;
  trackUuid?: string;
  originalUrl?: string;
  newUrl?: string;
  fileSize?: number;
  downloadSuccess?: boolean;
  s3Key?: string;
  error?: string;
  isTOSFile?: boolean;
  tosPath?: string;
}

export class AudioFileProcessor {
  private static instance: AudioFileProcessor;

  private constructor() {}

  static getInstance(): AudioFileProcessor {
    if (!AudioFileProcessor.instance) {
      AudioFileProcessor.instance = new AudioFileProcessor();
    }
    return AudioFileProcessor.instance;
  }

  /**
   * 检查URL是否为TOS URL
   */
  private isTOSUrl(url: string): boolean {
    const domain = process.env.VOLCANO_MUSIC_DOMAIN;
    return domain ? url.includes(domain) : false;
  }

  /**
   * 从TOS URL中提取路径
   */
  private extractTOSPath(tosUrl: string): string | undefined {
    const domain = process.env.VOLCANO_MUSIC_DOMAIN;
    if (!domain || !tosUrl.includes(domain)) {
      return undefined;
    }

    try {
      const url = new URL(tosUrl);
      // 移除开头的斜杠
      return url.pathname.replace(/^\//, '');
    } catch (error) {
      console.error('Failed to extract TOS path from URL:', error);
      return undefined;
    }
  }

  /**
   * 构造TOS URL
   */
  private constructTOSUrl(tosPath: string): string {
    const domain = process.env.VOLCANO_MUSIC_DOMAIN;
    if (!domain) {
      throw new Error("VOLCANO_MUSIC_DOMAIN environment variable is required for TOS URL construction");
    }
    
    if (!tosPath) {
      throw new Error("TOS path is required for URL construction");
    }
    
    // 确保domain不以/结尾，tosPath不以/开头
    const cleanDomain = domain.replace(/\/$/, '');
    const cleanPath = tosPath.replace(/^\//, '');
    
    return `${cleanDomain}/${cleanPath}`;
  }

  /**
   * 检查是否应该跳过文件处理（对于TOS文件）
   */
  private shouldSkipProcessing(sourceUrl: string, tosPath?: string): boolean {
    // 如果是TOS URL或者有TOS路径，我们可以跳过下载/上传过程
    return this.isTOSUrl(sourceUrl) || !!tosPath;
  }

  /**
   * 处理音频文件：下载、存储、更新数据库
   */
  async processAudioFile(
    trackUuid: string,
    sourceUrl: string,
    originalFileSize?: number,
    tosPath?: string
  ): Promise<AudioProcessResult> {
    try {
      console.log(`Processing audio file for track ${trackUuid}`);
      console.log(`Source URL: ${sourceUrl}`);
      console.log(`TOS Path: ${tosPath || 'none'}`);

      const isTOSFile = this.isTOSUrl(sourceUrl) || !!tosPath;
      const extractedTOSPath = tosPath || this.extractTOSPath(sourceUrl);

      console.log(`Is TOS file: ${isTOSFile}, TOS path: ${extractedTOSPath || 'none'}`);

      // 检查 URL 是否已经是我们的本地 URL
      if (this.isLocalUrl(sourceUrl)) {
        console.log(`File is already local: ${sourceUrl}`);
        return {
          success: true,
          trackUuid,
          originalUrl: sourceUrl,
          newUrl: sourceUrl,
          isTOSFile: false,
        };
      }

      // 对于TOS文件，跳过下载/上传过程
      if (this.shouldSkipProcessing(sourceUrl, tosPath)) {
        console.log(`Skipping file processing for TOS file: ${trackUuid}`);
        
        // 确保我们有正确的TOS URL
        let finalTOSUrl = sourceUrl;
        if (extractedTOSPath && !this.isTOSUrl(sourceUrl)) {
          try {
            finalTOSUrl = this.constructTOSUrl(extractedTOSPath);
            console.log(`Constructed TOS URL: ${finalTOSUrl}`);
          } catch (error) {
            console.warn(`Failed to construct TOS URL, using source URL:`, error);
          }
        }

        // 更新数据库中的文件信息（不需要下载）
        const updateResult = await updateTrackFileInfo(trackUuid, {
          file_url: finalTOSUrl,
          file_path: extractedTOSPath,
          file_size: originalFileSize || 0,
          file_format: this.getFileExtensionFromUrl(finalTOSUrl),
          original_file_url: sourceUrl !== finalTOSUrl ? sourceUrl : undefined,
        });

        if (!updateResult) {
          throw new Error('Failed to update database for TOS file');
        }

        console.log(`TOS file processed successfully for track ${trackUuid}`);
        console.log(`Final TOS URL: ${finalTOSUrl}`);

        return {
          success: true,
          trackUuid,
          originalUrl: sourceUrl,
          newUrl: finalTOSUrl,
          fileSize: originalFileSize || 0,
          downloadSuccess: false, // 没有下载，直接使用TOS
          isTOSFile: true,
          tosPath: extractedTOSPath,
        };
      }

      // 确定文件扩展名
      const fileExtension = this.getFileExtensionFromUrl(sourceUrl);
      
      // 尝试下载文件到S3或本地存储
      let finalFileUrl = sourceUrl;
      let finalFileSize = originalFileSize || 0;
      let downloadSuccess = false;
      let s3Key: string | undefined;

      try {
        const downloadResult = await fileStorageService.downloadAndSaveFile(
          sourceUrl,
          `track_${trackUuid}`,
          fileExtension
        );

        if (downloadResult.success && downloadResult.downloadSuccess) {
          finalFileUrl = downloadResult.publicUrl!;
          finalFileSize = downloadResult.fileSize || originalFileSize || 0;
          downloadSuccess = true;
          s3Key = downloadResult.s3Key;
          console.log(`File downloaded successfully for track ${trackUuid}: ${finalFileUrl}`);
          if (s3Key) {
            console.log(`File stored in S3 with key: ${s3Key}`);
          }
        } else {
          console.warn(`File download failed for track ${trackUuid}, using original URL: ${downloadResult.error}`);
        }
      } catch (error) {
        console.warn(`File download error for track ${trackUuid}, using original URL:`, error);
      }

      // 更新数据库中的文件信息（无论下载是否成功）
      const updateResult = await updateTrackFileInfo(trackUuid, {
        file_url: finalFileUrl,
        file_size: finalFileSize,
        file_format: fileExtension,
        original_file_url: sourceUrl, // 保存原始 URL 作为备份
        // TODO: 添加s3_key字段到数据库schema后启用
        // s3_key: s3Key, // 保存S3 key（如果有）
      });

      if (!updateResult) {
        // 如果数据库更新失败且文件已下载，删除已下载的文件
        if (downloadSuccess && finalFileUrl !== sourceUrl) {
          await fileStorageService.deleteFile(finalFileUrl, s3Key);
        }
        throw new Error('Failed to update database');
      }

      console.log(`Audio file processed successfully for track ${trackUuid}`);
      console.log(`Final URL: ${finalFileUrl}`);
      console.log(`Download success: ${downloadSuccess}`);

      return {
        success: true,
        trackUuid,
        originalUrl: sourceUrl,
        newUrl: finalFileUrl,
        fileSize: finalFileSize,
        downloadSuccess,
        s3Key,
        isTOSFile: false, // 非TOS文件走传统下载流程
      };

    } catch (error) {
      console.error(`Audio file processing failed for track ${trackUuid}:`, error);
      return {
        success: false,
        trackUuid,
        originalUrl: sourceUrl,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * 批量处理多个音频文件
   */
  async processBatchAudioFiles(
    tracks: Array<{
      uuid: string;
      file_url: string;
      file_size?: number;
      file_path?: string; // TOS path
    }>
  ): Promise<AudioProcessResult[]> {
    console.log(`Processing ${tracks.length} audio files in batch`);

    const results: AudioProcessResult[] = [];

    for (const track of tracks) {
      const result = await this.processAudioFile(
        track.uuid,
        track.file_url,
        track.file_size,
        track.file_path
      );
      results.push(result);

      // 添加小延迟避免过于频繁的请求
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    const successCount = results.filter(r => r.success).length;
    console.log(`Batch processing completed: ${successCount}/${tracks.length} successful`);

    return results;
  }

  /**
   * 检查 URL 是否是本地 URL
   */
  private isLocalUrl(url: string): boolean {
    const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'http://localhost:3000';
    return url.startsWith(baseUrl) || url.startsWith('/uploads/');
  }

  /**
   * 从 URL 中提取文件扩展名
   */
  private getFileExtensionFromUrl(url: string): string {
    try {
      // 检查 URL 中的 mime_type 参数
      const urlObj = new URL(url);
      const mimeType = urlObj.searchParams.get('mime_type');
      
      if (mimeType) {
        switch (mimeType) {
          case 'audio_wav':
            return 'wav';
          case 'audio_mp3':
            return 'mp3';
          case 'audio_mpeg':
            return 'mp3';
          default:
            break;
        }
      }

      // 从 URL 路径中提取扩展名
      const pathname = urlObj.pathname;
      const lastDot = pathname.lastIndexOf('.');
      if (lastDot > 0) {
        const ext = pathname.substring(lastDot + 1).toLowerCase();
        if (['wav', 'mp3', 'flac', 'aac'].includes(ext)) {
          return ext;
        }
      }

      // 默认返回 wav
      return 'wav';
    } catch (error) {
      console.error('Failed to parse URL for extension:', error);
      return 'wav';
    }
  }

  /**
   * 验证音频文件
   */
  async validateAudioFile(publicUrl: string): Promise<boolean> {
    try {
      const fileInfo = fileStorageService.getFileInfo(publicUrl);
      
      if (!fileInfo || !fileInfo.exists) {
        return false;
      }

      // 检查文件大小（至少应该有一些内容）
      if (fileInfo.size < 1000) { // 小于 1KB 可能是无效文件
        return false;
      }

      return true;
    } catch (error) {
      console.error('Audio file validation failed:', error);
      return false;
    }
  }

  /**
   * 验证TOS文件可访问性
   */
  async validateTOSFile(tosUrl: string): Promise<boolean> {
    try {
      console.log(`Validating TOS file accessibility: ${tosUrl}`);
      
      // 发送HEAD请求检查文件是否存在
      const response = await fetch(tosUrl, { method: 'HEAD' });
      
      if (response.ok) {
        console.log(`TOS file is accessible: ${tosUrl}`);
        return true;
      } else {
        console.warn(`TOS file not accessible: ${tosUrl}, status: ${response.status}`);
        return false;
      }
    } catch (error) {
      console.error(`Failed to validate TOS file: ${tosUrl}`, error);
      return false;
    }
  }

  /**
   * 获取处理统计信息
   */
  getProcessingStats(): {
    uploadDir: string;
    totalFiles: number;
    totalSize: number;
    tosFilesSkipped: number;
    traditionalFilesProcessed: number;
  } {
    // 这里可以实现统计逻辑
    return {
      uploadDir: 'public/uploads/music',
      totalFiles: 0,
      totalSize: 0,
      tosFilesSkipped: 0,
      traditionalFilesProcessed: 0,
    };
  }

  /**
   * 清理未使用的S3文件（排除音乐文件）
   */
  async cleanupUnusedFiles(): Promise<{
    cleaned: number;
    errors: string[];
  }> {
    console.log("Starting cleanup of unused files (excluding music files)");
    
    // 实现清理逻辑，但排除音乐文件
    // 这里只是一个占位符实现
    
    return {
      cleaned: 0,
      errors: [],
    };
  }
}

// 导出单例实例
export const audioFileProcessor = AudioFileProcessor.getInstance();
