/**
 * Track Creation Service - 统一的音轨创建服务
 * 确保正确处理用户订阅状态、水印和premium字段
 */

import { insertTrack } from "@/models/track";
import { findUserByUuid } from "@/models/user";
import { generateTrackSlug } from "@/lib/track-slug";
import { getUuid } from "@/lib/hash";
import { isTOSTrack } from "@/lib/track-utils";
import type { MusicGeneration } from "@/types/music";

interface TrackCreationOptions {
  generation: any; // 使用any来兼容不同的generation类型
  file_url: string;
  file_path?: string; // TOS path for direct access
  file_size?: number;
  file_format?: string;
  metadata?: any;
  title?: string;
  is_public?: boolean;
}

interface UserSubscription {
  plan: "free" | "professional" | "commercial";
  is_active: boolean;
  expires_at?: string;
}

export class TrackCreationService {
  
  /**
   * 获取可访问的TOS URL
   */
  private static async getAccessibleTOSUrl(filePath: string, fallbackUrl?: string): Promise<{
    success: boolean;
    url?: string;
    error?: string;
    usedFallback?: boolean;
  }> {
    try {
      const { tosAccessService } = await import("./tos-access-service");
      const result = await tosAccessService.getAccessibleUrl(filePath, fallbackUrl, true);
      
      return {
        success: result.success,
        url: result.url,
        error: result.error,
        usedFallback: !!result.fallbackUrl,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown TOS access error',
      };
    }
  }



  /**
   * 从metadata中提取TOS路径
   */
  private static extractTOSPath(metadata: any): string | undefined {
    return metadata?.tos_path || metadata?.file_path;
  }

  /**
   * 从URL中提取文件格式
   */
  private static extractFileFormat(url: string): string {
    try {
      // 检查URL中的mime_type参数
      const urlObj = new URL(url);
      const mimeType = urlObj.searchParams.get('mime_type');
      
      if (mimeType) {
        switch (mimeType) {
          case 'audio_wav':
            return 'wav';
          case 'audio_mp3':
          case 'audio_mpeg':
            return 'mp3';
          default:
            break;
        }
      }

      // 从URL路径中提取扩展名
      const pathname = urlObj.pathname;
      const lastDot = pathname.lastIndexOf('.');
      if (lastDot > 0) {
        const ext = pathname.substring(lastDot + 1).toLowerCase();
        if (['wav', 'mp3', 'flac', 'aac'].includes(ext)) {
          return ext;
        }
      }

      // 默认返回mp3
      return 'mp3';
    } catch (error) {
      console.error('Failed to extract file format from URL:', error);
      return 'mp3';
    }
  }

  /**
   * 创建音轨记录，正确处理用户订阅状态和水印
   */
  static async createTrack(options: TrackCreationOptions) {
    const {
      generation,
      file_url,
      file_path,
      file_size = 0,
      file_format,
      metadata = {},
      title,
      is_public = true
    } = options;

    try {
      // 1. 获取用户信息
      const user = await findUserByUuid(generation.user_uuid);
      if (!user) {
        throw new Error(`User not found: ${generation.user_uuid}`);
      }

      // 2. 确定用户订阅状态
      const userSubscription: UserSubscription = {
        plan: (user.subscription_plan as "free" | "professional" | "commercial") || "free",
        is_active: user.subscription_status === "active",
        expires_at: user.subscription_expires_at?.toISOString(),
      };

      console.log("User subscription status:", {
        user_uuid: generation.user_uuid,
        plan: userSubscription.plan,
        is_active: userSubscription.is_active,
        expires_at: userSubscription.expires_at,
      });

      // 3. 生成track UUID和slug
      const trackUuid = getUuid();
      const slug = generateTrackSlug({
        prompt: generation.prompt,
        bpm: generation.bpm,
        uuid: trackUuid,
        style: generation.style
      });

      // 4. 确定最终的文件URL和路径
      let finalFileUrl = file_url;
      let finalFilePath = file_path;

      // 检查是否有TOS路径，如果有则获取可访问的URL
      if (file_path) {
        try {
          const tosResult = await this.getAccessibleTOSUrl(file_path, file_url);
          
          if (tosResult.success && tosResult.url) {
            finalFileUrl = tosResult.url;
            finalFilePath = file_path;
            
            if (tosResult.usedFallback) {
              console.warn(`TOS access failed, using fallback URL: ${finalFileUrl}`);
              console.warn(`TOS error: ${tosResult.error}`);
            } else {
              console.log(`Using accessible TOS URL: ${finalFileUrl} (path: ${file_path})`);
            }
          } else {
            console.warn(`Failed to get accessible TOS URL from path ${file_path}: ${tosResult.error}`);
            console.log(`Falling back to provided file_url: ${file_url}`);
          }
        } catch (error) {
          console.warn(`TOS access error for path ${file_path}:`, error);
          console.log(`Falling back to provided file_url: ${file_url}`);
        }
      } else if (isTOSTrack(file_url)) {
        // 如果file_url本身就是TOS URL，尝试提取路径
        finalFilePath = this.extractTOSPath(metadata);
        console.log(`Detected TOS URL: ${file_url}`);
      }

      // 5. 确定用户是否为Premium用户（概念上的水印处理）
      const isPremiumUser = userSubscription.plan !== "free" && userSubscription.is_active;
      
      // 模拟水印处理结果（实际上没有水印处理）
      const processedAudio = {
        processed_url: finalFileUrl, // 使用TOS URL或原始URL
        has_watermark: !isPremiumUser, // 非Premium用户概念上有水印
        processing_time: 0,
      };

      console.log("Track URL assignment:", {
        track_uuid: trackUuid,
        api_original_url: file_url, // API返回的原始URL
        tos_final_url: finalFileUrl, // TOS组合地址
        file_path: finalFilePath,
        storage_type: isTOSTrack(finalFileUrl) ? 'tos' : 'legacy',
        is_premium_user: isPremiumUser,
        original_file_url_will_be: file_url, // 保存API原始URL
        file_url_will_be: finalFileUrl, // 保存TOS地址
      });

      // 6. 确定文件格式
      let finalFileFormat = file_format;
      if (!finalFileFormat) {
        // 从URL中提取文件格式
        finalFileFormat = this.extractFileFormat(finalFileUrl);
      }

      // 7. 创建track记录
      const trackData = {
        uuid: trackUuid,
        user_uuid: generation.user_uuid,
        generation_uuid: generation.uuid,
        title: title || generation.prompt.substring(0, 100),
        slug: slug,
        prompt: generation.prompt,
        style: generation.style || null,
        mood: generation.mood || null,
        bpm: generation.bpm || null,
        duration: generation.duration,
        // New fields for API compatibility
        genre: generation.genre,
        instrument: generation.instrument,
        theme: generation.theme,
        // File information
        file_url: finalFileUrl, // 前端使用的TOS地址
        file_path: finalFilePath, // TOS path for direct access
        file_size: file_size,
        file_format: finalFileFormat,
        metadata: JSON.stringify({
          ...metadata,
          original_provider_url: file_url, // 保存最初的provider URL
        }),
        // Premium and watermark fields - 关键部分！
        is_premium: isPremiumUser,
        has_watermark: !isPremiumUser,
        // original_file_url 保存API返回的原始AudioUrl
        original_file_url: file_url, // API返回的原始URL
        // file_url 保存组合的TOS地址（前端使用）
        // Other fields
        download_count: 0,
        is_public: is_public,
        created_at: new Date(),
        updated_at: new Date(),
      };

      const track = await insertTrack(trackData);

      console.log("Track created successfully:", {
        track_uuid: trackUuid,
        generation_uuid: generation.uuid,
        user_plan: userSubscription.plan,
        is_premium: isPremiumUser,
        has_watermark: !isPremiumUser,
        original_file_url: file_url,
        file_url: finalFileUrl,
      });

      return track;

    } catch (error) {
      console.error("Track creation failed:", error);
      throw error;
    }
  }

  /**
   * 获取用户订阅状态
   */
  static async getUserSubscription(user_uuid: string): Promise<UserSubscription> {
    const user = await findUserByUuid(user_uuid);
    if (!user) {
      throw new Error(`User not found: ${user_uuid}`);
    }

    return {
      plan: (user.subscription_plan as "free" | "professional" | "commercial") || "free",
      is_active: user.subscription_status === "active",
      expires_at: user.subscription_expires_at?.toISOString(),
    };
  }

  /**
   * 检查用户是否为premium用户
   */
  static async isUserPremium(user_uuid: string): Promise<boolean> {
    try {
      const subscription = await this.getUserSubscription(user_uuid);
      
      if (subscription.plan === "free") {
        return false;
      }

      if (!subscription.is_active) {
        return false;
      }

      if (subscription.expires_at) {
        const expiryDate = new Date(subscription.expires_at);
        if (expiryDate < new Date()) {
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error("Failed to check user premium status:", error);
      return false;
    }
  }

  /**
   * 验证Volcano SongDetail响应
   */
  private static validateVolcanoSongDetail(songDetail: any): void {
    if (!songDetail) {
      throw new Error('Missing SongDetail in Volcano response');
    }

    const requiredFields = ['AudioUrl', 'TosPath', 'Duration', 'Prompt'];
    for (const field of requiredFields) {
      if (!songDetail[field]) {
        throw new Error(`Missing ${field} in Volcano SongDetail`);
      }
    }

    if (songDetail.Duration <= 0) {
      throw new Error('Invalid Duration in Volcano SongDetail');
    }

    // Validate URL format
    try {
      new URL(songDetail.AudioUrl);
    } catch (error) {
      throw new Error(`Invalid AudioUrl format: ${songDetail.AudioUrl}`);
    }
  }

  /**
   * 创建音轨记录从Volcano响应（优化版本）
   */
  static async createTrackFromVolcanoResponse(
    generation: any,
    songDetail: any
  ) {
    // Validate the response first
    this.validateVolcanoSongDetail(songDetail);

    const { FileSizeEstimator } = await import("@/lib/file-size-estimator");
    
    // 构造TOS URL
    const domain = process.env.VOLCANO_MUSIC_DOMAIN;
    if (!domain) {
      throw new Error('VOLCANO_MUSIC_DOMAIN not configured');
    }
    
    const cleanDomain = domain.replace(/\/$/, '');
    const cleanPath = songDetail.TosPath.replace(/^\//, '');
    const tosUrl = `${cleanDomain}/${cleanPath}`;
    
    // 使用优化的数据映射
    const trackData = {
      file_url: tosUrl,                           // TOS URL as primary file URL
      original_file_url: songDetail.AudioUrl,    // Volcano AudioUrl for premium users
      file_path: songDetail.TosPath,             // TOS path for reference
      file_size: FileSizeEstimator.estimateFileSize(songDetail.Duration),
      metadata: {
        duration: songDetail.Duration,
        prompt: songDetail.Prompt,
        genre: songDetail.Genre || '',
        mood: songDetail.Mood || '',
        theme: songDetail.Theme || '',
        instrument: songDetail.Instrument || ''
      }
    };

    console.log('Creating track from Volcano response:', {
      generation_uuid: generation.uuid,
      provider_task_id: generation.provider_task_id,
      user_uuid: generation.user_uuid,
      file_url: trackData.file_url,
      original_file_url: trackData.original_file_url,
      file_size: trackData.file_size
    });

    return await this.createTrack({
      generation,
      ...trackData,
      is_public: true
    });
  }

  /**
   * 为现有track升级到premium版本（用户升级订阅后）
   */
  static async upgradeTrackToPremium(track_uuid: string): Promise<boolean> {
    try {
      // TODO: 实现track升级逻辑
      // 1. 获取track信息
      // 2. 检查是否有original_file_url
      // 3. 移除水印，更新文件URL
      // 4. 更新is_premium和has_watermark字段
      
      console.log("Track upgrade to premium:", track_uuid);
      return true;
    } catch (error) {
      console.error("Failed to upgrade track to premium:", error);
      return false;
    }
  }
}
