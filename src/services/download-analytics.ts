/**
 * 下载统计和监控服务
 */

export interface DownloadEvent {
  track_uuid: string;
  user_uuid?: string;
  download_method: 'cdn' | 's3';
  file_size?: number;
  format: string;
  quality: string;
  custom_filename?: string;
  user_agent?: string;
  ip_address?: string;
  timestamp: Date;
  response_time?: number; // 毫秒
  success: boolean;
  error_message?: string;
}

export interface DownloadStats {
  total_downloads: number;
  cdn_downloads: number;
  s3_downloads: number;
  success_rate: number;
  average_response_time: number;
  popular_formats: { format: string; count: number }[];
  error_summary: { error: string; count: number }[];
}

export interface PerformanceMetrics {
  cdn_response_time: number;
  s3_response_time: number;
  cdn_success_rate: number;
  s3_success_rate: number;
  cache_hit_rate?: number;
}

/**
 * 下载分析服务类
 */
export class DownloadAnalyticsService {
  private static instance: DownloadAnalyticsService | null = null;
  private events: DownloadEvent[] = []; // 内存中的事件缓存
  private readonly maxCacheSize = 1000; // 最大缓存事件数

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): DownloadAnalyticsService {
    if (!DownloadAnalyticsService.instance) {
      DownloadAnalyticsService.instance = new DownloadAnalyticsService();
    }
    return DownloadAnalyticsService.instance;
  }

  /**
   * 记录下载事件
   */
  recordDownloadEvent(event: Omit<DownloadEvent, 'timestamp'>): void {
    const fullEvent: DownloadEvent = {
      ...event,
      timestamp: new Date()
    };

    // 添加到内存缓存
    this.events.push(fullEvent);

    // 保持缓存大小限制
    if (this.events.length > this.maxCacheSize) {
      this.events.shift();
    }

    // 异步记录到日志
    this.logEvent(fullEvent);

    // TODO: 异步保存到数据库
    // this.saveToDatabase(fullEvent);
  }

  /**
   * 记录下载开始
   */
  recordDownloadStart(track_uuid: string, user_uuid?: string, method: 'cdn' | 's3' = 'cdn'): string {
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    console.log(`[Download Analytics] 开始下载 - Track: ${track_uuid}, Method: ${method}, RequestID: ${requestId}`);
    
    return requestId;
  }

  /**
   * 记录下载完成
   */
  recordDownloadComplete(
    requestId: string,
    event: Omit<DownloadEvent, 'timestamp' | 'success'>
  ): void {
    this.recordDownloadEvent({
      ...event,
      success: true
    });

    console.log(`[Download Analytics] 下载完成 - RequestID: ${requestId}, Method: ${event.download_method}`);
  }

  /**
   * 记录下载失败
   */
  recordDownloadError(
    requestId: string,
    event: Omit<DownloadEvent, 'timestamp' | 'success' | 'error_message'>,
    error: string
  ): void {
    this.recordDownloadEvent({
      ...event,
      success: false,
      error_message: error
    });

    console.error(`[Download Analytics] 下载失败 - RequestID: ${requestId}, Error: ${error}`);
  }

  /**
   * 获取下载统计
   */
  getDownloadStats(timeRange?: { start: Date; end: Date }): DownloadStats {
    let events = this.events;

    // 应用时间范围过滤
    if (timeRange) {
      events = events.filter(event => 
        event.timestamp >= timeRange.start && event.timestamp <= timeRange.end
      );
    }

    const totalDownloads = events.length;
    const cdnDownloads = events.filter(e => e.download_method === 'cdn').length;
    const s3Downloads = events.filter(e => e.download_method === 's3').length;
    const successfulDownloads = events.filter(e => e.success).length;

    // 计算平均响应时间
    const responseTimes = events
      .filter(e => e.response_time && e.response_time > 0)
      .map(e => e.response_time!);
    const averageResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length 
      : 0;

    // 统计流行格式
    const formatCounts = new Map<string, number>();
    events.forEach(event => {
      const count = formatCounts.get(event.format) || 0;
      formatCounts.set(event.format, count + 1);
    });
    const popularFormats = Array.from(formatCounts.entries())
      .map(([format, count]) => ({ format, count }))
      .sort((a, b) => b.count - a.count);

    // 统计错误
    const errorCounts = new Map<string, number>();
    events.filter(e => !e.success && e.error_message).forEach(event => {
      const error = event.error_message!;
      const count = errorCounts.get(error) || 0;
      errorCounts.set(error, count + 1);
    });
    const errorSummary = Array.from(errorCounts.entries())
      .map(([error, count]) => ({ error, count }))
      .sort((a, b) => b.count - a.count);

    return {
      total_downloads: totalDownloads,
      cdn_downloads: cdnDownloads,
      s3_downloads: s3Downloads,
      success_rate: totalDownloads > 0 ? (successfulDownloads / totalDownloads) * 100 : 0,
      average_response_time: Math.round(averageResponseTime),
      popular_formats: popularFormats,
      error_summary: errorSummary
    };
  }

  /**
   * 获取性能指标
   */
  getPerformanceMetrics(timeRange?: { start: Date; end: Date }): PerformanceMetrics {
    let events = this.events;

    if (timeRange) {
      events = events.filter(event => 
        event.timestamp >= timeRange.start && event.timestamp <= timeRange.end
      );
    }

    const cdnEvents = events.filter(e => e.download_method === 'cdn');
    const s3Events = events.filter(e => e.download_method === 's3');

    // CDN 性能指标
    const cdnResponseTimes = cdnEvents
      .filter(e => e.response_time && e.response_time > 0)
      .map(e => e.response_time!);
    const cdnResponseTime = cdnResponseTimes.length > 0
      ? cdnResponseTimes.reduce((sum, time) => sum + time, 0) / cdnResponseTimes.length
      : 0;
    const cdnSuccessRate = cdnEvents.length > 0
      ? (cdnEvents.filter(e => e.success).length / cdnEvents.length) * 100
      : 0;

    // S3 性能指标
    const s3ResponseTimes = s3Events
      .filter(e => e.response_time && e.response_time > 0)
      .map(e => e.response_time!);
    const s3ResponseTime = s3ResponseTimes.length > 0
      ? s3ResponseTimes.reduce((sum, time) => sum + time, 0) / s3ResponseTimes.length
      : 0;
    const s3SuccessRate = s3Events.length > 0
      ? (s3Events.filter(e => e.success).length / s3Events.length) * 100
      : 0;

    return {
      cdn_response_time: Math.round(cdnResponseTime),
      s3_response_time: Math.round(s3ResponseTime),
      cdn_success_rate: Math.round(cdnSuccessRate * 100) / 100,
      s3_success_rate: Math.round(s3SuccessRate * 100) / 100
    };
  }

  /**
   * 获取最近的下载事件
   */
  getRecentEvents(limit: number = 50): DownloadEvent[] {
    return this.events
      .slice(-limit)
      .reverse(); // 最新的在前
  }

  /**
   * 清理旧事件
   */
  cleanupOldEvents(olderThan: Date): void {
    const initialCount = this.events.length;
    this.events = this.events.filter(event => event.timestamp > olderThan);
    const removedCount = initialCount - this.events.length;
    
    if (removedCount > 0) {
      console.log(`[Download Analytics] 清理了 ${removedCount} 个旧事件`);
    }
  }

  /**
   * 记录事件到日志
   */
  private logEvent(event: DownloadEvent): void {
    const logLevel = event.success ? 'info' : 'error';
    const message = `Download ${event.success ? 'completed' : 'failed'} - Track: ${event.track_uuid}, Method: ${event.download_method}, Format: ${event.format}`;
    
    if (logLevel === 'error') {
      console.error(`[Download Analytics] ${message}, Error: ${event.error_message}`);
    } else {
      console.log(`[Download Analytics] ${message}`);
    }
  }

  /**
   * 生成下载报告
   */
  generateReport(timeRange?: { start: Date; end: Date }): string {
    const stats = this.getDownloadStats(timeRange);
    const performance = this.getPerformanceMetrics(timeRange);
    
    const report = `
📊 下载统计报告
${'='.repeat(50)}

📈 总体统计:
  总下载次数: ${stats.total_downloads}
  CDN 下载: ${stats.cdn_downloads} (${Math.round((stats.cdn_downloads / stats.total_downloads) * 100)}%)
  S3 下载: ${stats.s3_downloads} (${Math.round((stats.s3_downloads / stats.total_downloads) * 100)}%)
  成功率: ${stats.success_rate.toFixed(2)}%
  平均响应时间: ${stats.average_response_time}ms

⚡ 性能指标:
  CDN 平均响应时间: ${performance.cdn_response_time}ms
  S3 平均响应时间: ${performance.s3_response_time}ms
  CDN 成功率: ${performance.cdn_success_rate}%
  S3 成功率: ${performance.s3_success_rate}%

📁 热门格式:
${stats.popular_formats.slice(0, 5).map(f => `  ${f.format}: ${f.count} 次`).join('\n')}

❌ 错误统计:
${stats.error_summary.slice(0, 5).map(e => `  ${e.error}: ${e.count} 次`).join('\n')}
`;

    return report;
  }
}

/**
 * 获取默认下载分析服务实例
 */
export function getDownloadAnalytics(): DownloadAnalyticsService {
  return DownloadAnalyticsService.getInstance();
}

/**
 * 记录下载事件的便捷函数
 */
export function recordDownload(event: Omit<DownloadEvent, 'timestamp'>): void {
  const analytics = getDownloadAnalytics();
  analytics.recordDownloadEvent(event);
}

/**
 * 性能监控装饰器
 */
export function withDownloadTracking<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  getEventData: (...args: T) => Omit<DownloadEvent, 'timestamp' | 'success' | 'response_time'>
) {
  return async (...args: T): Promise<R> => {
    const analytics = getDownloadAnalytics();
    const startTime = Date.now();
    const eventData = getEventData(...args);
    const requestId = analytics.recordDownloadStart(eventData.track_uuid, eventData.user_uuid, eventData.download_method);

    try {
      const result = await fn(...args);
      const responseTime = Date.now() - startTime;
      
      analytics.recordDownloadComplete(requestId, {
        ...eventData,
        response_time: responseTime
      });

      return result;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      analytics.recordDownloadError(requestId, {
        ...eventData,
        response_time: responseTime
      }, error instanceof Error ? error.message : String(error));

      throw error;
    }
  };
}