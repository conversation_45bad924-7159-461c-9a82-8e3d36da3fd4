import { sitemapAnalytics, type SitemapMetrics } from "./sitemap-analytics";
import type { SitemapContentType } from "@/types/sitemap";

/**
 * Alert configuration
 */
export interface AlertConfig {
  errorRateThreshold: number; // Percentage
  slowRequestThreshold: number; // Milliseconds
  timeWindowMinutes: number;
  enabled: boolean;
}

/**
 * Health check result
 */
export interface HealthCheckResult {
  status: "healthy" | "warning" | "critical";
  checks: {
    errorRate: { status: string; value: number; threshold: number };
    performance: { status: string; averageTime: number; threshold: number };
    cacheHitRate: { status: string; value: number; threshold: number };
    contentCoverage: { status: string; missingContent: string[] };
  };
  timestamp: Date;
}

/**
 * Sitemap Monitor Service
 * Monitors sitemap health and performance
 */
export class SitemapMonitor {
  private alertConfig: AlertConfig = {
    errorRateThreshold: 10, // 10%
    slowRequestThreshold: 5000, // 5 seconds
    timeWindowMinutes: 30,
    enabled: true,
  };

  private lastAlertTime: Date | null = null;
  private alertCooldownMinutes = 15; // Prevent spam alerts

  constructor(alertConfig?: Partial<AlertConfig>) {
    if (alertConfig) {
      this.alertConfig = { ...this.alertConfig, ...alertConfig };
    }
  }

  /**
   * Records a sitemap generation event with monitoring
   */
  recordGeneration(
    contentType: SitemapContentType | "index",
    locale: string,
    urlCount: number,
    generationTime: number,
    xmlSize: number,
    cacheHit: boolean = false,
    error?: Error
  ): void {
    const metrics: Omit<SitemapMetrics, "timestamp"> = {
      contentType,
      locale,
      urlCount,
      generationTime,
      size: xmlSize,
      cacheHit,
      errorOccurred: !!error,
      errorType: error?.constructor.name,
    };

    // Record in analytics
    sitemapAnalytics.recordGeneration(metrics);

    // Check for alerts if enabled
    if (this.alertConfig.enabled) {
      this.checkForAlerts();
    }

    // Log performance warnings
    if (generationTime > this.alertConfig.slowRequestThreshold) {
      console.warn(
        `[Sitemap Monitor] Slow generation detected: ${generationTime}ms for ${contentType}:${locale}`
      );
    }
  }

  /**
   * Performs health check on sitemap system
   */
  performHealthCheck(): HealthCheckResult {
    const performanceMetrics = sitemapAnalytics.getPerformanceMetrics(
      this.alertConfig.timeWindowMinutes
    );

    const contentCoverage = sitemapAnalytics.getContentCoverage();
    const expectedContent = this.getExpectedContent();
    const missingContent = this.findMissingContent(contentCoverage, expectedContent);

    const checks = {
      errorRate: {
        status: performanceMetrics.errorRate <= this.alertConfig.errorRateThreshold ? "healthy" : "critical",
        value: performanceMetrics.errorRate,
        threshold: this.alertConfig.errorRateThreshold,
      },
      performance: {
        status: performanceMetrics.averageTime <= this.alertConfig.slowRequestThreshold ? "healthy" : "warning",
        averageTime: performanceMetrics.averageTime,
        threshold: this.alertConfig.slowRequestThreshold,
      },
      cacheHitRate: {
        status: performanceMetrics.cacheHitRate >= 50 ? "healthy" : "warning", // Expect at least 50% cache hit rate
        value: performanceMetrics.cacheHitRate,
        threshold: 50,
      },
      contentCoverage: {
        status: missingContent.length === 0 ? "healthy" : "warning",
        missingContent,
      },
    };

    // Determine overall status
    let overallStatus: "healthy" | "warning" | "critical" = "healthy";
    if (Object.values(checks).some(check => check.status === "critical")) {
      overallStatus = "critical";
    } else if (Object.values(checks).some(check => check.status === "warning")) {
      overallStatus = "warning";
    }

    return {
      status: overallStatus,
      checks,
      timestamp: new Date(),
    };
  }

  /**
   * Gets monitoring dashboard data
   */
  getDashboardData(): {
    summary: ReturnType<typeof sitemapAnalytics.getAnalyticsSummary>;
    recentPerformance: ReturnType<typeof sitemapAnalytics.getPerformanceMetrics>;
    contentCoverage: ReturnType<typeof sitemapAnalytics.getContentCoverage>;
    recentErrors: SitemapMetrics[];
    healthCheck: HealthCheckResult;
  } {
    return {
      summary: sitemapAnalytics.getAnalyticsSummary(),
      recentPerformance: sitemapAnalytics.getPerformanceMetrics(60),
      contentCoverage: sitemapAnalytics.getContentCoverage(),
      recentErrors: sitemapAnalytics.getRecentErrors(60),
      healthCheck: this.performHealthCheck(),
    };
  }

  /**
   * Updates alert configuration
   */
  updateAlertConfig(config: Partial<AlertConfig>): void {
    this.alertConfig = { ...this.alertConfig, ...config };
  }

  /**
   * Gets current alert configuration
   */
  getAlertConfig(): AlertConfig {
    return { ...this.alertConfig };
  }

  /**
   * Manually triggers alert check
   */
  checkForAlerts(): void {
    if (!this.alertConfig.enabled) return;

    // Check cooldown period
    if (this.lastAlertTime) {
      const cooldownExpired = Date.now() - this.lastAlertTime.getTime() > 
        this.alertCooldownMinutes * 60 * 1000;
      if (!cooldownExpired) return;
    }

    const shouldAlert = sitemapAnalytics.shouldAlert(
      this.alertConfig.errorRateThreshold,
      this.alertConfig.timeWindowMinutes
    );

    if (shouldAlert) {
      this.triggerAlert();
    }
  }

  /**
   * Gets metrics for external monitoring systems (Prometheus format)
   */
  getPrometheusMetrics(): string {
    const summary = sitemapAnalytics.getAnalyticsSummary();
    const performance = sitemapAnalytics.getPerformanceMetrics(60);
    
    const metrics = [
      `# HELP sitemap_generations_total Total number of sitemap generations`,
      `# TYPE sitemap_generations_total counter`,
      `sitemap_generations_total ${summary.totalGenerations}`,
      ``,
      `# HELP sitemap_errors_total Total number of sitemap generation errors`,
      `# TYPE sitemap_errors_total counter`,
      `sitemap_errors_total ${summary.totalErrors}`,
      ``,
      `# HELP sitemap_generation_duration_seconds Average sitemap generation time`,
      `# TYPE sitemap_generation_duration_seconds gauge`,
      `sitemap_generation_duration_seconds ${summary.averageGenerationTime / 1000}`,
      ``,
      `# HELP sitemap_cache_hit_rate Cache hit rate percentage`,
      `# TYPE sitemap_cache_hit_rate gauge`,
      `sitemap_cache_hit_rate ${summary.cacheHitRate}`,
      ``,
      `# HELP sitemap_error_rate Recent error rate percentage`,
      `# TYPE sitemap_error_rate gauge`,
      `sitemap_error_rate ${performance.errorRate}`,
    ];

    return metrics.join('\n');
  }

  private getExpectedContent(): string[] {
    // Define expected content types and locales
    const contentTypes: SitemapContentType[] = ["static", "posts", "loops"];
    const locales = ["en", "zh"];
    
    const expected: string[] = [];
    for (const contentType of contentTypes) {
      for (const locale of locales) {
        expected.push(`${contentType}:${locale}`);
      }
    }
    
    return expected;
  }

  private findMissingContent(
    coverage: Record<string, { count: number; averageSize: number }>,
    expected: string[]
  ): string[] {
    return expected.filter(content => !coverage[content] || coverage[content].count === 0);
  }

  private triggerAlert(): void {
    this.lastAlertTime = new Date();
    
    const recentErrors = sitemapAnalytics.getRecentErrors(this.alertConfig.timeWindowMinutes);
    const performanceMetrics = sitemapAnalytics.getPerformanceMetrics(this.alertConfig.timeWindowMinutes);
    
    const alertMessage = `
[SITEMAP ALERT] High error rate detected
- Error Rate: ${performanceMetrics.errorRate.toFixed(2)}%
- Threshold: ${this.alertConfig.errorRateThreshold}%
- Time Window: ${this.alertConfig.timeWindowMinutes} minutes
- Recent Errors: ${recentErrors.length}
- Average Generation Time: ${performanceMetrics.averageTime.toFixed(0)}ms
    `.trim();

    console.error(alertMessage);
    
    // In a real application, you would send this to your alerting system
    // (e.g., Slack, PagerDuty, email, etc.)
    this.sendAlert(alertMessage, recentErrors);
  }

  private sendAlert(message: string, recentErrors: SitemapMetrics[]): void {
    // Placeholder for actual alert sending logic
    // This could integrate with:
    // - Slack webhooks
    // - Email services
    // - PagerDuty
    // - Discord webhooks
    // - Custom alerting systems
    
    console.error("[Sitemap Monitor] Alert triggered:", {
      message,
      errorCount: recentErrors.length,
      timestamp: new Date().toISOString(),
    });
  }
}

// Global monitor instance
export const sitemapMonitor = new SitemapMonitor();