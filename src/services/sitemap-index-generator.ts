import type { 
  SitemapIndexEntry, 
  SitemapEntry, 
  ContentGenerator,
  SitemapXMLResult 
} from "@/types/sitemap";
import { URLBuilder } from "./sitemap-url-builder";
import { XMLSerializer } from "./sitemap-xml-serializer";
import { SitemapCacheManager } from "./sitemap-cache";
import { 
  StaticPagesGenerator, 
  BlogPostsGenerator, 
  MusicLoopsGenerator 
} from "./sitemap-content-generators";
import { 
  SITEMAP_CONFIG, 
  SITEMAP_CACHE_CONFIG 
} from "@/constants/sitemap";
import { 
  shouldUseSitemapIndex, 
  chunkSitemapEntries,
  measureExecutionTime 
} from "@/lib/sitemap-utils";

/**
 * Sitemap Index Generator
 * Manages multiple sitemap files and generates sitemap index when needed
 */
export class SitemapIndexGenerator {
  private urlBuilder: URLBuilder;
  private xmlSerializer: XMLSerializer;
  private cacheManager: SitemapCacheManager;
  private contentGenerators: Map<string, ContentGenerator>;

  constructor(
    urlBuilder?: URLBuilder,
    xmlSerializer?: XMLSerializer,
    cacheManager?: SitemapCacheManager
  ) {
    this.urlBuilder = urlBuilder || new URLBuilder();
    this.xmlSerializer = xmlSerializer || new XMLSerializer();
    this.cacheManager = cacheManager || new SitemapCacheManager();
    this.contentGenerators = new Map();
    
    this.initializeContentGenerators();
  }

  /**
   * Generates sitemap index or single sitemap based on content size
   */
  async generateSitemap(locale: string = 'en'): Promise<SitemapXMLResult> {
    const { result, executionTime } = await measureExecutionTime(async () => {
      // Get estimated counts for all content types
      const estimatedCounts = await this.getEstimatedCounts();
      const totalEstimatedUrls = Object.values(estimatedCounts).reduce((sum, count) => sum + count, 0);

      // Decide whether to use sitemap index
      if (shouldUseSitemapIndex(totalEstimatedUrls)) {
        return await this.generateSitemapIndex();
      } else {
        return await this.generateSingleSitemap(locale);
      }
    });

    return {
      ...result,
      generationTime: executionTime,
    };
  }

  /**
   * Generates a sitemap index with separate sitemaps for each content type
   */
  async generateSitemapIndex(): Promise<Omit<SitemapXMLResult, 'generationTime'>> {
    const indexEntries: SitemapIndexEntry[] = [];
    const baseUrl = this.urlBuilder.getBaseUrl();

    // Generate index entries for each content type
    for (const [contentType, generator] of this.contentGenerators) {
      try {
        const lastModified = await generator.getLastModified();
        const estimatedCount = await generator.getEstimatedCount();

        // Only include content types that have entries
        if (estimatedCount > 0) {
          const indexEntry: SitemapIndexEntry = {
            loc: `${baseUrl}/api/sitemap/${contentType}`,
            lastmod: lastModified.toISOString(),
          };
          indexEntries.push(indexEntry);
        }
      } catch (error) {
        console.error(`Error generating index entry for ${contentType}:`, error);
        // Continue with other content types
      }
    }

    // Generate XML for sitemap index
    const result = await this.xmlSerializer.serializeSitemapIndex(indexEntries);
    
    return {
      xml: result.xml,
      urlCount: indexEntries.length,
      isValid: result.isValid,
      validationErrors: result.validationErrors,
    };
  }

  /**
   * Generates a single sitemap with all content
   */
  async generateSingleSitemap(locale: string): Promise<Omit<SitemapXMLResult, 'generationTime'>> {
    const allEntries: SitemapEntry[] = [];

    // Generate entries from all content generators
    for (const [contentType, generator] of this.contentGenerators) {
      try {
        const entries = await generator.generate(locale);
        allEntries.push(...entries);
      } catch (error) {
        console.error(`Error generating entries for ${contentType}:`, error);
        // Continue with other content types
      }
    }

    // Check if we need to split into chunks
    const chunks = chunkSitemapEntries(allEntries, SITEMAP_CONFIG.maxUrlsPerSitemap);
    
    if (chunks.length > 1) {
      // If we have multiple chunks, we should use sitemap index instead
      console.warn(`Single sitemap has ${allEntries.length} URLs, consider using sitemap index`);
    }

    // Use the first chunk for single sitemap
    const entriesToSerialize = chunks[0] || [];
    
    // Generate XML for sitemap
    const result = await this.xmlSerializer.serializeSitemap(entriesToSerialize);
    
    return {
      xml: result.xml,
      urlCount: entriesToSerialize.length,
      isValid: result.isValid,
      validationErrors: result.validationErrors,
    };
  }

  /**
   * Generates sitemap for specific content type
   */
  async generateContentTypeSitemap(
    contentType: string, 
    locale: string = 'en'
  ): Promise<SitemapXMLResult> {
    const { result, executionTime } = await measureExecutionTime(async () => {
      // Check cache first
      const cacheKey = `${contentType}:${locale}`;
      const cached = await this.cacheManager.getCachedSitemap(contentType as any, locale);
      
      if (cached) {
        return {
          xml: cached,
          urlCount: this.estimateUrlCountFromXML(cached),
          isValid: true,
        };
      }

      // Generate fresh content
      const generator = this.contentGenerators.get(contentType);
      if (!generator) {
        throw new Error(`Unknown content type: ${contentType}`);
      }

      const entries = await generator.generate(locale);
      const result = await this.xmlSerializer.serializeSitemap(entries);

      // Cache the result
      await this.cacheManager.cacheSitemap(contentType as any, result.xml, locale);

      return {
        xml: result.xml,
        urlCount: entries.length,
        isValid: result.isValid,
        validationErrors: result.validationErrors,
      };
    });

    return {
      ...result,
      generationTime: executionTime,
    };
  }

  /**
   * Gets estimated counts for all content types
   */
  async getEstimatedCounts(): Promise<Record<string, number>> {
    const counts: Record<string, number> = {};

    for (const [contentType, generator] of this.contentGenerators) {
      try {
        counts[contentType] = await generator.getEstimatedCount();
      } catch (error) {
        console.error(`Error getting estimated count for ${contentType}:`, error);
        counts[contentType] = 0;
      }
    }

    return counts;
  }

  /**
   * Gets last modified dates for all content types
   */
  async getLastModifiedDates(): Promise<Record<string, Date>> {
    const dates: Record<string, Date> = {};

    for (const [contentType, generator] of this.contentGenerators) {
      try {
        dates[contentType] = await generator.getLastModified();
      } catch (error) {
        console.error(`Error getting last modified date for ${contentType}:`, error);
        dates[contentType] = new Date();
      }
    }

    return dates;
  }

  /**
   * Determines if sitemap index should be used
   */
  async shouldUseSitemapIndex(): Promise<boolean> {
    const estimatedCounts = await this.getEstimatedCounts();
    const totalUrls = Object.values(estimatedCounts).reduce((sum, count) => sum + count, 0);
    
    return shouldUseSitemapIndex(totalUrls);
  }

  /**
   * Invalidates cache for all content types
   */
  async invalidateAllCache(): Promise<void> {
    await this.cacheManager.invalidateCache();
  }

  /**
   * Invalidates cache for specific content type
   */
  async invalidateContentTypeCache(contentType: string, locale?: string): Promise<void> {
    await this.cacheManager.invalidateCache(contentType as any, locale);
  }

  /**
   * Warms up cache for all content types
   */
  async warmCache(locales: string[] = SITEMAP_CONFIG.locales): Promise<void> {
    const contentTypes = Array.from(this.contentGenerators.keys());
    
    await this.cacheManager.warmCache(
      contentTypes as any[],
      locales,
      async (contentType, locale) => {
        const result = await this.generateContentTypeSitemap(contentType, locale);
        return result.xml;
      }
    );
  }

  /**
   * Gets sitemap generation statistics
   */
  async getGenerationStats(): Promise<{
    estimatedCounts: Record<string, number>;
    lastModifiedDates: Record<string, Date>;
    shouldUseIndex: boolean;
    cacheStats: any;
  }> {
    const [estimatedCounts, lastModifiedDates, shouldUseIndex] = await Promise.all([
      this.getEstimatedCounts(),
      this.getLastModifiedDates(),
      this.shouldUseSitemapIndex(),
    ]);

    return {
      estimatedCounts,
      lastModifiedDates,
      shouldUseIndex,
      cacheStats: this.cacheManager.getStats(),
    };
  }

  /**
   * Adds a custom content generator
   */
  addContentGenerator(contentType: string, generator: ContentGenerator): void {
    this.contentGenerators.set(contentType, generator);
  }

  /**
   * Removes a content generator
   */
  removeContentGenerator(contentType: string): boolean {
    return this.contentGenerators.delete(contentType);
  }

  /**
   * Gets all registered content types
   */
  getContentTypes(): string[] {
    return Array.from(this.contentGenerators.keys());
  }

  /**
   * Updates configuration
   */
  updateConfiguration(config: {
    urlBuilder?: URLBuilder;
    xmlSerializer?: XMLSerializer;
    cacheManager?: SitemapCacheManager;
  }): void {
    if (config.urlBuilder) {
      this.urlBuilder = config.urlBuilder;
    }
    if (config.xmlSerializer) {
      this.xmlSerializer = config.xmlSerializer;
    }
    if (config.cacheManager) {
      this.cacheManager = config.cacheManager;
    }
  }

  /**
   * Initializes default content generators
   */
  private initializeContentGenerators(): void {
    this.contentGenerators.set('static', new StaticPagesGenerator(this.urlBuilder));
    this.contentGenerators.set('posts', new BlogPostsGenerator(this.urlBuilder));
    this.contentGenerators.set('loops', new MusicLoopsGenerator(this.urlBuilder));
  }

  /**
   * Estimates URL count from XML content (rough estimation)
   */
  private estimateUrlCountFromXML(xml: string): number {
    const urlMatches = xml.match(/<url>/g);
    return urlMatches ? urlMatches.length : 0;
  }

  /**
   * Validates that all required content generators are available
   */
  private validateContentGenerators(): void {
    const requiredTypes = ['static', 'posts', 'loops'];
    const missingTypes = requiredTypes.filter(type => !this.contentGenerators.has(type));
    
    if (missingTypes.length > 0) {
      console.warn(`Missing content generators for: ${missingTypes.join(', ')}`);
    }
  }

  /**
   * Gets content generator for specific type
   */
  getContentGenerator(contentType: string): ContentGenerator | undefined {
    return this.contentGenerators.get(contentType);
  }

  /**
   * Checks if content type is supported
   */
  isContentTypeSupported(contentType: string): boolean {
    return this.contentGenerators.has(contentType);
  }
}