import jsPDF from 'jspdf';

// Add support for Unicode characters by using a more robust text encoding approach
declare module 'jspdf' {
  interface jsPDF {
    addFont(postScriptName: string, id: string, fontStyle: string): string;
  }
}

// Canvas-based text rendering for better Unicode support
interface CanvasTextOptions {
  font: string;
  fontSize: number;
  color: string;
  align: 'left' | 'center' | 'right';
  maxWidth?: number;
}

export interface CertificateData {
  trackTitle: string;
  trackUuid: string;
  createdAt: string;
  duration: number;
  format: string;
  style?: string;
  mood?: string;
  bpm?: number;
  userEmail?: string;
  userName?: string;
}

export class CertificateGenerator {
  private pdf: jsPDF;
  private pageWidth: number;
  private pageHeight: number;
  // 添加 logo PNG base64 字符串
  private logoBase64 =
    '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';

  constructor() {
    this.pdf = new jsPDF({
      orientation: 'landscape',
      unit: 'mm',
      format: 'a4',
      putOnlyUsedFonts: true,
      compress: true
    });
    this.pageWidth = this.pdf.internal.pageSize.getWidth();
    this.pageHeight = this.pdf.internal.pageSize.getHeight();

    // Set default font that handles Unicode better
    this.pdf.setFont('helvetica');
  }

  generateCertificate(data: CertificateData): Uint8Array {
    this.drawBackground();
    this.drawHeader();
    this.drawTitle();
    this.drawTrackInfo(data);
    this.drawCertificationText(data);
    this.drawFooter(data);
    this.drawBorder();

    return new Uint8Array(this.pdf.output('arraybuffer') as ArrayBuffer);
  }

  private drawBackground() {
    // Clean white background
    this.pdf.setFillColor(255, 255, 255);
    this.pdf.rect(0, 0, this.pageWidth, this.pageHeight, 'F');

    // Elegant header background
    this.pdf.setFillColor(248, 250, 252);
    this.pdf.rect(0, 0, this.pageWidth, 60, 'F');

    // Subtle watermark pattern (less dense)
    this.pdf.setFillColor(252, 253, 254);
    for (let i = 40; i < this.pageWidth - 40; i += 30) {
      for (let j = 70; j < this.pageHeight - 50; j += 30) {
        this.pdf.circle(i, j, 0.2, 'F');
      }
    }
  }

  private drawBorder() {
    // Elegant gradient-style border
    this.pdf.setDrawColor(37, 99, 235); // Blue-600
    this.pdf.setLineWidth(3);
    this.pdf.rect(8, 8, this.pageWidth - 16, this.pageHeight - 16);

    // Inner accent border
    this.pdf.setDrawColor(59, 130, 246); // Blue-500
    this.pdf.setLineWidth(1);
    this.pdf.rect(12, 12, this.pageWidth - 24, this.pageHeight - 24);

    // Decorative corner elements
    this.drawCornerDecorations();
  }

  private drawCornerDecorations() {
    const cornerSize = 15;
    const offset = 20;

    this.pdf.setDrawColor(147, 197, 253); // Blue-300
    this.pdf.setLineWidth(2);

    // Top-left corner
    this.pdf.line(offset, offset, offset + cornerSize, offset);
    this.pdf.line(offset, offset, offset, offset + cornerSize);

    // Top-right corner
    this.pdf.line(this.pageWidth - offset, offset, this.pageWidth - offset - cornerSize, offset);
    this.pdf.line(this.pageWidth - offset, offset, this.pageWidth - offset, offset + cornerSize);

    // Bottom-left corner
    this.pdf.line(offset, this.pageHeight - offset, offset + cornerSize, this.pageHeight - offset);
    this.pdf.line(offset, this.pageHeight - offset, offset, this.pageHeight - offset - cornerSize);

    // Bottom-right corner
    this.pdf.line(this.pageWidth - offset, this.pageHeight - offset, this.pageWidth - offset - cornerSize, this.pageHeight - offset);
    this.pdf.line(this.pageWidth - offset, this.pageHeight - offset, this.pageWidth - offset, this.pageHeight - offset - cornerSize);
  }

  private drawHeader() {
    // 插入 PNG base64 logo
    this.pdf.addImage(
      this.logoBase64,
      'PNG',
      30, 25, // x, y
      20, 20 // width, height
    );

    // Company name
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.setFontSize(28);
    this.pdf.setTextColor(15, 23, 42); // Slate-900
    this.addTextSafely('LoopCraft', 55, 38);

    // Tagline
    this.pdf.setFont('helvetica', 'normal');
    this.pdf.setFontSize(14);
    this.pdf.setTextColor(71, 85, 105); // Slate-600
    this.addTextSafely('AI Music Generation Platform', 55, 45);
  }

  private drawTitle() {
    // Main title with elegant styling
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.setFontSize(38);
    this.pdf.setTextColor(15, 23, 42); // Slate-900

    const title = 'CERTIFICATE OF AUTHENTICITY';
    this.addTextSafely(title, this.pageWidth / 2, 80, { align: 'center' });

    // Subtitle with improved styling
    this.pdf.setFont('helvetica', 'normal');
    this.pdf.setFontSize(18);
    this.pdf.setTextColor(71, 85, 105); // Slate-600

    const subtitle = 'AI-Generated Music Track';
    this.addTextSafely(subtitle, this.pageWidth / 2, 92, { align: 'center' });

    // Decorative elements
    this.pdf.setDrawColor(37, 99, 235); // Blue-600
    this.pdf.setLineWidth(1.5);
    this.pdf.line(this.pageWidth / 2 - 50, 98, this.pageWidth / 2 + 50, 98);

    // Small decorative dots
    this.pdf.setFillColor(59, 130, 246); // Blue-500
    this.pdf.circle(this.pageWidth / 2 - 60, 98, 1.5, 'F');
    this.pdf.circle(this.pageWidth / 2 + 60, 98, 1.5, 'F');
  }

  private drawTrackInfo(data: CertificateData) {
    const startY = 115;
    const leftCol = 60;
    const rightCol = 190;

    // Track title with elegant styling and smart truncation
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.setFontSize(22);
    this.pdf.setTextColor(15, 23, 42); // Slate-900

    const originalTitle = data.trackTitle;
    const maxWidth = this.pageWidth - 100; // Leave more margins for safety
    const quotedTitle = `"${originalTitle}"`;

    // Check if title contains non-Latin characters
    const hasNonLatinChars = this.hasNonLatinCharacters(originalTitle);

    if (hasNonLatinChars) {
      // Use canvas-based rendering for non-Latin characters
      const maxChars = Math.floor(maxWidth / 8); // Rough estimate for character limit
      let finalTitle = quotedTitle;

      if (quotedTitle.length > maxChars) {
        // For non-Latin text, truncate more aggressively since characters may be wider
        const truncateLength = Math.floor(maxChars * 0.7);
        finalTitle = quotedTitle.substring(0, truncateLength) + '...';
      }

      // Try to render as image first with appropriate fonts
      const success = this.addTextAsImage(finalTitle, this.pageWidth / 2, startY, {
        fontSize: 22,
        color: '#0f172a', // Slate-900 in hex
        align: 'center',
        font: 'Arial, "Microsoft YaHei", "SimHei", "Noto Sans", "DejaVu Sans", sans-serif'
      });

      if (!success) {
        // Fallback to converted text
        const safeTitle = this.handleNonLatinCharacters(originalTitle);
        const safeFinalTitle = `"${safeTitle}"`;
        const truncatedSafe = this.truncateText(safeFinalTitle, maxWidth, 22);
        this.addTextSafely(truncatedSafe, this.pageWidth / 2, startY, { align: 'center' });
      }
    } else {
      // Regular text handling for Latin characters
      const titleWidth = this.pdf.getTextWidth(quotedTitle);
      let finalTitle = quotedTitle;

      if (titleWidth > maxWidth) {
        finalTitle = this.truncateText(quotedTitle, maxWidth, 22);
      }

      this.addTextSafely(finalTitle, this.pageWidth / 2, startY, { align: 'center' });
    }

    // Background for track details
    this.pdf.setFillColor(248, 250, 252); // Very light blue-gray
    this.pdf.roundedRect(40, startY + 10, this.pageWidth - 80, 40, 3, 3, 'F');

    // Track details in two columns with improved styling
    this.pdf.setFont('helvetica', 'normal');
    this.pdf.setFontSize(12);
    this.pdf.setTextColor(51, 65, 85); // Slate-700

    let currentY = startY + 22;

    // Left column
    this.drawInfoRow('Track ID:', data.trackUuid.substring(0, 8).toUpperCase(), leftCol, currentY);
    currentY += 10;
    this.drawInfoRow('Duration:', `${data.duration} seconds`, leftCol, currentY);
    currentY += 10;
    this.drawInfoRow('Format:', data.format.toUpperCase(), leftCol, currentY);

    // Right column
    currentY = startY + 22;
    if (data.style) {
      this.drawInfoRow('Style:', data.style, rightCol, currentY);
      currentY += 10;
    }
    if (data.mood) {
      this.drawInfoRow('Mood:', data.mood, rightCol, currentY);
      currentY += 10;
    }
    if (data.bpm) {
      this.drawInfoRow('BPM:', data.bpm.toString(), rightCol, currentY);
    }
  }

  private drawInfoRow(label: string, value: string, x: number, y: number) {
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.setTextColor(71, 85, 105); // Slate-600
    this.addTextSafely(label, x, y);

    this.pdf.setFont('helvetica', 'normal');
    this.pdf.setTextColor(15, 23, 42); // Slate-900
    this.addTextSafely(value, x + 30, y);
  }

  private drawCertificationText(_data: CertificateData) {
    const startY = 175;
    const margin = 45;
    const textWidth = this.pageWidth - (margin * 2);

    // Background for certification text
    this.pdf.setFillColor(248, 250, 252); // Very light blue-gray
    this.pdf.roundedRect(margin - 10, startY - 10, textWidth + 20, 60, 3, 3, 'F');

    this.pdf.setFont('helvetica', 'normal');
    this.pdf.setFontSize(13);
    this.pdf.setTextColor(51, 65, 85); // Slate-700

    const certificationText = [
      'This certificate confirms that the above music track was generated using LoopCraft\'s',
      'AI music generation service. The track is an original creation produced by artificial',
      'intelligence algorithms and is owned by the authenticated user.',
      '',
      'The holder of this certificate has the right to use this music for commercial purposes',
      'in accordance with LoopCraft\'s terms of service and applicable copyright laws.'
    ];

    let currentY = startY;
    certificationText.forEach(line => {
      if (line === '') {
        currentY += 8;
      } else {
        this.addTextSafely(line, this.pageWidth / 2, currentY, { align: 'center' });
        currentY += 8;
      }
    });
  }

  private drawFooter(data: CertificateData) {
    const footerY = this.pageHeight - 35;

    // Footer background
    this.pdf.setFillColor(248, 250, 252);
    this.pdf.rect(0, this.pageHeight - 50, this.pageWidth, 50, 'F');

    // Generation info with improved styling
    this.pdf.setFont('helvetica', 'normal');
    this.pdf.setFontSize(10);
    this.pdf.setTextColor(71, 85, 105); // Slate-600

    const generatedDate = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const certificateId = `CERT-${Date.now().toString(36).toUpperCase()}`;

    this.addTextSafely(`Generated on: ${generatedDate}`, 30, footerY);
    this.addTextSafely(`Certificate ID: ${certificateId}`, 30, footerY + 7);

    // Website with improved styling
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.setTextColor(37, 99, 235); // Blue-600
    const website = 'www.loopcraft.ai';
    this.addTextSafely(website, this.pageWidth - 30, footerY + 3, { align: 'right' });

    // Verification note with better positioning
    this.pdf.setFont('helvetica', 'italic');
    this.pdf.setFontSize(9);
    this.pdf.setTextColor(100, 116, 139); // Slate-500
    const verificationText = `Verify at: https://loopcraft.ai/verify-certificate?id=${certificateId}&track=${data.trackUuid}`;
    this.addTextSafely(verificationText, this.pageWidth / 2, this.pageHeight - 15, { align: 'center' });
  }

  /**
   * Split text into multiple lines to fit within specified width
   */
  private splitTextToFit(text: string, maxWidth: number, fontSize: number): string[] {
    this.pdf.setFontSize(fontSize);
    const words = text.split(' ');
    const lines: string[] = [];
    let currentLine = '';

    for (const word of words) {
      const testLine = currentLine ? `${currentLine} ${word}` : word;
      const testWidth = this.pdf.getTextWidth(testLine);

      if (testWidth <= maxWidth) {
        currentLine = testLine;
      } else {
        if (currentLine) {
          lines.push(currentLine);
          currentLine = word;
        } else {
          // Single word is too long, truncate it
          const truncatedWord = this.truncateText(word, maxWidth, fontSize);
          lines.push(truncatedWord);
          currentLine = '';
        }
      }
    }

    if (currentLine) {
      lines.push(currentLine);
    }

    return lines;
  }

  /**
   * Truncate text to fit within specified width with ellipsis
   */
  private truncateText(text: string, maxWidth: number, fontSize: number): string {
    this.pdf.setFontSize(fontSize);
    const ellipsis = '...';
    const ellipsisWidth = this.pdf.getTextWidth(ellipsis);

    if (this.pdf.getTextWidth(text) <= maxWidth) {
      return text;
    }

    // Start with a reasonable estimate to avoid too many iterations
    const avgCharWidth = this.pdf.getTextWidth(text) / text.length;
    const estimatedMaxChars = Math.floor((maxWidth - ellipsisWidth) / avgCharWidth);

    let truncated = text.substring(0, Math.min(estimatedMaxChars, text.length));

    // Fine-tune by removing characters until it fits
    while (truncated.length > 0 && this.pdf.getTextWidth(truncated + ellipsis) > maxWidth) {
      truncated = truncated.slice(0, -1);
    }

    // Ensure we don't end with a space or punctuation
    truncated = truncated.trim().replace(/[,\-\s]+$/, '');

    return truncated + ellipsis;
  }

  /**
   * Safely add text to PDF with Unicode support
   */
  private addTextSafely(text: string, x: number, y: number, options?: { align?: 'left' | 'center' | 'right' }) {
    try {
      // First try to handle non-Latin characters
      let processedText = this.handleNonLatinCharacters(text);
      processedText = this.sanitizeText(processedText);

      if (options?.align === 'center') {
        const textWidth = this.pdf.getTextWidth(processedText);
        x = x - (textWidth / 2);
      } else if (options?.align === 'right') {
        const textWidth = this.pdf.getTextWidth(processedText);
        x = x - textWidth;
      }

      this.pdf.text(processedText, x, y);
    } catch (error) {
      console.warn('Failed to add text to PDF, trying fallback:', error);
      // Fallback: try with ASCII-only version
      try {
        const fallbackText = this.toAsciiSafe(text);

        if (options?.align === 'center') {
          const textWidth = this.pdf.getTextWidth(fallbackText);
          x = x - (textWidth / 2);
        } else if (options?.align === 'right') {
          const textWidth = this.pdf.getTextWidth(fallbackText);
          x = x - textWidth;
        }

        this.pdf.text(fallbackText, x, y);
      } catch (fallbackError) {
        console.error('Both primary and fallback text rendering failed:', fallbackError);
        // Last resort: use a generic placeholder
        this.pdf.text('[Title]', x, y);
      }
    }
  }

  /**
   * Sanitize text for PDF generation
   */
  private sanitizeText(text: string): string {
    // Remove or replace problematic characters
    return text
      .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // Remove control characters
      .replace(/[\uFEFF]/g, '') // Remove BOM
      .trim();
  }

  /**
   * Convert text to ASCII-safe version as fallback
   */
  private toAsciiSafe(text: string): string {
    return text
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
      .replace(/[^\x00-\x7F]/g, '?') // Replace non-ASCII with ?
      .replace(/\?+/g, '[Non-ASCII Text]'); // Replace multiple ? with placeholder
  }

  /**
   * Create a canvas-based text image for better Unicode support
   */
  private createTextImage(text: string, options: CanvasTextOptions): string {
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      // Server-side rendering fallback
      return '';
    }

    try {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        return '';
      }

      // Set font and measure text
      ctx.font = `${options.fontSize}px ${options.font}`;
      const metrics = ctx.measureText(text);
      const textWidth = metrics.width;
      const textHeight = options.fontSize * 1.2; // Add some padding

      // Set canvas size
      canvas.width = Math.max(textWidth, 10);
      canvas.height = Math.max(textHeight, 10);

      // Clear and set styles
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.font = `${options.fontSize}px ${options.font}`;
      ctx.fillStyle = options.color;
      ctx.textBaseline = 'top';

      // Handle text alignment
      let x = 0;
      if (options.align === 'center') {
        x = canvas.width / 2;
        ctx.textAlign = 'center';
      } else if (options.align === 'right') {
        x = canvas.width;
        ctx.textAlign = 'right';
      } else {
        ctx.textAlign = 'left';
      }

      // Draw text
      ctx.fillText(text, x, 0);

      // Return base64 data URL
      return canvas.toDataURL('image/png');
    } catch (error) {
      console.warn('Failed to create text image:', error);
      return '';
    }
  }

  /**
   * Add text as image to PDF for better Unicode support
   */
  private addTextAsImage(text: string, x: number, y: number, options: {
    fontSize: number;
    color?: string;
    align?: 'left' | 'center' | 'right';
    font?: string;
  }) {
    const imageData = this.createTextImage(text, {
      font: options.font || 'Arial, sans-serif',
      fontSize: options.fontSize,
      color: options.color || '#000000',
      align: options.align || 'left'
    });

    if (imageData) {
      try {
        // Calculate dimensions (approximate conversion from pixels to mm)
        const pixelToMm = 0.264583; // 96 DPI to mm conversion
        const imgWidth = text.length * options.fontSize * 0.6 * pixelToMm;
        const imgHeight = options.fontSize * 1.2 * pixelToMm;

        // Adjust x position for alignment
        let adjustedX = x;
        if (options.align === 'center') {
          adjustedX = x - (imgWidth / 2);
        } else if (options.align === 'right') {
          adjustedX = x - imgWidth;
        }

        this.pdf.addImage(imageData, 'PNG', adjustedX, y - imgHeight + 2, imgWidth, imgHeight);
        return true;
      } catch (error) {
        console.warn('Failed to add text image to PDF:', error);
        return false;
      }
    }
    return false;
  }

  /**
   * Handle non-Latin characters by converting to safe representation
   * Supports Chinese, Japanese, Korean, Arabic, Russian, and other scripts
   */
  private handleNonLatinCharacters(text: string): string {
    // Define Unicode ranges for different scripts
    const scriptRanges = {
      chinese: /[\u4e00-\u9fff]/g,           // CJK Unified Ideographs
      japanese: /[\u3040-\u309f\u30a0-\u30ff]/g, // Hiragana + Katakana
      korean: /[\uac00-\ud7af]/g,            // Hangul Syllables
      arabic: /[\u0600-\u06ff]/g,            // Arabic
      russian: /[\u0400-\u04ff]/g,           // Cyrillic
      thai: /[\u0e00-\u0e7f]/g,              // Thai
      hebrew: /[\u0590-\u05ff]/g,            // Hebrew
      devanagari: /[\u0900-\u097f]/g,        // Hindi/Sanskrit
      greek: /[\u0370-\u03ff]/g,             // Greek
      armenian: /[\u0530-\u058f]/g,          // Armenian
      georgian: /[\u10a0-\u10ff]/g,          // Georgian
    };

    // Check which scripts are present
    const detectedScripts: string[] = [];
    let hasNonLatin = false;

    Object.entries(scriptRanges).forEach(([script, regex]) => {
      if (regex.test(text)) {
        detectedScripts.push(script);
        hasNonLatin = true;
      }
    });

    if (!hasNonLatin) {
      return text; // No non-Latin characters, return as is
    }

    // Extract Latin characters (English, numbers, punctuation)
    const latinParts = text.replace(/[^\u0000-\u024f\u1e00-\u1eff]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();

    // Check if we have meaningful Latin content
    if (latinParts && latinParts.length > 3 && !/^[\s\-_.,!@#$%^&*()0-9]*$/.test(latinParts)) {
      // Use the Latin parts with a note about the non-Latin content
      const scriptNames = this.getScriptDisplayNames(detectedScripts);
      return `${latinParts} (${scriptNames} Title)`;
    }

    // If no meaningful Latin content, provide a descriptive placeholder
    const textLength = text.length;
    const scriptNames = this.getScriptDisplayNames(detectedScripts);

    if (textLength > 20) {
      return `${scriptNames} Music Title (Long)`;
    } else if (textLength > 10) {
      return `${scriptNames} Music Title`;
    } else {
      return `${scriptNames} Title`;
    }
  }

  /**
   * Get display names for detected scripts
   */
  private getScriptDisplayNames(scripts: string[]): string {
    const displayNames: { [key: string]: string } = {
      chinese: 'Chinese',
      japanese: 'Japanese',
      korean: 'Korean',
      arabic: 'Arabic',
      russian: 'Russian',
      thai: 'Thai',
      hebrew: 'Hebrew',
      devanagari: 'Hindi',
      greek: 'Greek',
      armenian: 'Armenian',
      georgian: 'Georgian'
    };

    if (scripts.length === 0) {
      return 'Non-Latin';
    } else if (scripts.length === 1) {
      return displayNames[scripts[0]] || 'Non-Latin';
    } else {
      // Multiple scripts detected
      const names = scripts.map(script => displayNames[script] || 'Unknown').slice(0, 2);
      return names.join('/');
    }
  }

  /**
   * Check if text contains non-Latin characters
   */
  private hasNonLatinCharacters(text: string): boolean {
    // Check for any characters outside the basic Latin range
    return /[^\u0000-\u024f\u1e00-\u1eff]/.test(text);
  }

  /**
   * Legacy method for backward compatibility
   */
  private handleChineseCharacters(text: string): string {
    return this.handleNonLatinCharacters(text);
  }
}
