import type { SitemapContentType } from "@/types/sitemap";

/**
 * Sitemap generation metrics
 */
export interface SitemapMetrics {
  contentType: SitemapContentType | "index";
  locale: string;
  urlCount: number;
  generationTime: number;
  cacheHit: boolean;
  errorOccurred: boolean;
  errorType?: string;
  timestamp: Date;
  size: number; // XML size in bytes
}

/**
 * Sitemap analytics summary
 */
export interface SitemapAnalyticsSummary {
  totalGenerations: number;
  totalErrors: number;
  averageGenerationTime: number;
  cacheHitRate: number;
  contentCoverage: Record<string, number>;
  localeCoverage: Record<string, number>;
  errorsByType: Record<string, number>;
  performanceMetrics: {
    fastest: number;
    slowest: number;
    median: number;
  };
}

/**
 * Sitemap Analytics Service
 * Tracks and analyzes sitemap generation metrics
 */
export class SitemapAnalytics {
  private metrics: SitemapMetrics[] = [];
  private maxMetricsHistory = 1000; // Keep last 1000 metrics

  /**
   * Records a sitemap generation event
   */
  recordGeneration(metrics: Omit<SitemapMetrics, "timestamp">): void {
    const fullMetrics: SitemapMetrics = {
      ...metrics,
      timestamp: new Date(),
    };

    this.metrics.push(fullMetrics);

    // Keep only the most recent metrics
    if (this.metrics.length > this.maxMetricsHistory) {
      this.metrics = this.metrics.slice(-this.maxMetricsHistory);
    }

    // Log the metrics
    this.logMetrics(fullMetrics);
  }

  /**
   * Gets analytics summary for a time period
   */
  getAnalyticsSummary(
    startDate?: Date,
    endDate?: Date
  ): SitemapAnalyticsSummary {
    const filteredMetrics = this.filterMetricsByDate(startDate, endDate);

    if (filteredMetrics.length === 0) {
      return this.getEmptyAnalyticsSummary();
    }

    const totalGenerations = filteredMetrics.length;
    const totalErrors = filteredMetrics.filter(m => m.errorOccurred).length;
    const cacheHits = filteredMetrics.filter(m => m.cacheHit).length;
    const generationTimes = filteredMetrics.map(m => m.generationTime);

    return {
      totalGenerations,
      totalErrors,
      averageGenerationTime: this.calculateAverage(generationTimes),
      cacheHitRate: totalGenerations > 0 ? (cacheHits / totalGenerations) * 100 : 0,
      contentCoverage: this.calculateContentCoverage(filteredMetrics),
      localeCoverage: this.calculateLocaleCoverage(filteredMetrics),
      errorsByType: this.calculateErrorsByType(filteredMetrics),
      performanceMetrics: {
        fastest: Math.min(...generationTimes),
        slowest: Math.max(...generationTimes),
        median: this.calculateMedian(generationTimes),
      },
    };
  }

  /**
   * Gets recent error metrics for alerting
   */
  getRecentErrors(minutes: number = 60): SitemapMetrics[] {
    const cutoffTime = new Date(Date.now() - minutes * 60 * 1000);
    return this.metrics.filter(
      m => m.errorOccurred && m.timestamp >= cutoffTime
    );
  }

  /**
   * Gets performance metrics for monitoring
   */
  getPerformanceMetrics(minutes: number = 60): {
    averageTime: number;
    slowRequests: number;
    cacheHitRate: number;
    errorRate: number;
  } {
    const cutoffTime = new Date(Date.now() - minutes * 60 * 1000);
    const recentMetrics = this.metrics.filter(m => m.timestamp >= cutoffTime);

    if (recentMetrics.length === 0) {
      return {
        averageTime: 0,
        slowRequests: 0,
        cacheHitRate: 0,
        errorRate: 0,
      };
    }

    const generationTimes = recentMetrics.map(m => m.generationTime);
    const slowThreshold = 5000; // 5 seconds
    const slowRequests = generationTimes.filter(t => t > slowThreshold).length;
    const cacheHits = recentMetrics.filter(m => m.cacheHit).length;
    const errors = recentMetrics.filter(m => m.errorOccurred).length;

    return {
      averageTime: this.calculateAverage(generationTimes),
      slowRequests,
      cacheHitRate: (cacheHits / recentMetrics.length) * 100,
      errorRate: (errors / recentMetrics.length) * 100,
    };
  }

  /**
   * Gets content coverage statistics
   */
  getContentCoverage(): Record<string, { count: number; averageSize: number }> {
    const coverage: Record<string, { count: number; totalSize: number }> = {};

    for (const metric of this.metrics) {
      const key = `${metric.contentType}:${metric.locale}`;
      if (!coverage[key]) {
        coverage[key] = { count: 0, totalSize: 0 };
      }
      coverage[key].count++;
      coverage[key].totalSize += metric.size;
    }

    // Convert to average size
    const result: Record<string, { count: number; averageSize: number }> = {};
    for (const [key, data] of Object.entries(coverage)) {
      result[key] = {
        count: data.count,
        averageSize: data.count > 0 ? data.totalSize / data.count : 0,
      };
    }

    return result;
  }

  /**
   * Checks if error rate is above threshold for alerting
   */
  shouldAlert(
    errorRateThreshold: number = 10, // 10%
    timeWindowMinutes: number = 30
  ): boolean {
    const cutoffTime = new Date(Date.now() - timeWindowMinutes * 60 * 1000);
    const recentMetrics = this.metrics.filter(m => m.timestamp >= cutoffTime);

    if (recentMetrics.length < 5) {
      // Not enough data to determine alert status
      return false;
    }

    const errorCount = recentMetrics.filter(m => m.errorOccurred).length;
    const errorRate = (errorCount / recentMetrics.length) * 100;

    return errorRate > errorRateThreshold;
  }

  /**
   * Exports metrics for external monitoring systems
   */
  exportMetrics(format: "json" | "csv" = "json"): string {
    if (format === "csv") {
      return this.exportToCsv();
    }
    return JSON.stringify(this.metrics, null, 2);
  }

  /**
   * Clears old metrics to free memory
   */
  clearOldMetrics(olderThanDays: number = 7): number {
    const cutoffTime = new Date(Date.now() - olderThanDays * 24 * 60 * 60 * 1000);
    const initialCount = this.metrics.length;
    this.metrics = this.metrics.filter(m => m.timestamp >= cutoffTime);
    return initialCount - this.metrics.length;
  }

  private filterMetricsByDate(startDate?: Date, endDate?: Date): SitemapMetrics[] {
    return this.metrics.filter(metric => {
      if (startDate && metric.timestamp < startDate) return false;
      if (endDate && metric.timestamp > endDate) return false;
      return true;
    });
  }

  private calculateContentCoverage(metrics: SitemapMetrics[]): Record<string, number> {
    const coverage: Record<string, number> = {};
    for (const metric of metrics) {
      coverage[metric.contentType] = (coverage[metric.contentType] || 0) + 1;
    }
    return coverage;
  }

  private calculateLocaleCoverage(metrics: SitemapMetrics[]): Record<string, number> {
    const coverage: Record<string, number> = {};
    for (const metric of metrics) {
      coverage[metric.locale] = (coverage[metric.locale] || 0) + 1;
    }
    return coverage;
  }

  private calculateErrorsByType(metrics: SitemapMetrics[]): Record<string, number> {
    const errors: Record<string, number> = {};
    for (const metric of metrics) {
      if (metric.errorOccurred && metric.errorType) {
        errors[metric.errorType] = (errors[metric.errorType] || 0) + 1;
      }
    }
    return errors;
  }

  private calculateAverage(numbers: number[]): number {
    if (numbers.length === 0) return 0;
    return numbers.reduce((sum, num) => sum + num, 0) / numbers.length;
  }

  private calculateMedian(numbers: number[]): number {
    if (numbers.length === 0) return 0;
    const sorted = [...numbers].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0
      ? (sorted[mid - 1] + sorted[mid]) / 2
      : sorted[mid];
  }

  private getEmptyAnalyticsSummary(): SitemapAnalyticsSummary {
    return {
      totalGenerations: 0,
      totalErrors: 0,
      averageGenerationTime: 0,
      cacheHitRate: 0,
      contentCoverage: {},
      localeCoverage: {},
      errorsByType: {},
      performanceMetrics: {
        fastest: 0,
        slowest: 0,
        median: 0,
      },
    };
  }

  private logMetrics(metrics: SitemapMetrics): void {
    const logData = {
      contentType: metrics.contentType,
      locale: metrics.locale,
      urlCount: metrics.urlCount,
      generationTime: metrics.generationTime,
      cacheHit: metrics.cacheHit,
      errorOccurred: metrics.errorOccurred,
      errorType: metrics.errorType,
      size: metrics.size,
    };

    if (metrics.errorOccurred) {
      console.error("[Sitemap Analytics] Generation error:", logData);
    } else {
      console.log("[Sitemap Analytics] Generation completed:", logData);
    }
  }

  private exportToCsv(): string {
    if (this.metrics.length === 0) return "";

    const headers = [
      "timestamp",
      "contentType",
      "locale",
      "urlCount",
      "generationTime",
      "cacheHit",
      "errorOccurred",
      "errorType",
      "size",
    ];

    const rows = this.metrics.map(metric => [
      metric.timestamp.toISOString(),
      metric.contentType,
      metric.locale,
      metric.urlCount.toString(),
      metric.generationTime.toString(),
      metric.cacheHit.toString(),
      metric.errorOccurred.toString(),
      metric.errorType || "",
      metric.size.toString(),
    ]);

    return [headers.join(","), ...rows.map(row => row.join(","))].join("\n");
  }
}

// Global analytics instance
export const sitemapAnalytics = new SitemapAnalytics();