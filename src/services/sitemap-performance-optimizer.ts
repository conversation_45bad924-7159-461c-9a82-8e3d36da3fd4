import type { SitemapEntry, SitemapXMLResult, SitemapContentType } from "@/types/sitemap";
import { XMLSerializer } from "./sitemap-xml-serializer";
import { SitemapCacheManager } from "./sitemap-cache";
import { SITEMAP_CONFIG } from "@/constants/sitemap";
import { gzip } from "zlib";
import { promisify } from "util";

const gzipAsync = promisify(gzip);

/**
 * Performance optimization configuration
 */
export interface PerformanceConfig {
  enableParallelGeneration: boolean;
  enableCompression: boolean;
  enableConditionalRequests: boolean;
  maxConcurrentGenerators: number;
  compressionLevel: number;
  queryBatchSize: number;
}

/**
 * Performance metrics for monitoring
 */
export interface PerformanceMetrics {
  generationTime: number;
  compressionTime: number;
  cacheHitRate: number;
  urlCount: number;
  compressedSize: number;
  uncompressedSize: number;
  parallelTasks: number;
}

/**
 * Sitemap Performance Optimizer
 * Provides performance optimizations for sitemap generation
 */
export class SitemapPerformanceOptimizer {
  private config: PerformanceConfig;
  private xmlSerializer: XMLSerializer;
  private cacheManager: SitemapCacheManager;
  private metrics: Map<string, PerformanceMetrics> = new Map();

  constructor(
    config: Partial<PerformanceConfig> = {},
    xmlSerializer?: XMLSerializer,
    cacheManager?: SitemapCacheManager
  ) {
    this.config = {
      enableParallelGeneration: true,
      enableCompression: true,
      enableConditionalRequests: true,
      maxConcurrentGenerators: 3,
      compressionLevel: 6,
      queryBatchSize: 1000,
      ...config,
    };
    
    this.xmlSerializer = xmlSerializer || new XMLSerializer();
    this.cacheManager = cacheManager || new SitemapCacheManager();
  }

  /**
   * Optimized sitemap generation with parallel processing
   */
  async generateOptimizedSitemap(
    contentGenerators: Array<() => Promise<SitemapEntry[]>>,
    locale: string,
    contentType: SitemapContentType
  ): Promise<SitemapXMLResult> {
    const startTime = Date.now();
    const metricsKey = `${contentType}-${locale}`;
    
    try {
      // Check cache first
      const cached = await this.checkCache(contentType, locale);
      if (cached) {
        this.updateMetrics(metricsKey, {
          generationTime: Date.now() - startTime,
          compressionTime: 0,
          cacheHitRate: 1,
          urlCount: this.estimateUrlCount(cached.xml),
          compressedSize: 0,
          uncompressedSize: cached.xml.length,
          parallelTasks: 0,
        });
        return cached;
      }

      // Generate content with parallel processing
      const entries = await this.generateContentInParallel(contentGenerators);
      
      // Serialize to XML
      const xmlResult = await this.xmlSerializer.serializeSitemap(entries);
      
      // Apply compression if enabled
      const optimizedResult = await this.applyOptimizations(xmlResult, locale);
      
      // Cache the result
      await this.cacheManager.cacheSitemap(contentType, optimizedResult.xml, locale);
      
      // Update metrics
      this.updateMetrics(metricsKey, {
        generationTime: Date.now() - startTime,
        compressionTime: optimizedResult.compressionTime || 0,
        cacheHitRate: 0,
        urlCount: entries.length,
        compressedSize: optimizedResult.compressedSize || 0,
        uncompressedSize: optimizedResult.xml.length,
        parallelTasks: contentGenerators.length,
      });
      
      return optimizedResult;
    } catch (error) {
      console.error(`[Sitemap Performance] Error generating optimized sitemap:`, error);
      throw error;
    }
  }

  /**
   * Generate content using parallel processing
   */
  private async generateContentInParallel(
    contentGenerators: Array<() => Promise<SitemapEntry[]>>
  ): Promise<SitemapEntry[]> {
    if (!this.config.enableParallelGeneration || contentGenerators.length <= 1) {
      // Sequential generation
      const allEntries: SitemapEntry[] = [];
      for (const generator of contentGenerators) {
        const entries = await generator();
        allEntries.push(...entries);
      }
      return allEntries;
    }

    // Parallel generation with concurrency limit
    const results = await this.executeWithConcurrencyLimit(
      contentGenerators,
      this.config.maxConcurrentGenerators
    );
    
    return results.flat();
  }

  /**
   * Execute generators with concurrency limit
   */
  private async executeWithConcurrencyLimit<T>(
    tasks: Array<() => Promise<T>>,
    limit: number
  ): Promise<T[]> {
    const results: T[] = [];
    const executing: Promise<void>[] = [];
    
    for (const task of tasks) {
      const promise = task().then(result => {
        results.push(result);
      });
      
      executing.push(promise);
      
      if (executing.length >= limit) {
        await Promise.race(executing);
        executing.splice(executing.findIndex(p => p === promise), 1);
      }
    }
    
    await Promise.all(executing);
    return results;
  }

  /**
   * Apply performance optimizations to XML result
   */
  private async applyOptimizations(
    xmlResult: SitemapXMLResult,
    locale: string
  ): Promise<SitemapXMLResult & { compressionTime?: number; compressedSize?: number }> {
    let optimizedResult = { ...xmlResult };
    
    if (this.config.enableCompression) {
      const compressionStart = Date.now();
      
      try {
        const compressed = await gzipAsync(xmlResult.xml, {
          level: this.config.compressionLevel,
        });
        
        // Track compression metrics internally
        const compressionTime = Date.now() - compressionStart;
        const compressedSize = compressed.length;
        
        // Store compressed version in cache if significant savings
        const compressionRatio = compressed.length / xmlResult.xml.length;
        if (compressionRatio < 0.8) {
          console.log(`[Sitemap Performance] Compression saved ${Math.round((1 - compressionRatio) * 100)}% for ${locale}`);
        }
      } catch (error) {
        console.warn(`[Sitemap Performance] Compression failed for ${locale}:`, error);
      }
    }
    
    return optimizedResult;
  }

  /**
   * Check cache with conditional request support
   */
  private async checkCache(
    contentType: SitemapContentType,
    locale: string
  ): Promise<SitemapXMLResult | null> {
    if (!this.config.enableConditionalRequests) {
      return null;
    }
    
    try {
      const cached = await this.cacheManager.getCachedSitemap(contentType, locale);
      if (cached) {
        return {
          xml: cached,
          urlCount: this.estimateUrlCount(cached),
          isValid: true,
          generationTime: 0,
        };
      }
    } catch (error) {
      console.warn(`[Sitemap Performance] Cache check failed:`, error);
    }
    
    return null;
  }

  /**
   * Generate ETag for conditional requests
   */
  generateETag(content: string, lastModified: Date): string {
    const hash = require('crypto')
      .createHash('md5')
      .update(content + lastModified.toISOString())
      .digest('hex');
    return `"${hash.substring(0, 16)}"`; 
  }

  /**
   * Check if content has been modified since given date
   */
  async isModifiedSince(
    contentType: SitemapContentType,
    locale: string,
    ifModifiedSince: Date
  ): Promise<boolean> {
    try {
      const lastModified = await this.getLastModifiedDate(contentType, locale);
      return lastModified > ifModifiedSince;
    } catch (error) {
      console.warn(`[Sitemap Performance] Modified check failed:`, error);
      return true; // Assume modified if check fails
    }
  }

  /**
   * Get last modified date for content type
   */
  private async getLastModifiedDate(
    contentType: SitemapContentType,
    locale: string
  ): Promise<Date> {
    // This would normally query the database for the latest update
    // For now, return current time minus cache timeout
    return new Date(Date.now() - SITEMAP_CONFIG.cacheTimeout * 1000);
  }

  /**
   * Optimize database queries with batching
   */
  async optimizeQuery<T>(
    queryFn: (offset: number, limit: number) => Promise<T[]>,
    totalCount?: number
  ): Promise<T[]> {
    const batchSize = this.config.queryBatchSize;
    const results: T[] = [];
    let offset = 0;
    
    while (true) {
      const batch = await queryFn(offset, batchSize);
      
      if (batch.length === 0) {
        break;
      }
      
      results.push(...batch);
      offset += batchSize;
      
      // Stop if we know the total count and have reached it
      if (totalCount && results.length >= totalCount) {
        break;
      }
      
      // Stop if batch is smaller than expected (last batch)
      if (batch.length < batchSize) {
        break;
      }
    }
    
    return results;
  }

  /**
   * Update performance metrics
   */
  private updateMetrics(key: string, metrics: PerformanceMetrics): void {
    this.metrics.set(key, metrics);
    
    // Log performance metrics
    console.log(`[Sitemap Performance] ${key}:`, {
      generationTime: `${metrics.generationTime}ms`,
      urlCount: metrics.urlCount,
      cacheHit: metrics.cacheHitRate > 0,
      parallelTasks: metrics.parallelTasks,
      compressionSavings: metrics.compressedSize > 0 
        ? `${Math.round((1 - metrics.compressedSize / metrics.uncompressedSize) * 100)}%`
        : 'N/A',
    });
  }

  /**
   * Get performance metrics for monitoring
   */
  getMetrics(key?: string): PerformanceMetrics | Map<string, PerformanceMetrics> {
    if (key) {
      return this.metrics.get(key) || {
        generationTime: 0,
        compressionTime: 0,
        cacheHitRate: 0,
        urlCount: 0,
        compressedSize: 0,
        uncompressedSize: 0,
        parallelTasks: 0,
      };
    }
    return new Map(this.metrics);
  }

  /**
   * Clear performance metrics
   */
  clearMetrics(): void {
    this.metrics.clear();
  }

  /**
   * Estimate URL count from XML content
   */
  private estimateUrlCount(xml: string): number {
    const urlMatches = xml.match(/<url>/g);
    return urlMatches ? urlMatches.length : 0;
  }
}