import type { 
  SitemapEntry, 
  AlternateLink, 
  SitemapXMLResult,
  ContentGenerator 
} from "@/types/sitemap";
import { URLBuilder } from "./sitemap-url-builder";
import { XMLSerializer } from "./sitemap-xml-serializer";
import { SitemapCacheManager } from "./sitemap-cache";
import { SitemapIndexGenerator } from "./sitemap-index-generator";
import { SITEMAP_CONFIG } from "@/constants/sitemap";
import { locales, defaultLocale } from "@/i18n/locale";

/**
 * Multi-language Sitemap Service
 * Provides enhanced internationalization support for sitemap generation
 */
export class MultiLanguageSitemapService {
  private urlBuilder: URLBuilder;
  private xmlSerializer: XMLSerializer;
  private cacheManager: SitemapCacheManager;
  private sitemapGenerator: SitemapIndexGenerator;
  private supportedLocales: string[];
  private defaultLocale: string;

  constructor(
    urlBuilder?: URLBuilder,
    xmlSerializer?: XMLSerializer,
    cacheManager?: SitemapCacheManager,
    sitemapGenerator?: SitemapIndexGenerator
  ) {
    this.urlBuilder = urlBuilder || new URLBuilder();
    this.xmlSerializer = xmlSerializer || new XMLSerializer();
    this.cacheManager = cacheManager || new SitemapCacheManager();
    this.sitemapGenerator = sitemapGenerator || new SitemapIndexGenerator();
    this.supportedLocales = locales;
    this.defaultLocale = defaultLocale;
  }

  /**
   * Generates sitemap with proper hreflang annotations for all locales
   */
  async generateMultiLanguageSitemap(
    contentType: string,
    primaryLocale: string = this.defaultLocale
  ): Promise<SitemapXMLResult> {
    const startTime = Date.now();

    try {
      // Get content for all locales
      const localeEntries = await this.getEntriesForAllLocales(contentType);
      
      // Group entries by path to create hreflang relationships
      const groupedEntries = this.groupEntriesByPath(localeEntries);
      
      // Generate entries with hreflang annotations
      const enhancedEntries = this.enhanceEntriesWithHreflang(groupedEntries, primaryLocale);
      
      // Generate XML
      const result = await this.xmlSerializer.serializeSitemap(enhancedEntries);
      
      return {
        ...result,
        generationTime: Date.now() - startTime,
      };
    } catch (error) {
      console.error(`[MultiLang Sitemap] Error generating ${contentType} sitemap:`, error);
      throw error;
    }
  }

  /**
   * Generates sitemap index with locale-specific sitemaps
   */
  async generateMultiLanguageSitemapIndex(): Promise<SitemapXMLResult> {
    const startTime = Date.now();

    try {
      const indexEntries = [];
      const baseUrl = this.urlBuilder.getBaseUrl();

      // Generate index entries for each content type and locale combination
      const contentTypes = this.sitemapGenerator.getContentTypes();
      
      for (const contentType of contentTypes) {
        for (const locale of this.supportedLocales) {
          try {
            const generator = this.sitemapGenerator.getContentGenerator(contentType);
            if (!generator) continue;

            const estimatedCount = await generator.getEstimatedCount();
            if (estimatedCount === 0) continue;

            const lastModified = await generator.getLastModified();
            
            // Create locale-specific sitemap URL
            const sitemapUrl = this.buildLocaleSitemapUrl(baseUrl, contentType, locale);
            
            indexEntries.push({
              loc: sitemapUrl,
              lastmod: lastModified.toISOString(),
            });
          } catch (error) {
            console.error(`[MultiLang Sitemap] Error processing ${contentType}:${locale}:`, error);
            // Continue with other combinations
          }
        }
      }

      // Generate XML for sitemap index
      const result = await this.xmlSerializer.serializeSitemapIndex(indexEntries);
      
      return {
        ...result,
        generationTime: Date.now() - startTime,
      };
    } catch (error) {
      console.error("[MultiLang Sitemap] Error generating sitemap index:", error);
      throw error;
    }
  }

  /**
   * Validates hreflang implementation across all locales
   */
  async validateHreflangImplementation(contentType: string): Promise<{
    isValid: boolean;
    issues: string[];
    statistics: {
      totalUrls: number;
      urlsWithHreflang: number;
      localeDistribution: Record<string, number>;
      missingAlternates: string[];
    };
  }> {
    const issues: string[] = [];
    const statistics = {
      totalUrls: 0,
      urlsWithHreflang: 0,
      localeDistribution: {} as Record<string, number>,
      missingAlternates: [] as string[],
    };

    try {
      // Get entries for all locales
      const localeEntries = await this.getEntriesForAllLocales(contentType);
      
      // Initialize locale distribution
      this.supportedLocales.forEach(locale => {
        statistics.localeDistribution[locale] = 0;
      });

      // Analyze entries
      const pathGroups = this.groupEntriesByPath(localeEntries);
      
      for (const [path, entries] of Object.entries(pathGroups)) {
        statistics.totalUrls += entries.length;
        
        // Check if all locales are represented
        const representedLocales = new Set(entries.map(entry => this.extractLocaleFromUrl(entry.url)));
        const missingLocales = this.supportedLocales.filter(locale => !representedLocales.has(locale));
        
        if (missingLocales.length > 0) {
          statistics.missingAlternates.push(`${path}: missing ${missingLocales.join(', ')}`);
        }

        // Count locale distribution
        entries.forEach(entry => {
          const locale = this.extractLocaleFromUrl(entry.url);
          if (statistics.localeDistribution[locale] !== undefined) {
            statistics.localeDistribution[locale]++;
          }
          
          // Check hreflang annotations
          if (entry.alternates && entry.alternates.length > 0) {
            statistics.urlsWithHreflang++;
            
            // Validate hreflang completeness
            const hreflangLocales = new Set(entry.alternates.map(alt => alt.hreflang));
            const missingHreflang = this.supportedLocales.filter(locale => !hreflangLocales.has(locale));
            
            if (missingHreflang.length > 0) {
              issues.push(`${entry.url}: missing hreflang for ${missingHreflang.join(', ')}`);
            }
          } else {
            issues.push(`${entry.url}: no hreflang annotations`);
          }
        });
      }

      return {
        isValid: issues.length === 0,
        issues,
        statistics,
      };
    } catch (error) {
      issues.push(`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return {
        isValid: false,
        issues,
        statistics,
      };
    }
  }

  /**
   * Generates locale-specific sitemap for a content type
   */
  async generateLocaleSitemap(
    contentType: string,
    locale: string
  ): Promise<SitemapXMLResult> {
    const cacheKey = `${contentType}:${locale}:multilang`;
    
    // Check cache first
    const cached = await this.cacheManager.getCachedSitemap(contentType as any, locale);
    if (cached) {
      return {
        xml: cached,
        urlCount: this.estimateUrlCountFromXML(cached),
        isValid: true,
        generationTime: 0, // Cached
      };
    }

    // Generate fresh content
    const result = await this.sitemapGenerator.generateContentTypeSitemap(contentType, locale);
    
    // Cache the result
    await this.cacheManager.cacheSitemap(contentType as any, result.xml, locale);
    
    return result;
  }

  /**
   * Warms cache for all locale combinations
   */
  async warmMultiLanguageCache(): Promise<void> {
    const contentTypes = this.sitemapGenerator.getContentTypes();
    const warmupPromises: Promise<void>[] = [];

    for (const contentType of contentTypes) {
      for (const locale of this.supportedLocales) {
        const promise = this.warmSingleLocaleCache(contentType, locale);
        warmupPromises.push(promise);
      }
    }

    await Promise.allSettled(warmupPromises);
    console.log(`[MultiLang Sitemap] Cache warmed for ${contentTypes.length} content types and ${this.supportedLocales.length} locales`);
  }

  /**
   * Gets sitemap statistics for all locales
   */
  async getMultiLanguageStatistics(): Promise<{
    totalUrls: number;
    urlsByLocale: Record<string, number>;
    urlsByContentType: Record<string, number>;
    cacheHitRate: number;
    lastGenerated: Date;
  }> {
    const stats = {
      totalUrls: 0,
      urlsByLocale: {} as Record<string, number>,
      urlsByContentType: {} as Record<string, number>,
      cacheHitRate: 0,
      lastGenerated: new Date(),
    };

    // Initialize counters
    this.supportedLocales.forEach(locale => {
      stats.urlsByLocale[locale] = 0;
    });

    const contentTypes = this.sitemapGenerator.getContentTypes();
    contentTypes.forEach(contentType => {
      stats.urlsByContentType[contentType] = 0;
    });

    // Get statistics from generators
    for (const contentType of contentTypes) {
      try {
        const generator = this.sitemapGenerator.getContentGenerator(contentType);
        if (!generator) continue;

        const estimatedCount = await generator.getEstimatedCount();
        stats.urlsByContentType[contentType] = estimatedCount;
        stats.totalUrls += estimatedCount;
      } catch (error) {
        console.error(`[MultiLang Sitemap] Error getting stats for ${contentType}:`, error);
      }
    }

    // Distribute URLs across locales (approximation)
    const urlsPerLocale = Math.floor(stats.totalUrls / this.supportedLocales.length);
    this.supportedLocales.forEach(locale => {
      stats.urlsByLocale[locale] = urlsPerLocale;
    });

    // Get cache statistics
    stats.cacheHitRate = this.cacheManager.getStats().hitRate;

    return stats;
  }

  /**
   * Gets entries for all locales for a content type
   */
  private async getEntriesForAllLocales(contentType: string): Promise<Record<string, SitemapEntry[]>> {
    const localeEntries: Record<string, SitemapEntry[]> = {};

    for (const locale of this.supportedLocales) {
      try {
        const result = await this.sitemapGenerator.generateContentTypeSitemap(contentType, locale);
        localeEntries[locale] = this.parseEntriesFromXML(result.xml);
      } catch (error) {
        console.error(`[MultiLang Sitemap] Error getting entries for ${contentType}:${locale}:`, error);
        localeEntries[locale] = [];
      }
    }

    return localeEntries;
  }

  /**
   * Groups entries by their path (without locale prefix)
   */
  private groupEntriesByPath(localeEntries: Record<string, SitemapEntry[]>): Record<string, SitemapEntry[]> {
    const grouped: Record<string, SitemapEntry[]> = {};

    for (const [locale, entries] of Object.entries(localeEntries)) {
      for (const entry of entries) {
        const path = this.urlBuilder.extractPathFromUrl(entry.url);
        if (!grouped[path]) {
          grouped[path] = [];
        }
        grouped[path].push(entry);
      }
    }

    return grouped;
  }

  /**
   * Enhances entries with proper hreflang annotations
   */
  private enhanceEntriesWithHreflang(
    groupedEntries: Record<string, SitemapEntry[]>,
    primaryLocale: string
  ): SitemapEntry[] {
    const enhancedEntries: SitemapEntry[] = [];

    for (const [path, entries] of Object.entries(groupedEntries)) {
      // Find the primary locale entry or use the first available
      const primaryEntry = entries.find(entry => 
        this.extractLocaleFromUrl(entry.url) === primaryLocale
      ) || entries[0];

      if (!primaryEntry) continue;

      // Create hreflang alternates for all available locales
      const alternates: AlternateLink[] = entries.map(entry => ({
        href: entry.url,
        hreflang: this.extractLocaleFromUrl(entry.url),
      }));

      // Add the enhanced entry
      enhancedEntries.push({
        ...primaryEntry,
        alternates,
      });
    }

    return enhancedEntries;
  }

  /**
   * Builds locale-specific sitemap URL
   */
  private buildLocaleSitemapUrl(baseUrl: string, contentType: string, locale: string): string {
    const localePrefix = locale === this.defaultLocale ? '' : `/${locale}`;
    return `${baseUrl}${localePrefix}/api/sitemap/${contentType}`;
  }

  /**
   * Extracts locale from URL
   */
  private extractLocaleFromUrl(url: string): string {
    return this.urlBuilder.extractLocaleFromUrl(url);
  }

  /**
   * Parses sitemap entries from XML (simplified implementation)
   */
  private parseEntriesFromXML(xml: string): SitemapEntry[] {
    // This is a simplified parser - in production, you might want to use a proper XML parser
    const entries: SitemapEntry[] = [];
    const urlMatches = xml.match(/<url>([\s\S]*?)<\/url>/g);
    
    if (urlMatches) {
      for (const urlMatch of urlMatches) {
        const locMatch = urlMatch.match(/<loc>(.*?)<\/loc>/);
        const lastmodMatch = urlMatch.match(/<lastmod>(.*?)<\/lastmod>/);
        const changefreqMatch = urlMatch.match(/<changefreq>(.*?)<\/changefreq>/);
        const priorityMatch = urlMatch.match(/<priority>(.*?)<\/priority>/);
        
        if (locMatch) {
          entries.push({
            url: locMatch[1],
            lastmod: lastmodMatch?.[1] || new Date().toISOString(),
            changefreq: (changefreqMatch?.[1] as any) || 'weekly',
            priority: priorityMatch?.[1] || '0.5',
          });
        }
      }
    }
    
    return entries;
  }

  /**
   * Estimates URL count from XML
   */
  private estimateUrlCountFromXML(xml: string): number {
    const urlMatches = xml.match(/<url>/g);
    return urlMatches ? urlMatches.length : 0;
  }

  /**
   * Warms cache for a single locale and content type
   */
  private async warmSingleLocaleCache(contentType: string, locale: string): Promise<void> {
    try {
      await this.generateLocaleSitemap(contentType, locale);
    } catch (error) {
      console.error(`[MultiLang Sitemap] Failed to warm cache for ${contentType}:${locale}:`, error);
    }
  }

  /**
   * Gets supported locales
   */
  getSupportedLocales(): string[] {
    return [...this.supportedLocales];
  }

  /**
   * Gets default locale
   */
  getDefaultLocale(): string {
    return this.defaultLocale;
  }

  /**
   * Checks if a locale is supported
   */
  isLocaleSupported(locale: string): boolean {
    return this.supportedLocales.includes(locale);
  }
}