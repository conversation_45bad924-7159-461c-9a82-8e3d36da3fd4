import type { 
  SitemapXMLResult,
  SitemapContentType 
} from "@/types/sitemap";
import { SitemapCacheManager } from "./sitemap-cache";
import { XMLSerializer } from "./sitemap-xml-serializer";
import { URLBuilder } from "./sitemap-url-builder";
import { 
  SITEMAP_CONFIG, 
  SITEMAP_ERROR_MESSAGES,
  PERFORMANCE_THRESHOLDS 
} from "@/constants/sitemap";

/**
 * Error types for sitemap operations
 */
export enum SitemapErrorType {
  DATABASE_ERROR = "DATABASE_ERROR",
  GENERATION_ERROR = "GENERATION_ERROR",
  CACHE_ERROR = "CACHE_ERROR",
  VALIDATION_ERROR = "VALIDATION_ERROR",
  TIMEOUT_ERROR = "TIMEOUT_ERROR",
  NETWORK_ERROR = "NETWORK_ERROR",
  CONTENT_ERROR = "CONTENT_ERROR",
  XML_ERROR = "XML_ERROR",
}

/**
 * Sitemap error with context information
 */
export class SitemapError extends Error {
  public readonly type: SitemapErrorType;
  public readonly context: Record<string, any>;
  public readonly timestamp: Date;
  public readonly recoverable: boolean;

  constructor(
    message: string,
    type: SitemapErrorType,
    context: Record<string, any> = {},
    recoverable: boolean = true
  ) {
    super(message);
    this.name = "SitemapError";
    this.type = type;
    this.context = context;
    this.timestamp = new Date();
    this.recoverable = recoverable;
  }
}

/**
 * Recovery strategy interface
 */
interface RecoveryStrategy {
  canRecover(error: SitemapError): boolean;
  recover(error: SitemapError, context: RecoveryContext): Promise<SitemapXMLResult>;
  priority: number; // Lower number = higher priority
}

/**
 * Recovery context
 */
interface RecoveryContext {
  contentType?: SitemapContentType;
  locale?: string;
  cacheManager: SitemapCacheManager;
  xmlSerializer: XMLSerializer;
  urlBuilder: URLBuilder;
  originalRequest?: any;
}

/**
 * Sitemap Error Handler and Recovery System
 */
export class SitemapErrorHandler {
  private cacheManager: SitemapCacheManager;
  private xmlSerializer: XMLSerializer;
  private urlBuilder: URLBuilder;
  private recoveryStrategies: RecoveryStrategy[];
  private errorStats: Map<SitemapErrorType, number>;
  private lastErrors: SitemapError[];
  private maxErrorHistory: number = 100;

  constructor(
    cacheManager: SitemapCacheManager,
    xmlSerializer: XMLSerializer,
    urlBuilder: URLBuilder
  ) {
    this.cacheManager = cacheManager;
    this.xmlSerializer = xmlSerializer;
    this.urlBuilder = urlBuilder;
    this.errorStats = new Map();
    this.lastErrors = [];
    this.recoveryStrategies = this.initializeRecoveryStrategies();
  }

  /**
   * Handles sitemap errors with automatic recovery
   */
  async handleError(
    error: Error | SitemapError,
    context: Partial<RecoveryContext> = {}
  ): Promise<SitemapXMLResult> {
    const sitemapError = this.normalizeSitemapError(error, context);
    
    // Log the error
    this.logError(sitemapError);
    
    // Update error statistics
    this.updateErrorStats(sitemapError);
    
    // Store error in history
    this.addToErrorHistory(sitemapError);

    // Attempt recovery if the error is recoverable
    if (sitemapError.recoverable) {
      const recoveryContext: RecoveryContext = {
        cacheManager: this.cacheManager,
        xmlSerializer: this.xmlSerializer,
        urlBuilder: this.urlBuilder,
        ...context,
      };

      for (const strategy of this.recoveryStrategies) {
        if (strategy.canRecover(sitemapError)) {
          try {
            console.log(`[Sitemap Recovery] Attempting recovery with ${strategy.constructor.name}`);
            const result = await strategy.recover(sitemapError, recoveryContext);
            
            console.log(`[Sitemap Recovery] Successfully recovered using ${strategy.constructor.name}`);
            return {
              ...result,
              xml: this.addRecoveryMetadata(result.xml, strategy.constructor.name),
            };
          } catch (recoveryError) {
            console.error(`[Sitemap Recovery] Recovery strategy ${strategy.constructor.name} failed:`, recoveryError);
            // Continue to next strategy
          }
        }
      }
    }

    // If all recovery strategies fail, return minimal sitemap
    console.warn("[Sitemap Recovery] All recovery strategies failed, returning minimal sitemap");
    return this.createMinimalSitemap(context.contentType, context.locale);
  }

  /**
   * Gets error statistics
   */
  getErrorStats(): {
    totalErrors: number;
    errorsByType: Record<string, number>;
    recentErrors: SitemapError[];
    errorRate: number;
  } {
    const totalErrors = Array.from(this.errorStats.values()).reduce((sum, count) => sum + count, 0);
    const errorsByType: Record<string, number> = {};
    
    for (const [type, count] of this.errorStats.entries()) {
      errorsByType[type] = count;
    }

    // Calculate error rate (errors in last hour)
    const oneHourAgo = new Date(Date.now() - 3600000);
    const recentErrorCount = this.lastErrors.filter(error => error.timestamp > oneHourAgo).length;
    const errorRate = recentErrorCount / 60; // Errors per minute

    return {
      totalErrors,
      errorsByType,
      recentErrors: this.lastErrors.slice(-10), // Last 10 errors
      errorRate,
    };
  }

  /**
   * Checks system health based on error patterns
   */
  getHealthStatus(): {
    status: 'healthy' | 'degraded' | 'unhealthy';
    issues: string[];
    recommendations: string[];
  } {
    const stats = this.getErrorStats();
    const issues: string[] = [];
    const recommendations: string[] = [];
    
    // Check error rate
    if (stats.errorRate > 5) {
      issues.push(`High error rate: ${stats.errorRate.toFixed(2)} errors/minute`);
      recommendations.push("Check database connectivity and system resources");
    }

    // Check for specific error patterns
    const databaseErrors = stats.errorsByType[SitemapErrorType.DATABASE_ERROR] || 0;
    const cacheErrors = stats.errorsByType[SitemapErrorType.CACHE_ERROR] || 0;
    const timeoutErrors = stats.errorsByType[SitemapErrorType.TIMEOUT_ERROR] || 0;

    if (databaseErrors > 10) {
      issues.push("Frequent database errors detected");
      recommendations.push("Check database connection pool and query performance");
    }

    if (cacheErrors > 5) {
      issues.push("Cache system experiencing issues");
      recommendations.push("Verify cache service availability and configuration");
    }

    if (timeoutErrors > 3) {
      issues.push("Generation timeouts occurring");
      recommendations.push("Consider increasing timeout limits or optimizing content generation");
    }

    // Determine overall status
    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    if (issues.length > 0) {
      status = stats.errorRate > 10 || issues.length > 3 ? 'unhealthy' : 'degraded';
    }

    return { status, issues, recommendations };
  }

  /**
   * Clears error history and statistics
   */
  clearErrorHistory(): void {
    this.errorStats.clear();
    this.lastErrors = [];
    console.log("[Sitemap Error Handler] Error history cleared");
  }

  /**
   * Initialize recovery strategies
   */
  private initializeRecoveryStrategies(): RecoveryStrategy[] {
    return [
      new CacheRecoveryStrategy(),
      new PartialContentRecoveryStrategy(),
      new StaticContentRecoveryStrategy(),
      new MinimalSitemapRecoveryStrategy(),
    ].sort((a, b) => a.priority - b.priority);
  }

  /**
   * Normalizes different error types to SitemapError
   */
  private normalizeSitemapError(
    error: Error | SitemapError,
    context: Partial<RecoveryContext>
  ): SitemapError {
    if (error instanceof SitemapError) {
      return error;
    }

    // Determine error type based on error message and context
    let errorType = SitemapErrorType.GENERATION_ERROR;
    let recoverable = true;

    if (error.message.includes("database") || error.message.includes("connection")) {
      errorType = SitemapErrorType.DATABASE_ERROR;
    } else if (error.message.includes("cache")) {
      errorType = SitemapErrorType.CACHE_ERROR;
    } else if (error.message.includes("timeout")) {
      errorType = SitemapErrorType.TIMEOUT_ERROR;
    } else if (error.message.includes("validation") || error.message.includes("invalid")) {
      errorType = SitemapErrorType.VALIDATION_ERROR;
    } else if (error.message.includes("xml") || error.message.includes("XML")) {
      errorType = SitemapErrorType.XML_ERROR;
    } else if (error.message.includes("network") || error.message.includes("fetch")) {
      errorType = SitemapErrorType.NETWORK_ERROR;
    }

    return new SitemapError(
      error.message,
      errorType,
      {
        originalError: error.name,
        stack: error.stack,
        ...context,
      },
      recoverable
    );
  }

  /**
   * Logs error with appropriate level
   */
  private logError(error: SitemapError): void {
    const logData = {
      type: error.type,
      message: error.message,
      context: error.context,
      timestamp: error.timestamp,
      recoverable: error.recoverable,
    };

    if (error.type === SitemapErrorType.DATABASE_ERROR || 
        error.type === SitemapErrorType.TIMEOUT_ERROR) {
      console.error("[Sitemap Error] Critical error:", logData);
    } else {
      console.warn("[Sitemap Error] Recoverable error:", logData);
    }
  }

  /**
   * Updates error statistics
   */
  private updateErrorStats(error: SitemapError): void {
    const currentCount = this.errorStats.get(error.type) || 0;
    this.errorStats.set(error.type, currentCount + 1);
  }

  /**
   * Adds error to history with size limit
   */
  private addToErrorHistory(error: SitemapError): void {
    this.lastErrors.push(error);
    
    if (this.lastErrors.length > this.maxErrorHistory) {
      this.lastErrors = this.lastErrors.slice(-this.maxErrorHistory);
    }
  }

  /**
   * Adds recovery metadata to XML
   */
  private addRecoveryMetadata(xml: string, strategyName: string): string {
    // Add recovery information as XML comment
    const recoveryComment = `<!-- Recovered using ${strategyName} at ${new Date().toISOString()} -->`;
    return xml.replace('<?xml version="1.0" encoding="UTF-8"?>', 
      `<?xml version="1.0" encoding="UTF-8"?>\n${recoveryComment}`);
  }

  /**
   * Creates minimal sitemap as last resort
   */
  private createMinimalSitemap(
    contentType?: SitemapContentType,
    locale?: string
  ): SitemapXMLResult {
    const baseUrl = this.urlBuilder.getBaseUrl();
    const url = locale && locale !== SITEMAP_CONFIG.defaultLocale 
      ? `${baseUrl}/${locale}` 
      : baseUrl;

    const xml = this.xmlSerializer.createMinimalSitemap(url);
    
    return {
      xml,
      urlCount: 1,
      isValid: true,
      generationTime: 0,
    };
  }
}

/**
 * Recovery strategy implementations
 */

class CacheRecoveryStrategy implements RecoveryStrategy {
  priority = 1;

  canRecover(error: SitemapError): boolean {
    return error.type !== SitemapErrorType.CACHE_ERROR;
  }

  async recover(error: SitemapError, context: RecoveryContext): Promise<SitemapXMLResult> {
    const { contentType, locale, cacheManager } = context;
    
    if (!contentType) {
      throw new Error("Content type required for cache recovery");
    }

    const cached = await cacheManager.getCachedSitemap(contentType, locale);
    if (!cached) {
      throw new Error("No cached content available");
    }

    return {
      xml: cached,
      urlCount: this.estimateUrlCount(cached),
      isValid: true,
      generationTime: 0,
    };
  }

  private estimateUrlCount(xml: string): number {
    const matches = xml.match(/<url>/g);
    return matches ? matches.length : 0;
  }
}

class PartialContentRecoveryStrategy implements RecoveryStrategy {
  priority = 2;

  canRecover(error: SitemapError): boolean {
    return error.type === SitemapErrorType.DATABASE_ERROR || 
           error.type === SitemapErrorType.TIMEOUT_ERROR;
  }

  async recover(error: SitemapError, context: RecoveryContext): Promise<SitemapXMLResult> {
    // Try to generate sitemap with reduced content
    // This is a simplified implementation - in practice, you'd implement
    // partial content generation based on the specific error
    throw new Error("Partial content recovery not implemented");
  }
}

class StaticContentRecoveryStrategy implements RecoveryStrategy {
  priority = 3;

  canRecover(error: SitemapError): boolean {
    return true; // Can always fall back to static content
  }

  async recover(error: SitemapError, context: RecoveryContext): Promise<SitemapXMLResult> {
    const { xmlSerializer, urlBuilder, locale } = context;
    
    // Generate sitemap with only static pages
    const baseUrl = urlBuilder.getBaseUrl();
    const staticPages = [
      { path: "", priority: "1.0", changefreq: "daily" as const },
      { path: "generate", priority: "0.9", changefreq: "daily" as const },
      { path: "explore", priority: "0.8", changefreq: "daily" as const },
      { path: "pricing", priority: "0.6", changefreq: "monthly" as const },
    ];

    const entries = staticPages.map(page => ({
      url: urlBuilder.buildUrl(locale || SITEMAP_CONFIG.defaultLocale, page.path),
      lastmod: new Date().toISOString(),
      changefreq: page.changefreq,
      priority: page.priority,
    }));

    const result = await xmlSerializer.serializeSitemap(entries);
    return result;
  }
}

class MinimalSitemapRecoveryStrategy implements RecoveryStrategy {
  priority = 4;

  canRecover(error: SitemapError): boolean {
    return true; // Always can create minimal sitemap
  }

  async recover(error: SitemapError, context: RecoveryContext): Promise<SitemapXMLResult> {
    const { xmlSerializer, urlBuilder, locale } = context;
    const baseUrl = urlBuilder.getBaseUrl();
    const url = locale && locale !== SITEMAP_CONFIG.defaultLocale 
      ? `${baseUrl}/${locale}` 
      : baseUrl;

    const xml = xmlSerializer.createMinimalSitemap(url);
    
    return {
      xml,
      urlCount: 1,
      isValid: true,
      generationTime: 0,
    };
  }
}