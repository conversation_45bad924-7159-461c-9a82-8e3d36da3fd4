import type { SitemapEntry, SitemapXMLResult } from "@/types/sitemap";
import { SITEMAP_CONFIG } from "@/constants/sitemap";

/**
 * Validation result for individual URLs
 */
export interface URLValidationResult {
  url: string;
  isValid: boolean;
  errors: string[];
  warnings: string[];
  httpStatus?: number;
  responseTime?: number;
}

/**
 * Comprehensive sitemap validation result
 */
export interface SitemapValidationResult {
  isValid: boolean;
  xmlValid: boolean;
  urlsValid: number;
  urlsInvalid: number;
  duplicateUrls: string[];
  errors: string[];
  warnings: string[];
  performance: {
    totalSize: number;
    urlCount: number;
    averageResponseTime?: number;
    slowUrls: string[];
  };
  urlValidations: URLValidationResult[];
}

/**
 * Sitemap Validator Service
 * Provides comprehensive validation and quality assurance for sitemaps
 */
export class SitemapValidator {
  private maxConcurrentRequests = 10;
  private requestTimeout = 5000; // 5 seconds
  private slowUrlThreshold = 2000; // 2 seconds

  constructor(options?: {
    maxConcurrentRequests?: number;
    requestTimeout?: number;
    slowUrlThreshold?: number;
  }) {
    if (options) {
      this.maxConcurrentRequests = options.maxConcurrentRequests ?? this.maxConcurrentRequests;
      this.requestTimeout = options.requestTimeout ?? this.requestTimeout;
      this.slowUrlThreshold = options.slowUrlThreshold ?? this.slowUrlThreshold;
    }
  }

  /**
   * Validates XML structure against sitemap protocol
   */
  validateXMLStructure(xml: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check XML declaration
    if (!xml.includes('<?xml version="1.0" encoding="UTF-8"?>')) {
      errors.push('Missing or incorrect XML declaration');
    }

    // Check root element
    if (!xml.includes('<urlset') && !xml.includes('<sitemapindex')) {
      errors.push('Missing sitemap root element (urlset or sitemapindex)');
    }

    // Check namespace
    if (!xml.includes('xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"')) {
      errors.push('Missing or incorrect sitemap namespace');
    }

    // Check for required elements in URL entries
    if (xml.includes('<urlset')) {
      const urlBlocks = xml.match(/<url>[\s\S]*?<\/url>/g) || [];
      urlBlocks.forEach((block, index) => {
        if (!block.includes('<loc>')) {
          errors.push(`URL block ${index + 1} missing required <loc> element`);
        }
        if (!block.includes('<lastmod>')) {
          errors.push(`URL block ${index + 1} missing <lastmod> element`);
        }
      });
    }

    // Check for required elements in sitemap index entries
    if (xml.includes('<sitemapindex')) {
      const sitemapBlocks = xml.match(/<sitemap>[\s\S]*?<\/sitemap>/g) || [];
      sitemapBlocks.forEach((block, index) => {
        if (!block.includes('<loc>')) {
          errors.push(`Sitemap block ${index + 1} missing required <loc> element`);
        }
        if (!block.includes('<lastmod>')) {
          errors.push(`Sitemap block ${index + 1} missing <lastmod> element`);
        }
      });
    }

    // Check XML well-formedness
    try {
      // Basic XML parsing check
      const parser = new DOMParser();
      const doc = parser.parseFromString(xml, 'text/xml');
      const parseError = doc.querySelector('parsererror');
      if (parseError) {
        errors.push(`XML parsing error: ${parseError.textContent}`);
      }
    } catch (error) {
      errors.push(`XML parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Validates sitemap entries for duplicates and format issues
   */
  validateSitemapEntries(entries: SitemapEntry[]): {
    duplicates: string[];
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];
    const urlSet = new Set<string>();
    const duplicates: string[] = [];

    for (const entry of entries) {
      // Check for duplicates
      if (urlSet.has(entry.url)) {
        duplicates.push(entry.url);
      } else {
        urlSet.add(entry.url);
      }

      // Validate URL format
      try {
        const url = new URL(entry.url);
        
        // Check protocol
        if (!['http:', 'https:'].includes(url.protocol)) {
          errors.push(`Invalid protocol for URL: ${entry.url}`);
        }

        // Check URL length
        if (entry.url.length > 2048) {
          errors.push(`URL too long (${entry.url.length} chars): ${entry.url}`);
        }

        // Check for fragments
        if (url.hash) {
          warnings.push(`URL contains fragment: ${entry.url}`);
        }

        // Check for session parameters
        if (url.search.includes('sessionid') || url.search.includes('PHPSESSID')) {
          warnings.push(`URL contains session parameters: ${entry.url}`);
        }
      } catch {
        errors.push(`Invalid URL format: ${entry.url}`);
      }

      // Validate lastmod format
      if (entry.lastmod) {
        const lastmodDate = new Date(entry.lastmod);
        if (isNaN(lastmodDate.getTime())) {
          errors.push(`Invalid lastmod date for URL: ${entry.url}`);
        } else if (lastmodDate > new Date()) {
          warnings.push(`Future lastmod date for URL: ${entry.url}`);
        }
      }

      // Validate priority
      if (entry.priority) {
        const priority = parseFloat(entry.priority);
        if (isNaN(priority) || priority < 0 || priority > 1) {
          errors.push(`Invalid priority value for URL: ${entry.url}`);
        }
      }

      // Validate changefreq
      if (entry.changefreq) {
        const validFreqs = ['always', 'hourly', 'daily', 'weekly', 'monthly', 'yearly', 'never'];
        if (!validFreqs.includes(entry.changefreq)) {
          errors.push(`Invalid changefreq value for URL: ${entry.url}`);
        }
      }
    }

    return { duplicates, errors, warnings };
  }

  /**
   * Validates URLs by making HTTP requests (accessibility check)
   */
  async validateURLAccessibility(urls: string[]): Promise<URLValidationResult[]> {
    const results: URLValidationResult[] = [];
    const chunks = this.chunkArray(urls, this.maxConcurrentRequests);

    for (const chunk of chunks) {
      const chunkResults = await Promise.all(
        chunk.map(url => this.validateSingleURL(url))
      );
      results.push(...chunkResults);
    }

    return results;
  }

  /**
   * Validates a single URL accessibility
   */
  private async validateSingleURL(url: string): Promise<URLValidationResult> {
    const result: URLValidationResult = {
      url,
      isValid: false,
      errors: [],
      warnings: [],
    };

    try {
      const startTime = Date.now();
      
      // Create AbortController for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.requestTimeout);

      const response = await fetch(url, {
        method: 'HEAD', // Use HEAD to avoid downloading content
        signal: controller.signal,
        headers: {
          'User-Agent': 'SitemapValidator/1.0 (+https://loopcraft.app/sitemap-validator)',
        },
      });

      clearTimeout(timeoutId);
      
      const responseTime = Date.now() - startTime;
      result.httpStatus = response.status;
      result.responseTime = responseTime;

      // Check response status
      if (response.status >= 200 && response.status < 300) {
        result.isValid = true;
      } else if (response.status >= 300 && response.status < 400) {
        result.warnings.push(`Redirect status: ${response.status}`);
        result.isValid = true; // Redirects are generally OK
      } else if (response.status >= 400) {
        result.errors.push(`HTTP error: ${response.status} ${response.statusText}`);
      }

      // Check response time
      if (responseTime > this.slowUrlThreshold) {
        result.warnings.push(`Slow response: ${responseTime}ms`);
      }

      // Check content type for HTML pages
      const contentType = response.headers.get('content-type');
      if (contentType && !contentType.includes('text/html') && !contentType.includes('application/xml')) {
        result.warnings.push(`Non-HTML content type: ${contentType}`);
      }

    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          result.errors.push(`Request timeout (${this.requestTimeout}ms)`);
        } else {
          result.errors.push(`Request failed: ${error.message}`);
        }
      } else {
        result.errors.push('Unknown request error');
      }
    }

    return result;
  }

  /**
   * Performs comprehensive sitemap validation
   */
  async validateSitemap(
    xmlResult: SitemapXMLResult,
    entries: SitemapEntry[],
    options?: {
      checkAccessibility?: boolean;
      maxUrlsToCheck?: number;
    }
  ): Promise<SitemapValidationResult> {
    const checkAccessibility = options?.checkAccessibility ?? false;
    const maxUrlsToCheck = options?.maxUrlsToCheck ?? 100;

    // Validate XML structure
    const xmlValidation = this.validateXMLStructure(xmlResult.xml);

    // Validate entries
    const entryValidation = this.validateSitemapEntries(entries);

    // Validate URL accessibility if requested
    let urlValidations: URLValidationResult[] = [];
    if (checkAccessibility && entries.length > 0) {
      const urlsToCheck = entries
        .slice(0, maxUrlsToCheck)
        .map(entry => entry.url);
      
      urlValidations = await this.validateURLAccessibility(urlsToCheck);
    }

    // Calculate performance metrics
    const totalSize = Buffer.byteLength(xmlResult.xml, 'utf8');
    const slowUrls = urlValidations
      .filter(result => result.responseTime && result.responseTime > this.slowUrlThreshold)
      .map(result => result.url);

    const averageResponseTime = urlValidations.length > 0
      ? urlValidations
          .filter(result => result.responseTime)
          .reduce((sum, result) => sum + (result.responseTime || 0), 0) / urlValidations.length
      : undefined;

    // Compile results
    const allErrors = [
      ...xmlValidation.errors,
      ...entryValidation.errors,
      ...urlValidations.flatMap(result => result.errors),
    ];

    const allWarnings = [
      ...entryValidation.warnings,
      ...urlValidations.flatMap(result => result.warnings),
    ];

    // Add size warnings
    if (totalSize > SITEMAP_CONFIG.maxSitemapSize) {
      allWarnings.push(`Sitemap size (${totalSize} bytes) exceeds recommended limit`);
    }

    if (entries.length > SITEMAP_CONFIG.maxUrlsPerSitemap) {
      allWarnings.push(`URL count (${entries.length}) exceeds recommended limit`);
    }

    return {
      isValid: allErrors.length === 0,
      xmlValid: xmlValidation.isValid,
      urlsValid: urlValidations.filter(result => result.isValid).length,
      urlsInvalid: urlValidations.filter(result => !result.isValid).length,
      duplicateUrls: entryValidation.duplicates,
      errors: allErrors,
      warnings: allWarnings,
      performance: {
        totalSize,
        urlCount: entries.length,
        averageResponseTime,
        slowUrls,
      },
      urlValidations,
    };
  }

  /**
   * Validates sitemap size and performance constraints
   */
  validateSitemapConstraints(xmlResult: SitemapXMLResult, entries: SitemapEntry[]): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    const xmlSize = Buffer.byteLength(xmlResult.xml, 'utf8');
    const urlCount = entries.length;

    // Hard limits (errors)
    if (xmlSize > 50 * 1024 * 1024) { // 50MB
      errors.push(`Sitemap size (${xmlSize} bytes) exceeds maximum limit of 50MB`);
    }

    if (urlCount > 50000) {
      errors.push(`URL count (${urlCount}) exceeds maximum limit of 50,000`);
    }

    // Recommended limits (warnings)
    if (xmlSize > SITEMAP_CONFIG.maxSitemapSize) {
      warnings.push(`Sitemap size (${xmlSize} bytes) exceeds recommended limit of ${SITEMAP_CONFIG.maxSitemapSize} bytes`);
    }

    if (urlCount > SITEMAP_CONFIG.maxUrlsPerSitemap) {
      warnings.push(`URL count (${urlCount}) exceeds recommended limit of ${SITEMAP_CONFIG.maxUrlsPerSitemap}`);
    }

    // Performance warnings
    if (xmlResult.generationTime > 10000) { // 10 seconds
      warnings.push(`Sitemap generation time (${xmlResult.generationTime}ms) is very slow`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Detects and reports duplicate URLs
   */
  detectDuplicateUrls(entries: SitemapEntry[]): {
    duplicates: Array<{ url: string; count: number; indices: number[] }>;
    totalDuplicates: number;
  } {
    const urlMap = new Map<string, number[]>();
    
    // Build map of URLs to their indices
    entries.forEach((entry, index) => {
      if (!urlMap.has(entry.url)) {
        urlMap.set(entry.url, []);
      }
      urlMap.get(entry.url)!.push(index);
    });

    // Find duplicates
    const duplicates = Array.from(urlMap.entries())
      .filter(([_, indices]) => indices.length > 1)
      .map(([url, indices]) => ({
        url,
        count: indices.length,
        indices,
      }));

    const totalDuplicates = duplicates.reduce((sum, dup) => sum + dup.count - 1, 0);

    return { duplicates, totalDuplicates };
  }

  /**
   * Validates hreflang annotations
   */
  validateHreflangAnnotations(entries: SitemapEntry[]): {
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    for (const entry of entries) {
      if (entry.alternates && entry.alternates.length > 0) {
        for (const alternate of entry.alternates) {
          // Validate hreflang format
          if (!alternate.hreflang.match(/^[a-z]{2}(-[A-Z]{2})?$/)) {
            errors.push(`Invalid hreflang format: ${alternate.hreflang} for URL: ${entry.url}`);
          }

          // Validate alternate URL
          try {
            new URL(alternate.href);
          } catch {
            errors.push(`Invalid alternate URL: ${alternate.href} for URL: ${entry.url}`);
          }

          // Check for self-referencing alternates
          if (alternate.href === entry.url && alternate.hreflang !== 'x-default') {
            warnings.push(`Self-referencing alternate for URL: ${entry.url}`);
          }
        }

        // Check for x-default
        const hasXDefault = entry.alternates.some(alt => alt.hreflang === 'x-default');
        if (entry.alternates.length > 1 && !hasXDefault) {
          warnings.push(`Missing x-default hreflang for URL: ${entry.url}`);
        }
      }
    }

    return { errors, warnings };
  }

  /**
   * Utility method to chunk arrays for concurrent processing
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * Updates validator configuration
   */
  updateConfig(options: {
    maxConcurrentRequests?: number;
    requestTimeout?: number;
    slowUrlThreshold?: number;
  }): void {
    this.maxConcurrentRequests = options.maxConcurrentRequests ?? this.maxConcurrentRequests;
    this.requestTimeout = options.requestTimeout ?? this.requestTimeout;
    this.slowUrlThreshold = options.slowUrlThreshold ?? this.slowUrlThreshold;
  }

  /**
   * Gets current validator configuration
   */
  getConfig(): {
    maxConcurrentRequests: number;
    requestTimeout: number;
    slowUrlThreshold: number;
  } {
    return {
      maxConcurrentRequests: this.maxConcurrentRequests,
      requestTimeout: this.requestTimeout,
      slowUrlThreshold: this.slowUrlThreshold,
    };
  }
}