/**
 * 音乐生成状态更新服务
 * 定期检查进行中的音乐生成任务并更新状态
 */

import { 
  findPendingMusicGenerations, 
  updateMusicGenerationStatus,
  getGenerationsWithoutCallbacks 
} from "@/models/music-generation";
import { findTrackByGenerationUuid } from "@/models/track";
import { MusicProviderFactory } from "@/services/music-provider";
import { audioFileProcessor } from "./audio-file-processor";
import { TrackCreationService } from "@/services/track-creation-service";
import { TaskContext, ProviderLogger } from "@/lib/provider-logger";


export class MusicStatusUpdater {
  private static instance: MusicStatusUpdater;
  private intervalId: NodeJS.Timeout | null = null;
  private isRunning = false;
  private readonly checkInterval = 10000; // 10秒检查一次

  private constructor() {}

  static getInstance(): MusicStatusUpdater {
    if (!MusicStatusUpdater.instance) {
      MusicStatusUpdater.instance = new MusicStatusUpdater();
    }
    return MusicStatusUpdater.instance;
  }

  /**
   * 启动状态更新服务
   */
  start() {
    if (this.isRunning) {
      console.log("Music status updater is already running");
      return;
    }

    console.log("Starting music status updater...");
    this.isRunning = true;

    // 立即执行一次
    this.checkAndUpdateStatuses();

    // 设置定期检查
    this.intervalId = setInterval(() => {
      this.checkAndUpdateStatuses();
    }, this.checkInterval);
  }

  /**
   * 停止状态更新服务
   */
  stop() {
    if (!this.isRunning) {
      return;
    }

    console.log("Stopping music status updater...");
    this.isRunning = false;

    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  /**
   * 检查并更新所有进行中的音乐生成状态
   */
  private async checkAndUpdateStatuses() {
    try {
      // 优先获取未收到回调的生成任务（5分钟内创建的）
      const generationsWithoutCallbacks = await getGenerationsWithoutCallbacks(5);
      
      // 获取所有进行中的生成任务作为备用
      const allPendingGenerations = await findPendingMusicGenerations();
      
      // 合并并去重，优先处理未收到回调的任务
      const generationsToCheck = this.prioritizeGenerations(
        generationsWithoutCallbacks,
        allPendingGenerations
      );
      
      if (generationsToCheck.length === 0) {
        console.log("No pending music generations to check");
        return;
      }

      console.log(`Checking ${generationsToCheck.length} pending music generations...`);
      console.log(`- Without callbacks: ${generationsWithoutCallbacks.length}`);
      console.log(`- Total pending: ${allPendingGenerations.length}`);

      // 确保提供商已初始化
      await MusicProviderFactory.initialize();

      // 并行处理所有任务
      const updatePromises = generationsToCheck.map(generation => 
        this.updateSingleGeneration(generation)
      );

      await Promise.allSettled(updatePromises);
      
    } catch (error) {
      console.error("Error in music status updater:", error);
    }
  }

  /**
   * 优先排序生成任务
   */
  private prioritizeGenerations(withoutCallbacks: any[], allPending: any[]): any[] {
    // 创建一个Set来跟踪已处理的UUID
    const processedUuids = new Set<string>();
    const result: any[] = [];

    // 首先添加未收到回调的任务（优先级最高）
    for (const generation of withoutCallbacks) {
      if (!processedUuids.has(generation.uuid)) {
        result.push(generation);
        processedUuids.add(generation.uuid);
      }
    }

    // 然后添加其他待处理任务，包括回调失败需要重试的
    for (const generation of allPending) {
      if (!processedUuids.has(generation.uuid)) {
        // 检查是否需要轮询备用处理
        const needsPolling = this.shouldFallbackToPolling(generation);
        
        if (needsPolling) {
          result.push(generation);
          processedUuids.add(generation.uuid);
        }
      }
    }

    return result;
  }

  /**
   * 检查是否需要回退到轮询处理
   */
  private shouldFallbackToPolling(generation: any): boolean {
    // 如果没有处理过回调，需要轮询
    if (!generation.callback_processed) {
      return true;
    }

    // 如果回调处理时间过久（超过10分钟），可能需要重新检查
    if (generation.callback_received_at) {
      const callbackTime = new Date(generation.callback_received_at).getTime();
      const now = Date.now();
      const timeSinceCallback = now - callbackTime;
      
      // 如果回调超过10分钟但状态仍然是处理中，可能回调处理失败了
      if (timeSinceCallback > 10 * 60 * 1000 && 
          (generation.status === "pending" || generation.status === "processing")) {
        console.log(`Generation ${generation.uuid} may have failed callback processing, falling back to polling`);
        return true;
      }
    }

    return false;
  }

  /**
   * 更新单个音乐生成任务的状态
   */
  private async updateSingleGeneration(generation: any) {
    const taskContext: TaskContext = {
      generation_uuid: generation.uuid,
      provider_task_id: generation.provider_task_id,
      user_uuid: generation.user_uuid
    };

    const logger = ProviderLogger.createSafeLogger(taskContext);

    try {
      logger.logOperationStart('status check', {
        callback_received: !!generation.callback_received_at,
        callback_processed: !!generation.callback_processed
      });

      // 如果已经通过回调处理过，跳过轮询（除非是很久之前的任务）
      if (generation.callback_processed) {
        const callbackTime = generation.callback_received_at ? new Date(generation.callback_received_at) : null;
        const now = new Date();
        const timeSinceCallback = callbackTime ? now.getTime() - callbackTime.getTime() : 0;
        
        // 如果回调处理时间少于1分钟，跳过轮询
        if (timeSinceCallback < 60000) {
          logger.logOperationComplete('status check skipped', 'recently processed by callback');
          return;
        }
      }

      // 检查是否有 provider_task_id
      if (!generation.provider_task_id) {
        logger.logWarning('No provider_task_id, skipping status check');
        return;
      }

      // 获取提供商实例
      const provider = MusicProviderFactory.getProvider(generation.provider || "volcano");
      if (!provider) {
        logger.logError(new Error(`Provider not found: ${generation.provider}`), 'provider lookup');
        return;
      }

      // 创建任务上下文
      const taskContext = {
        generation_uuid: generation.uuid,
        provider_task_id: generation.provider_task_id,
        user_uuid: generation.user_uuid
      };

      // 查询提供商状态
      const providerStatus = await provider.checkStatus(generation.provider_task_id, taskContext);

      // 如果状态没有变化，跳过更新
      if (providerStatus.status === generation.status) {
        logger.logOperationComplete('status check', `unchanged: ${providerStatus.status}`);
        return;
      }

      // 更新数据库状态
      const updatedGeneration = await updateMusicGenerationStatus(
        generation.uuid,
        providerStatus.status,
        providerStatus.error
      );

      logger.logOperationComplete('status update', `${generation.status} -> ${providerStatus.status}`);

      // 如果完成了，创建音轨记录
      if (providerStatus.status === "completed" && providerStatus.result) {
        await this.createTrackIfNotExists(generation, providerStatus.result);
      }

    } catch (error) {
      logger.logError(error, 'generation status update');
      
      // 如果查询失败多次，可以考虑标记为失败
      // 这里暂时只记录错误，不改变状态
    }
  }

  /**
   * 创建音轨记录（如果不存在）
   */
  private async createTrackIfNotExists(generation: any, result: any) {
    try {
      // 检查是否已经存在音轨
      const existingTrack = await findTrackByGenerationUuid(generation.uuid);
      if (existingTrack) {
        console.log(`Track already exists for generation ${generation.uuid}`);
        return;
      }

      // Use the optimized data mapping from provider
      const fileUrl = result.file_url; // Already contains TOS URL from optimized mapping
      const filePath = result.file_path;
      
      console.log(`Using TOS URL for track creation: ${fileUrl}`);

      // 使用优化的track创建服务
      const track = await TrackCreationService.createTrack({
        generation,
        file_url: fileUrl,
        file_path: filePath, // 传递TOS路径
        file_size: result.file_size || 0,
        metadata: {
          ...result.metadata || {},
          original_provider_url: result.original_file_url,
        },
        is_public: true, // 默认公开
      });

      console.log(`Created track for generation ${generation.uuid}:`, track?.uuid);

      // 异步处理文件（TOS文件会跳过下载/上传）
      if (track) {
        audioFileProcessor.processAudioFile(
          track.uuid,
          fileUrl,
          result.file_size,
          filePath // 传递TOS路径
        ).then(processResult => {
          if (processResult.success) {
            console.log(`Audio file processed successfully for track ${track.uuid} (TOS: ${processResult.isTOSFile})`);
          } else {
            console.error(`Audio file processing failed for track ${track.uuid}:`, processResult.error);
          }
        }).catch(error => {
          console.error(`Audio file processing error for track ${track.uuid}:`, error);
        });
      }

    } catch (error) {
      console.error(`Failed to create track for generation ${generation.uuid}:`, error);
    }
  }

  /**
   * 获取服务状态
   */
  async getStatus() {
    try {
      const pendingGenerations = await findPendingMusicGenerations();
      const generationsWithoutCallbacks = await getGenerationsWithoutCallbacks(5);
      
      return {
        isRunning: this.isRunning,
        checkInterval: this.checkInterval,
        totalPending: pendingGenerations.length,
        withoutCallbacks: generationsWithoutCallbacks.length,
        withCallbacks: pendingGenerations.filter(g => g.callback_received_at).length,
        callbackProcessed: pendingGenerations.filter(g => g.callback_processed).length,
      };
    } catch (error) {
      console.error("Failed to get status:", error);
      return {
        isRunning: this.isRunning,
        checkInterval: this.checkInterval,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * 手动触发状态检查（用于测试）
   */
  async triggerCheck(): Promise<void> {
    console.log("Manually triggering status check...");
    await this.checkAndUpdateStatuses();
  }
}

// 导出单例实例
export const musicStatusUpdater = MusicStatusUpdater.getInstance();

// 在模块加载时自动启动（仅在服务器端）
if (typeof window === "undefined") {
  // 延迟启动，确保数据库连接已建立
  setTimeout(() => {
    musicStatusUpdater.start();
  }, 5000);
}
