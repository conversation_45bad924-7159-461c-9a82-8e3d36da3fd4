import type { 
  SitemapCache, 
  SitemapCacheStats, 
  SitemapContentType 
} from "@/types/sitemap";
import { 
  SITEMAP_CACHE_CONFIG, 
  SITEMAP_CONFIG 
} from "@/constants/sitemap";
import { generateCacheKey } from "@/lib/sitemap-utils";

/**
 * Abstract base class for sitemap cache implementations
 */
export abstract class BaseSitemapCache {
  protected stats: SitemapCacheStats = {
    hitRate: 0,
    totalRequests: 0,
    cacheHits: 0,
    cacheMisses: 0,
    averageGenerationTime: 0,
  };

  abstract get(key: string): Promise<SitemapCache | null>;
  abstract set(key: string, cache: SitemapCache): Promise<void>;
  abstract delete(key: string): Promise<boolean>;
  abstract clear(): Promise<void>;
  abstract exists(key: string): Promise<boolean>;

  /**
   * Gets cache statistics
   */
  getStats(): SitemapCacheStats {
    return { ...this.stats };
  }

  /**
   * Updates cache statistics
   */
  protected updateStats(hit: boolean, generationTime?: number): void {
    this.stats.totalRequests++;
    
    if (hit) {
      this.stats.cacheHits++;
    } else {
      this.stats.cacheMisses++;
    }
    
    this.stats.hitRate = this.stats.cacheHits / this.stats.totalRequests;
    
    if (generationTime !== undefined) {
      const totalTime = this.stats.averageGenerationTime * (this.stats.totalRequests - 1);
      this.stats.averageGenerationTime = (totalTime + generationTime) / this.stats.totalRequests;
    }
  }

  /**
   * Resets cache statistics
   */
  resetStats(): void {
    this.stats = {
      hitRate: 0,
      totalRequests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      averageGenerationTime: 0,
    };
  }
}

/**
 * In-memory sitemap cache implementation
 * Suitable for development and single-instance deployments
 */
export class MemorySitemapCache extends BaseSitemapCache {
  private cache = new Map<string, SitemapCache>();
  private timers = new Map<string, NodeJS.Timeout>();

  async get(key: string): Promise<SitemapCache | null> {
    const cached = this.cache.get(key);
    
    if (!cached) {
      this.updateStats(false);
      return null;
    }

    // Check if cache has expired
    if (cached.expiresAt && cached.expiresAt < new Date()) {
      await this.delete(key);
      this.updateStats(false);
      return null;
    }

    this.updateStats(true);
    return cached;
  }

  async set(key: string, cache: SitemapCache): Promise<void> {
    this.cache.set(key, cache);

    // Clear existing timer if any
    const existingTimer = this.timers.get(key);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // Set expiration timer
    if (cache.expiresAt) {
      const timeToExpire = cache.expiresAt.getTime() - Date.now();
      if (timeToExpire > 0) {
        const timer = setTimeout(() => {
          this.delete(key);
        }, timeToExpire);
        this.timers.set(key, timer);
      }
    }
  }

  async delete(key: string): Promise<boolean> {
    const existed = this.cache.has(key);
    this.cache.delete(key);

    // Clear timer
    const timer = this.timers.get(key);
    if (timer) {
      clearTimeout(timer);
      this.timers.delete(key);
    }

    return existed;
  }

  async clear(): Promise<void> {
    // Clear all timers
    for (const timer of this.timers.values()) {
      clearTimeout(timer);
    }
    
    this.cache.clear();
    this.timers.clear();
    this.resetStats();
  }

  async exists(key: string): Promise<boolean> {
    return this.cache.has(key);
  }

  /**
   * Gets current cache size
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * Gets all cache keys
   */
  keys(): string[] {
    return Array.from(this.cache.keys());
  }
}

/**
 * Sitemap Cache Manager
 * Provides high-level caching operations for sitemap generation
 */
export class SitemapCacheManager {
  private cache: BaseSitemapCache;

  constructor(cache?: BaseSitemapCache) {
    this.cache = cache || new MemorySitemapCache();
  }

  /**
   * Gets cached sitemap content
   */
  async getCachedSitemap(
    contentType: SitemapContentType,
    locale?: string,
    additionalParams?: Record<string, string>
  ): Promise<string | null> {
    const key = this.generateKey(contentType, locale, additionalParams);
    const cached = await this.cache.get(key);
    
    if (!cached) {
      return null;
    }

    return cached.content;
  }

  /**
   * Caches sitemap content
   */
  async cacheSitemap(
    contentType: SitemapContentType,
    content: string,
    locale?: string,
    additionalParams?: Record<string, string>,
    customTimeout?: number
  ): Promise<void> {
    const key = this.generateKey(contentType, locale, additionalParams);
    const timeout = customTimeout || this.getTimeoutForContentType(contentType);
    const expiresAt = new Date(Date.now() + timeout * 1000);

    const cacheEntry: SitemapCache = {
      key,
      content,
      lastModified: new Date(),
      expiresAt,
      contentType,
      locale,
    };

    await this.cache.set(key, cacheEntry);
  }

  /**
   * Invalidates cache for specific content type and locale
   */
  async invalidateCache(
    contentType?: SitemapContentType,
    locale?: string
  ): Promise<number> {
    if (!contentType) {
      // Clear all cache
      await this.cache.clear();
      return 0; // Can't count cleared items in this case
    }

    let deletedCount = 0;
    const keys = await this.getAllKeys();
    
    for (const key of keys) {
      const shouldDelete = this.shouldDeleteKey(key, contentType, locale);
      if (shouldDelete) {
        const deleted = await this.cache.delete(key);
        if (deleted) deletedCount++;
      }
    }

    return deletedCount;
  }

  /**
   * Warms up cache by pre-generating content
   */
  async warmCache(
    contentTypes: SitemapContentType[],
    locales: string[] = SITEMAP_CONFIG.locales,
    generator?: (contentType: SitemapContentType, locale: string) => Promise<string>
  ): Promise<void> {
    if (!generator) {
      console.warn('Cache warming requires a generator function');
      return;
    }

    const warmupPromises: Promise<void>[] = [];

    for (const contentType of contentTypes) {
      for (const locale of locales) {
        const promise = this.warmSingleEntry(contentType, locale, generator);
        warmupPromises.push(promise);
      }
    }

    await Promise.allSettled(warmupPromises);
  }

  /**
   * Gets cache statistics
   */
  getStats(): SitemapCacheStats {
    return this.cache.getStats();
  }

  /**
   * Checks if cache entry exists and is valid
   */
  async isCached(
    contentType: SitemapContentType,
    locale?: string,
    additionalParams?: Record<string, string>
  ): Promise<boolean> {
    const key = this.generateKey(contentType, locale, additionalParams);
    const cached = await this.cache.get(key);
    return cached !== null;
  }

  /**
   * Gets cache entry metadata without content
   */
  async getCacheMetadata(
    contentType: SitemapContentType,
    locale?: string,
    additionalParams?: Record<string, string>
  ): Promise<Omit<SitemapCache, 'content'> | null> {
    const key = this.generateKey(contentType, locale, additionalParams);
    const cached = await this.cache.get(key);
    
    if (!cached) {
      return null;
    }

    const { content, ...metadata } = cached;
    return metadata;
  }

  /**
   * Updates cache entry expiration
   */
  async extendCacheExpiration(
    contentType: SitemapContentType,
    additionalSeconds: number,
    locale?: string,
    additionalParams?: Record<string, string>
  ): Promise<boolean> {
    const key = this.generateKey(contentType, locale, additionalParams);
    const cached = await this.cache.get(key);
    
    if (!cached) {
      return false;
    }

    cached.expiresAt = new Date(cached.expiresAt.getTime() + additionalSeconds * 1000);
    await this.cache.set(key, cached);
    return true;
  }

  /**
   * Generates cache key for content type and locale
   */
  private generateKey(
    contentType: SitemapContentType,
    locale?: string,
    additionalParams?: Record<string, string>
  ): string {
    return generateCacheKey(contentType, locale, additionalParams);
  }

  /**
   * Gets timeout for specific content type
   */
  private getTimeoutForContentType(contentType: SitemapContentType): number {
    switch (contentType) {
      case 'static':
        return SITEMAP_CACHE_CONFIG.STATIC_PAGES_CACHE_TIMEOUT;
      case 'posts':
        return SITEMAP_CACHE_CONFIG.BLOG_POSTS_CACHE_TIMEOUT;
      case 'loops':
        return SITEMAP_CACHE_CONFIG.MUSIC_LOOPS_CACHE_TIMEOUT;
      case 'index':
        return SITEMAP_CACHE_CONFIG.SITEMAP_INDEX_CACHE_TIMEOUT;
      default:
        return SITEMAP_CONFIG.cacheTimeout;
    }
  }

  /**
   * Gets all cache keys (implementation dependent)
   */
  private async getAllKeys(): Promise<string[]> {
    if (this.cache instanceof MemorySitemapCache) {
      return this.cache.keys();
    }
    
    // For other implementations, this would need to be implemented
    return [];
  }

  /**
   * Determines if a key should be deleted based on content type and locale
   */
  private shouldDeleteKey(
    key: string,
    contentType: SitemapContentType,
    locale?: string
  ): boolean {
    const keyParts = key.split(':');
    
    if (keyParts.length < 2) {
      return false;
    }

    const keyContentType = keyParts[1];
    const keyLocale = keyParts.length > 2 ? keyParts[2] : undefined;

    if (keyContentType !== contentType) {
      return false;
    }

    if (locale && keyLocale !== locale) {
      return false;
    }

    return true;
  }

  /**
   * Warms a single cache entry
   */
  private async warmSingleEntry(
    contentType: SitemapContentType,
    locale: string,
    generator: (contentType: SitemapContentType, locale: string) => Promise<string>
  ): Promise<void> {
    try {
      const content = await generator(contentType, locale);
      await this.cacheSitemap(contentType, content, locale);
    } catch (error) {
      console.error(`Failed to warm cache for ${contentType}:${locale}:`, error);
    }
  }

  /**
   * Gets the underlying cache implementation
   */
  getCache(): BaseSitemapCache {
    return this.cache;
  }

  /**
   * Replaces the cache implementation
   */
  setCache(cache: BaseSitemapCache): void {
    this.cache = cache;
  }
}