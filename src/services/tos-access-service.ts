/**
 * TOS访问服务
 * 处理TOS文件访问、验证和错误处理
 */

export interface TOSAccessResult {
  success: boolean;
  url?: string;
  error?: string;
  fallbackUrl?: string;
  accessTime?: number;
}

export interface TOSValidationResult {
  isAccessible: boolean;
  statusCode?: number;
  responseTime?: number;
  error?: string;
}

export class TOSAccessService {
  private static instance: TOSAccessService;
  private accessCache = new Map<string, { result: TOSValidationResult; timestamp: number }>();
  private readonly cacheTimeout = 5 * 60 * 1000; // 5分钟缓存

  private constructor() {}

  static getInstance(): TOSAccessService {
    if (!TOSAccessService.instance) {
      TOSAccessService.instance = new TOSAccessService();
    }
    return TOSAccessService.instance;
  }

  /**
   * 构造TOS URL
   */
  constructTOSUrl(tosPath: string): TOSAccessResult {
    const startTime = Date.now();
    
    try {
      const domain = process.env.VOLCANO_MUSIC_DOMAIN;
      
      if (!domain) {
        return {
          success: false,
          error: "VOLCANO_MUSIC_DOMAIN environment variable is not configured",
          accessTime: Date.now() - startTime,
        };
      }

      if (!tosPath) {
        return {
          success: false,
          error: "TOS path is required for URL construction",
          accessTime: Date.now() - startTime,
        };
      }

      // 验证域名格式
      try {
        new URL(domain);
      } catch (error) {
        return {
          success: false,
          error: `Invalid VOLCANO_MUSIC_DOMAIN format: ${domain}`,
          accessTime: Date.now() - startTime,
        };
      }

      // 构造URL
      const cleanDomain = domain.replace(/\/$/, '');
      const cleanPath = tosPath.replace(/^\//, '');
      const tosUrl = `${cleanDomain}/${cleanPath}`;

      // 验证构造的URL格式
      try {
        new URL(tosUrl);
      } catch (error) {
        return {
          success: false,
          error: `Invalid constructed TOS URL: ${tosUrl}`,
          accessTime: Date.now() - startTime,
        };
      }

      return {
        success: true,
        url: tosUrl,
        accessTime: Date.now() - startTime,
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error during URL construction',
        accessTime: Date.now() - startTime,
      };
    }
  }

  /**
   * 验证TOS URL可访问性
   */
  async validateTOSAccess(tosUrl: string, useCache: boolean = true): Promise<TOSValidationResult> {
    const startTime = Date.now();

    // 检查缓存
    if (useCache) {
      const cached = this.accessCache.get(tosUrl);
      if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
        console.log(`Using cached TOS validation result for: ${tosUrl}`);
        return cached.result;
      }
    }

    try {
      console.log(`Validating TOS access: ${tosUrl}`);
      
      // 发送HEAD请求检查文件可访问性
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

      const response = await fetch(tosUrl, {
        method: 'HEAD',
        signal: controller.signal,
        headers: {
          'User-Agent': 'LoopCraft-TOS-Validator/1.0',
        },
      });

      clearTimeout(timeoutId);
      const responseTime = Date.now() - startTime;

      const result: TOSValidationResult = {
        isAccessible: response.ok,
        statusCode: response.status,
        responseTime,
      };

      if (!response.ok) {
        result.error = `HTTP ${response.status}: ${response.statusText}`;
        console.warn(`TOS access validation failed: ${tosUrl} - ${result.error}`);
      } else {
        console.log(`TOS access validation successful: ${tosUrl} (${responseTime}ms)`);
      }

      // 缓存结果
      if (useCache) {
        this.accessCache.set(tosUrl, {
          result,
          timestamp: Date.now(),
        });
      }

      return result;

    } catch (error) {
      const responseTime = Date.now() - startTime;
      const result: TOSValidationResult = {
        isAccessible: false,
        responseTime,
        error: error instanceof Error ? error.message : 'Unknown validation error',
      };

      console.error(`TOS access validation error: ${tosUrl}`, error);

      // 缓存失败结果（较短时间）
      if (useCache) {
        this.accessCache.set(tosUrl, {
          result,
          timestamp: Date.now(),
        });
      }

      return result;
    }
  }

  /**
   * 获取可访问的URL（TOS优先，回退到备用URL）
   */
  async getAccessibleUrl(
    tosPath: string,
    fallbackUrl?: string,
    validateAccess: boolean = true
  ): Promise<TOSAccessResult> {
    const startTime = Date.now();

    // 1. 尝试构造TOS URL
    const tosResult = this.constructTOSUrl(tosPath);
    if (!tosResult.success) {
      console.warn(`TOS URL construction failed: ${tosResult.error}`);
      
      if (fallbackUrl) {
        console.log(`Using fallback URL: ${fallbackUrl}`);
        return {
          success: true,
          url: fallbackUrl,
          fallbackUrl,
          error: `TOS construction failed: ${tosResult.error}`,
          accessTime: Date.now() - startTime,
        };
      }

      return {
        success: false,
        error: tosResult.error,
        accessTime: Date.now() - startTime,
      };
    }

    const tosUrl = tosResult.url!;

    // 2. 如果不需要验证访问性，直接返回TOS URL
    if (!validateAccess) {
      return {
        success: true,
        url: tosUrl,
        accessTime: Date.now() - startTime,
      };
    }

    // 3. 验证TOS URL可访问性
    const validation = await this.validateTOSAccess(tosUrl);
    
    if (validation.isAccessible) {
      return {
        success: true,
        url: tosUrl,
        accessTime: Date.now() - startTime,
      };
    }

    // 4. TOS不可访问，尝试使用备用URL
    console.warn(`TOS URL not accessible: ${tosUrl} - ${validation.error}`);
    
    if (fallbackUrl) {
      console.log(`Falling back to: ${fallbackUrl}`);
      
      // 可选：也验证备用URL的可访问性
      if (validateAccess) {
        try {
          const fallbackValidation = await this.validateTOSAccess(fallbackUrl, false);
          if (!fallbackValidation.isAccessible) {
            console.warn(`Fallback URL also not accessible: ${fallbackUrl}`);
          }
        } catch (error) {
          console.warn(`Failed to validate fallback URL: ${fallbackUrl}`, error);
        }
      }

      return {
        success: true,
        url: fallbackUrl,
        fallbackUrl,
        error: `TOS access failed: ${validation.error}`,
        accessTime: Date.now() - startTime,
      };
    }

    // 5. 没有备用URL，返回失败
    return {
      success: false,
      error: `TOS access failed and no fallback URL provided: ${validation.error}`,
      accessTime: Date.now() - startTime,
    };
  }

  /**
   * 批量验证TOS URL
   */
  async validateMultipleTOSUrls(urls: string[]): Promise<Map<string, TOSValidationResult>> {
    console.log(`Validating ${urls.length} TOS URLs...`);
    
    const results = new Map<string, TOSValidationResult>();
    const promises = urls.map(async (url) => {
      const result = await this.validateTOSAccess(url);
      results.set(url, result);
      return { url, result };
    });

    await Promise.allSettled(promises);
    
    const accessible = Array.from(results.values()).filter(r => r.isAccessible).length;
    console.log(`TOS validation completed: ${accessible}/${urls.length} URLs accessible`);
    
    return results;
  }

  /**
   * 清理访问缓存
   */
  clearCache(): void {
    this.accessCache.clear();
    console.log("TOS access cache cleared");
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): {
    size: number;
    entries: Array<{
      url: string;
      isAccessible: boolean;
      age: number;
    }>;
  } {
    const now = Date.now();
    const entries = Array.from(this.accessCache.entries()).map(([url, cached]) => ({
      url,
      isAccessible: cached.result.isAccessible,
      age: now - cached.timestamp,
    }));

    return {
      size: this.accessCache.size,
      entries,
    };
  }

  /**
   * 清理过期缓存
   */
  cleanupExpiredCache(): void {
    const now = Date.now();
    let cleaned = 0;

    for (const [url, cached] of this.accessCache.entries()) {
      if (now - cached.timestamp > this.cacheTimeout) {
        this.accessCache.delete(url);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      console.log(`Cleaned up ${cleaned} expired TOS cache entries`);
    }
  }
}

// 导出单例实例
export const tosAccessService = TOSAccessService.getInstance();

// 定期清理过期缓存
if (typeof window === "undefined") {
  setInterval(() => {
    tosAccessService.cleanupExpiredCache();
  }, 10 * 60 * 1000); // 每10分钟清理一次
}