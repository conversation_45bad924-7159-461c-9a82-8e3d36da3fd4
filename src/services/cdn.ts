/**
 * CDN 服务 - 用于生成安全的音频下载链接和流媒体链接
 */

import crypto from 'crypto';

export interface CDNConfig {
  baseUrl: string;
  secretKey: string;
  defaultExpiryHours: number;
}

export interface SecureDownloadOptions {
  filePath: string;
  expiryHours?: number;
  customFilename?: string;
  forceDownload?: boolean;
}

export interface SecureDownloadResult {
  downloadUrl: string;
  expiresAt: Date;
  token: string;
  timestamp: number;
}

/**
 * CDN 服务类
 */
export class CDNService {
  private config: CDNConfig;

  constructor(config: CDNConfig) {
    this.config = config;
  }

  /**
   * 生成下载安全令牌
   * @param filePath 文件路径
   * @param timestamp 过期时间戳
   * @param customSecret 自定义密钥（可选）
   * @returns SHA-256 令牌
   */
  generateDownloadToken(filePath: string, timestamp: number, customSecret?: string): string {
    const secretKey = customSecret || this.config.secretKey;
    const message = `${filePath}:${timestamp}:${secretKey}`;
    return crypto.createHash('sha256').update(message).digest('hex');
  }

  /**
   * 验证下载令牌
   * @param filePath 文件路径
   * @param timestamp 时间戳
   * @param token 令牌
   * @returns 是否有效
   */
  validateDownloadToken(filePath: string, timestamp: number, token: string): boolean {
    // 检查是否过期
    const currentTimestamp = Math.floor(Date.now() / 1000);
    if (timestamp <= currentTimestamp) {
      return false;
    }

    // 验证令牌
    const expectedToken = this.generateDownloadToken(filePath, timestamp);
    return token === expectedToken;
  }

  /**
   * 从 S3 URL 提取文件路径
   * @param s3Url S3 URL
   * @returns 文件路径
   */
  extractFilePathFromS3Url(s3Url: string): string {
    try {
      const url = new URL(s3Url);
      let pathname = url.pathname;
      
      // 如果路径不以 /music 开头，添加 /music 前缀
      if (!pathname.startsWith('/music')) {
        pathname = '/music' + pathname;
      }
      
      return pathname;
    } catch (error) {
      throw new Error('Invalid S3 URL format');
    }
  }

  /**
   * 从 CDN URL 提取文件路径
   * @param cdnUrl CDN URL
   * @returns 文件路径
   */
  extractFilePathFromCdnUrl(cdnUrl: string): string {
    try {
      const url = new URL(cdnUrl);
      return url.pathname;
    } catch (error) {
      throw new Error('Invalid CDN URL format');
    }
  }

  /**
   * 生成安全下载链接
   * @param options 下载选项
   * @returns 安全下载结果
   */
  generateSecureDownloadUrl(options: SecureDownloadOptions): SecureDownloadResult {
    const { filePath, expiryHours = this.config.defaultExpiryHours, customFilename, forceDownload = true } = options;
    
    // 计算过期时间戳
    const timestamp = Math.floor(Date.now() / 1000) + (expiryHours * 3600);
    
    // 生成令牌
    const token = this.generateDownloadToken(filePath, timestamp);
    
    // 构建 URL
    const url = new URL(`${this.config.baseUrl}${filePath}`);
    
    if (forceDownload) {
      url.searchParams.set('download', 'true');
    }
    
    if (customFilename) {
      url.searchParams.set('filename', customFilename);
    }
    
    url.searchParams.set('t', timestamp.toString());
    url.searchParams.set('token', token);
    
    return {
      downloadUrl: url.toString(),
      expiresAt: new Date(timestamp * 1000),
      token,
      timestamp
    };
  }

  /**
   * 生成流媒体播放链接
   * @param filePath 文件路径
   * @returns 流媒体链接
   */
  generateStreamingUrl(filePath: string): string {
    return `${this.config.baseUrl}${filePath}`;
  }

  /**
   * 批量生成安全下载链接
   * @param filePaths 文件路径数组
   * @param options 下载选项
   * @returns 安全下载结果数组
   */
  generateBatchDownloadUrls(filePaths: string[], options?: Partial<SecureDownloadOptions>): SecureDownloadResult[] {
    return filePaths.map(filePath => 
      this.generateSecureDownloadUrl({
        filePath,
        ...options
      })
    );
  }
}

/**
 * 获取默认 CDN 配置
 */
function getDefaultCDNConfig(): CDNConfig {
  const baseUrl = process.env.VOLCANO_MUSIC_DOMAIN || process.env.CDN_BASE_URL;
  const secretKey = process.env.CDN_SECRET_KEY;
  const defaultExpiryHours = parseInt(process.env.CDN_DEFAULT_EXPIRY_HOURS || '1');

  if (!baseUrl) {
    throw new Error('CDN base URL not configured. Please set VOLCANO_MUSIC_DOMAIN or CDN_BASE_URL environment variable.');
  }

  if (!secretKey) {
    throw new Error('CDN secret key not configured. Please set CDN_SECRET_KEY environment variable.');
  }

  return {
    baseUrl,
    secretKey,
    defaultExpiryHours
  };
}

/**
 * 默认 CDN 服务实例
 */
let defaultCDNService: CDNService | null = null;

/**
 * 获取默认 CDN 服务实例
 */
function getDefaultCDNService(): CDNService {
  if (!defaultCDNService) {
    defaultCDNService = new CDNService(getDefaultCDNConfig());
  }
  return defaultCDNService;
}

/**
 * 工具函数：生成安全下载链接
 * @param urlOrFilePath URL 或文件路径
 * @param options 下载选项
 * @returns 安全下载结果
 */
export function generateSecureDownloadUrl(
  urlOrFilePath: string, 
  options?: Partial<SecureDownloadOptions>
): SecureDownloadResult {
  const cdnService = getDefaultCDNService();
  
  let filePath: string;
  
  // 判断是否为完整 URL
  if (urlOrFilePath.startsWith('http')) {
    // 如果是 S3 URL，提取文件路径
    if (urlOrFilePath.includes('tos-') || urlOrFilePath.includes('s3')) {
      filePath = cdnService.extractFilePathFromS3Url(urlOrFilePath);
    } else {
      // 如果是 CDN URL，提取文件路径
      filePath = cdnService.extractFilePathFromCdnUrl(urlOrFilePath);
    }
  } else {
    // 直接使用文件路径
    filePath = urlOrFilePath.startsWith('/') ? urlOrFilePath : `/${urlOrFilePath}`;
  }
  
  return cdnService.generateSecureDownloadUrl({
    filePath,
    ...options
  });
}

/**
 * 工具函数：生成流媒体链接
 * @param urlOrFilePath URL 或文件路径
 * @returns 流媒体链接
 */
export function generateStreamingUrl(urlOrFilePath: string): string {
  const cdnService = getDefaultCDNService();
  
  let filePath: string;
  
  // 判断是否为完整 URL
  if (urlOrFilePath.startsWith('http')) {
    // 如果是 S3 URL，提取文件路径
    if (urlOrFilePath.includes('tos-') || urlOrFilePath.includes('s3')) {
      filePath = cdnService.extractFilePathFromS3Url(urlOrFilePath);
    } else {
      // 如果是 CDN URL，提取文件路径
      filePath = cdnService.extractFilePathFromCdnUrl(urlOrFilePath);
    }
  } else {
    // 直接使用文件路径
    filePath = urlOrFilePath.startsWith('/') ? urlOrFilePath : `/${urlOrFilePath}`;
  }
  
  return cdnService.generateStreamingUrl(filePath);
}

/**
 * 工具函数：生成自定义文件名
 * @param url 原始 URL
 * @param title 音频标题（可选）
 * @param format 文件格式（可选）
 * @returns 清理后的文件名
 */
export function generateCustomFilename(url: string, title?: string, format?: string): string {
  if (title) {
    // 清理标题中的特殊字符
    const cleanTitle = title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // 移除特殊字符
      .replace(/\s+/g, '-') // 空格替换为连字符
      .replace(/-+/g, '-') // 多个连字符合并为一个
      .trim();
    
    // 确定文件扩展名
    const extension = format || 'wav';
    
    return `${cleanTitle}.${extension}`;
  }
  
  try {
    // 从 URL 提取文件名
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const filename = pathname.split('/').pop();
    
    if (filename && filename.includes('.')) {
      return filename;
    }
  } catch (error) {
    // URL 解析失败，使用默认文件名
  }
  
  // 默认文件名
  return 'loopcraft-audio.wav';
}