/**
 * Volcengine 音乐生成服务提供商
 */

import { BaseMusicProvider } from "./base-music-provider";
import { MusicProviderConfig, MusicGenerationOptions } from "@/types/music";
import { sign, hash, getDateTimeNow } from "@/lib/volcengine-signature";
import { FileSizeEstimator } from "@/lib/file-size-estimator";
import { TaskContext, ProviderLogger } from "@/lib/provider-logger";

export interface VolcanoMusicRequest {
  Text: string;
  Duration?: number;
  Genre?: string[];
  Mood?: string[];
  Instrument?: string[];
  Theme?: string[];
  TosBucket: string;
  CallbackURL?: string;
}

export interface VolcanoMusicResponse {
  Code: number;
  Message: string;
  Result: {
    TaskID: string;
  };
  ResponseMetadata: {
    RequestId: string;
    Action: string;
    Version: string;
    Service: string;
    Region: string;
    Error: any;
  };
}

export interface VolcanoQueryResponse {
  Code: number;
  Message: string;
  Result: {
    TaskID: string;
    Status: number; // 0->等待中, 1->处理中, 2->成功, 3->失败
    Progress: number;
    FailureReason?: {
      Code: number;
      Msg: string;
    };
    SongDetail?: {
      AudioUrl: string;
      Prompt: string;
      Duration: number;
      CallbackURL: string;
      Theme: string;
      Instrument: string;
      Mood: string;
      Genre: string;
      TosPath: string;
    };
  };
}

export interface VolcanoSongDetail {
  AudioUrl: string;      // Direct download URL from Volcano
  TosPath: string;       // Path in TOS bucket
  Duration: number;      // Track duration in seconds
  Prompt: string;        // Generation prompt
  Genre: string;         // Music genre
  Mood: string;          // Music mood
  Theme: string;         // Music theme
  Instrument: string;    // Instruments used
  CallbackURL: string;   // Callback URL used
}

export interface TrackDataMapping {
  file_url: string;           // TOS URL (VOLCANO_MUSIC_DOMAIN + TosPath)
  original_file_url: string;  // AudioUrl from Volcano response
  file_size: number;          // Estimated based on duration
  file_path: string;          // TosPath for reference
  metadata: {
    duration: number;
    prompt: string;
    genre: string;
    mood: string;
    theme: string;
    instrument: string;
  };
}



export class VolcanoProvider extends BaseMusicProvider {
  private accessKeyId: string;
  private secretAccessKey: string;
  private region: string;
  private serviceName: string;
  private baseUrl: string;
  private version: string;

  constructor(config: MusicProviderConfig & {
    access_key_id: string;
    secret_access_key: string;
    region?: string;
    service_name?: string;
    version?: string;
  }) {
    super("volcano", config);

    this.accessKeyId = config.access_key_id;
    this.secretAccessKey = config.secret_access_key;
    this.region = config.region || "cn-beijing";
    this.serviceName = config.service_name || "imagination";
    this.baseUrl = config.base_url || "https://open.volcengineapi.com";
    this.version = config.version || "2024-08-12";

    if (!this.accessKeyId || !this.secretAccessKey) {
      throw new Error("Volcano API credentials are required");
    }

    // 验证TOS配置
    this.validateTOSConfig();
  }

  protected getDisplayName(): string {
    return "Volcengine Music";
  }

  protected getSupportedDurations(): number[] {
    return [15, 30, 60];
  }

  protected getSupportedStyles(): string[] {
    return [
      "corporate", "dance/edm", "orchestral", "chill out", "rock", "hip hop",
      "folk", "funk", "ambient", "holiday", "jazz", "kids", "world", "travel",
      "commercial", "advertising", "driving", "cinematic", "upbeat", "epic",
      "inspiring", "business", "video game", "dark", "pop", "trailer",
      "modern", "electronic", "documentary", "soundtrack", "fashion",
      "acoustic", "movie", "tv", "high tech", "industrial", "dance",
      "video", "vlog", "marketing", "game", "radio", "promotional",
      "sports", "party", "summer", "beauty"
    ];
  }

  protected getMaxBpm(): number {
    return 200;
  }

  protected getMinBpm(): number {
    return 60;
  }

  protected supportsStems(): boolean {
    return false;
  }

  protected supportsVariations(): boolean {
    return false;
  }

  /**
   * 发送API请求
   */
  private async makeRequest(action: string, body: any = {}, method: string = "POST"): Promise<any> {
    const query = {
      Action: action,
      Version: this.version,
    };

    // 正规化 query object - 参考工作代码
    for (const [key, val] of Object.entries(query)) {
      if (val === undefined || val === null) {
        (query as any)[key] = '';
      }
    }

    const requestBody = method === "POST" ? JSON.stringify(body) : "";
    const headers = {
      "X-Date": getDateTimeNow(),
      "Content-Type": "application/json",
      "Host": "open.volcengineapi.com",
    };

    const signParams = {
      headers,
      method,
      query,
      accessKeyId: this.accessKeyId,
      secretAccessKey: this.secretAccessKey,
      serviceName: this.serviceName,
      region: this.region,
      body: requestBody,
      bodySha: hash(requestBody),
    };

    const authorization = sign(signParams);

    // 使用 qs.stringify 与工作代码保持一致
    const qs = await import('querystring');
    const queryString = qs.stringify(query);
    const url = `${this.baseUrl}/?${queryString}`;

    console.log("Volcano API request:", {
      url,
      method,
      headers: { ...headers, Authorization: authorization.substring(0, 50) + "..." },
      body: requestBody
    });

    try {
      const response = await fetch(url, {
        method,
        headers: {
          ...headers,
          Authorization: authorization,
        },
        body: method === "POST" ? requestBody : undefined,
      });

      const responseText = await response.text();
      console.log("Volcano API response:", {
        status: response.status,
        statusText: response.statusText,
        body: responseText
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${responseText}`);
      }

      const result = JSON.parse(responseText);

      if (result.Code !== 0) {
        throw new Error(`Volcano API Error ${result.Code}: ${result.Message}`);
      }

      return result;
    } catch (error) {
      console.error('Volcano API error:', error);
      throw error;
    }
  }

  /**
   * 构造TOS URL
   */
  private constructTOSUrl(tosPath: string): string {
    const { tosAccessService } = require("./tos-access-service");
    const result = tosAccessService.constructTOSUrl(tosPath);
    
    if (!result.success) {
      throw new Error(result.error);
    }
    
    return result.url!;
  }

  /**
   * 验证Volcano SongDetail响应
   */
  private validateSongDetail(songDetail: any): VolcanoSongDetail {
    if (!songDetail) {
      throw new Error('Missing SongDetail in Volcano response');
    }

    if (!songDetail.AudioUrl) {
      throw new Error('Missing AudioUrl in Volcano SongDetail');
    }

    if (!songDetail.TosPath) {
      throw new Error('Missing TosPath in Volcano SongDetail');
    }

    if (!songDetail.Duration || songDetail.Duration <= 0) {
      throw new Error('Invalid Duration in Volcano SongDetail');
    }

    if (!songDetail.Prompt) {
      throw new Error('Missing Prompt in Volcano SongDetail');
    }

    // Validate URL format
    try {
      new URL(songDetail.AudioUrl);
    } catch (error) {
      throw new Error(`Invalid AudioUrl format in Volcano SongDetail: ${songDetail.AudioUrl}`);
    }

    // Validate TosPath format (should not be empty and should be a valid path)
    if (typeof songDetail.TosPath !== 'string' || songDetail.TosPath.trim().length === 0) {
      throw new Error(`Invalid TosPath format in Volcano SongDetail: ${songDetail.TosPath}`);
    }

    return songDetail as VolcanoSongDetail;
  }

  /**
   * 映射Volcano响应数据到track数据结构
   */
  private mapResponseToTrackData(songDetail: VolcanoSongDetail): TrackDataMapping {
    // Validate the response first
    const validatedSongDetail = this.validateSongDetail(songDetail);

    return {
      file_url: this.constructTOSUrl(validatedSongDetail.TosPath),
      original_file_url: validatedSongDetail.AudioUrl,
      file_size: FileSizeEstimator.estimateFileSize(validatedSongDetail.Duration),
      file_path: validatedSongDetail.TosPath,
      metadata: {
        duration: validatedSongDetail.Duration,
        prompt: validatedSongDetail.Prompt,
        genre: validatedSongDetail.Genre || '',
        mood: validatedSongDetail.Mood || '',
        theme: validatedSongDetail.Theme || '',
        instrument: validatedSongDetail.Instrument || ''
      }
    };
  }

  /**
   * 验证TOS配置
   */
  private validateTOSConfig(): void {
    const domain = process.env.VOLCANO_MUSIC_DOMAIN;
    const bucket = process.env.VOLCANO_MUSIC_BUCKET;

    if (!domain) {
      throw new Error("VOLCANO_MUSIC_DOMAIN environment variable is required");
    }

    if (!bucket) {
      throw new Error("VOLCANO_MUSIC_BUCKET environment variable is required");
    }

    console.log(`TOS Configuration validated - Domain: ${domain}, Bucket: ${bucket}`);
  }

  /**
   * 构造回调URL
   */
  private constructCallbackUrl(): string | undefined {
    const baseUrl = process.env.NEXT_PUBLIC_WEB_URL;
    if (!baseUrl) {
      console.warn("NEXT_PUBLIC_WEB_URL not configured, callback URL will not be set");
      return undefined;
    }

    // 确保baseUrl不以/结尾
    const cleanBaseUrl = baseUrl.replace(/\/$/, '');
    const callbackPath = '/api/music/callback/volcano';

    return `${cleanBaseUrl}${callbackPath}`;
  }

  /**
   * 映射参数到Volcano API格式
   */
  private mapOptionsToVolcano(options?: MusicGenerationOptions): Partial<VolcanoMusicRequest> {
    const volcanoParams: Partial<VolcanoMusicRequest> = {};

    // Handle legacy style parameter
    if (options?.style) {
      volcanoParams.Genre = Array.isArray(options.style) ? options.style : [options.style];
    }

    // Handle legacy mood parameter
    if (options?.mood) {
      volcanoParams.Mood = Array.isArray(options.mood) ? options.mood : [options.mood];
    }

    // Handle new array parameters
    if (options?.genre && Array.isArray(options.genre)) {
      volcanoParams.Genre = options.genre;
    }

    if (options?.instrument && Array.isArray(options.instrument)) {
      volcanoParams.Instrument = options.instrument;
    }

    if (options?.theme && Array.isArray(options.theme)) {
      volcanoParams.Theme = options.theme;
    }

    // Note: BPM is not supported by Volcano API, so we don't map it
    // BPM will be stored in database for reference but not sent to API

    return volcanoParams;
  }

  async generateMusic(
    prompt: string,
    duration: number,
    options?: MusicGenerationOptions
  ): Promise<{ task_id: string; estimated_time: number }> {
    try {
      const tosBucket = process.env.VOLCANO_MUSIC_BUCKET;
      if (!tosBucket) {
        throw new Error("VOLCANO_MUSIC_BUCKET environment variable is required");
      }

      // 构造回调URL
      const callbackUrl = this.constructCallbackUrl();

      const volcanoParams: VolcanoMusicRequest = {
        Text: prompt,
        Duration: Math.min(Math.max(duration, 1), 60), // 限制在1-60秒范围内
        TosBucket: tosBucket,
        CallbackURL: callbackUrl, // 添加回调URL
        ...this.mapOptionsToVolcano(options),
      };

      if (callbackUrl) {
        console.log(`Volcano generation with callback URL: ${callbackUrl}`);
      } else {
        console.log("Volcano generation without callback URL (NEXT_PUBLIC_WEB_URL not configured)");
      }

      console.log("Volcano generation request:", volcanoParams);

      const response: VolcanoMusicResponse = await this.makeRequest("GenBGMForTime", volcanoParams);

      return {
        task_id: response.Result.TaskID,
        estimated_time: duration * 2, // 估计生成时间为音频时长的2倍
      };
    } catch (error) {
      console.error("Volcano generation failed:", error);
      throw new Error(`Failed to start Volcano generation: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async checkStatus(task_id: string, context?: TaskContext): Promise<{
    status: "pending" | "processing" | "completed" | "failed";
    progress?: number;
    result?: {
      file_url: string;
      file_size: number;
      file_path?: string; // TOS path for direct access
      metadata?: any
    };
    error?: string;
  }> {
    try {
      const response: VolcanoQueryResponse = await this.makeRequest("QuerySong", {
        TaskID: task_id,
      });

      const result = response.Result;
      const status = this.mapVolcanoStatus(result.Status);

      const statusResult: any = {
        status,
        progress: result.Progress || 0,
      };

      // Log status with proper context
      if (context) {
        ProviderLogger.logStatus(context, { status, progress: result.Progress }, 'Provider status check');
      } else {
        console.log(`Provider status for task ${task_id}:`, { status, progress: result.Progress });
      }

      if (status === "completed" && result.SongDetail) {
        try {
          // Use consistent data mapping with validation
          const trackData = this.mapResponseToTrackData(result.SongDetail);
        
          statusResult.result = {
            file_url: trackData.file_url,           // TOS URL as primary file URL
            file_size: trackData.file_size,         // Estimated file size
            file_path: trackData.file_path,         // TOS path for reference
            original_file_url: trackData.original_file_url, // Volcano AudioUrl for premium users
            metadata: trackData.metadata
          };

          if (context) {
            ProviderLogger.logStatus(context, statusResult.result, 'Generation completed with track data');
          }
        } catch (validationError) {
          const errorMessage = `Response validation failed: ${validationError instanceof Error ? validationError.message : String(validationError)}`;
          statusResult.status = "failed";
          statusResult.error = errorMessage;
          
          if (context) {
            ProviderLogger.logError(context, validationError, 'Response validation');
          }
        }
      } else if (status === "failed" && result.FailureReason) {
        statusResult.error = `${result.FailureReason.Code}: ${result.FailureReason.Msg}`;
        
        if (context) {
          ProviderLogger.logError(context, statusResult.error, 'Generation failed');
        }
      }

      return statusResult;
    } catch (error) {
      if (context) {
        ProviderLogger.logError(context, error, 'Status check');
      } else {
        console.error(`Volcano status check failed for task ${task_id}:`, error);
      }
      throw new Error(`Failed to check Volcano status: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async cancelGeneration(task_id: string): Promise<boolean> {
    // Volcengine API 目前不支持取消任务，返回 false
    console.log(`Volcano provider does not support cancellation for task: ${task_id}`);
    return false;
  }

  /**
   * 映射Volcano状态到标准状态
   */
  private mapVolcanoStatus(volcanoStatus: number): "pending" | "processing" | "completed" | "failed" {
    switch (volcanoStatus) {
      case 0:
        return "pending";
      case 1:
        return "processing";
      case 2:
        return "completed";
      case 3:
        return "failed";
      default:
        return "pending";
    }
  }

  /**
   * 获取支持的参数选项
   */
  static getSupportedOptions() {
    return {
      genres: [
        "corporate", "dance/edm", "orchestral", "chill out", "rock", "hip hop",
        "folk", "funk", "ambient", "holiday", "jazz", "kids", "world", "travel",
        "commercial", "advertising", "driving", "cinematic", "upbeat", "epic",
        "inspiring", "business", "video game", "dark", "pop", "trailer",
        "modern", "electronic", "documentary", "soundtrack", "fashion",
        "acoustic", "movie", "tv", "high tech", "industrial", "dance",
        "video", "vlog", "marketing", "game", "radio", "promotional",
        "sports", "party", "summer", "beauty"
      ],
      moods: [
        "positive", "uplifting", "energetic", "happy", "bright", "optimistic",
        "hopeful", "cool", "dreamy", "fun", "light", "powerful", "calm",
        "confident", "joyful", "dramatic", "peaceful", "playful", "soft",
        "groovy", "reflective", "easy", "relaxed", "lively", "smooth",
        "romantic", "intense", "elegant", "mellow", "emotional", "sentimental",
        "cheerful happy", "contemplative", "soothing", "proud", "passionate",
        "sweet", "mystical", "tranquil", "cheerful", "casual", "beautiful",
        "ethereal", "melancholy", "sad", "aggressive", "haunting",
        "adventure", "serene", "sincere", "funky", "funny"
      ],
      instruments: [
        "piano", "drums", "guitar", "percussion", "synth", "electric guitar",
        "acoustic guitar", "bass guitar", "brass", "violin", "cello", "flute",
        "organ", "trumpet", "ukulele", "saxophone", "double bass", "harp",
        "glockenspiel", "synthesizer", "keyboard", "marimba", "bass", "banjo", "strings"
      ],
      themes: [
        "inspirational", "motivational", "achievement", "discovery", "every day",
        "love", "technology", "lifestyle", "journey", "meditation", "drama",
        "children", "hope", "fantasy", "holiday", "health", "family",
        "real estate", "media", "kids", "science", "education", "progress",
        "world", "vacation", "training", "christmas", "sales"
      ]
    };
  }
}
