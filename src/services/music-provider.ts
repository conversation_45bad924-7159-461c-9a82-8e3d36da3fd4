import { <PERSON><PERSON><PERSON>ider, MusicProviderConfig, MusicGenerationOptions } from "@/types/music";
import { BaseMusicProvider } from "./base-music-provider";



// Mubert provider implementation
export class <PERSON>bertProvider extends BaseMusicProvider {
  constructor(config: MusicProviderConfig) {
    super("mubert", config);
  }

  protected getDisplayName(): string {
    return "Mubert";
  }

  protected getSupportedDurations(): number[] {
    return [15, 30, 60];
  }

  protected getSupportedStyles(): string[] {
    return [
      "ambient", "electronic", "chill", "upbeat", "corporate",
      "cinematic", "jazz", "classical", "rock", "pop"
    ];
  }

  protected getMaxBpm(): number {
    return 200;
  }

  protected getMinBpm(): number {
    return 60;
  }

  protected supportsStems(): boolean {
    return false;
  }

  protected supportsVariations(): boolean {
    return true;
  }

  async generateMusic(
    prompt: string,
    duration: number,
    options?: MusicGenerationOptions
  ): Promise<{ task_id: string; estimated_time: number }> {
    try {
      // Mock implementation - replace with actual Mubert API calls
      const task_id = `mubert_${Date.now()}_${Math.random().toString(36).substring(7)}`;
      
      console.log("Mubert generation request:", {
        prompt,
        duration,
        options,
      });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 100));

      return {
        task_id,
        estimated_time: duration * 2, // Estimate 2x duration
      };
    } catch (error) {
      console.error("Mubert generation failed:", error);
      throw new Error("Failed to start Mubert generation");
    }
  }

  async checkStatus(task_id: string, context?: any) {
    try {
      // Mock implementation - replace with actual Mubert API calls
      if (context) {
        console.log("Checking Mubert status:", {
          generation_uuid: context.generation_uuid,
          provider_task_id: context.provider_task_id,
          user_uuid: context.user_uuid
        });
      } else {
        console.log("Checking Mubert status for:", task_id);
      }

      // Simulate different statuses based on time
      const age = Date.now() - parseInt(task_id.split('_')[1]);
      
      if (age < 5000) {
        return { status: "pending" as const };
      } else if (age < 15000) {
        return { 
          status: "processing" as const, 
          progress: Math.min(90, Math.floor((age - 5000) / 100))
        };
      } else {
        return {
          status: "completed" as const,
          progress: 100,
          result: {
            file_url: `https://example.com/mubert/${task_id}.mp3`,
            file_size: 1024 * 1024 * 2, // 2MB
            metadata: {
              provider: "mubert",
              generated_at: new Date().toISOString(),
            },
          },
        };
      }
    } catch (error) {
      console.error("Mubert status check failed:", error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      return { status: "failed" as const, error: errorMessage };
    }
  }

  async cancelGeneration(task_id: string): Promise<boolean> {
    try {
      console.log("Cancelling Mubert generation:", task_id);
      // Mock implementation
      return true;
    } catch (error) {
      console.error("Failed to cancel Mubert generation:", error);
      return false;
    }
  }
}

// Suno provider implementation
export class SunoProvider extends BaseMusicProvider {
  constructor(config: MusicProviderConfig) {
    super("suno", config);
  }

  protected getDisplayName(): string {
    return "Suno AI";
  }

  protected getSupportedDurations(): number[] {
    return [30, 60];
  }

  protected getSupportedStyles(): string[] {
    return [
      "pop", "rock", "electronic", "hip-hop", "jazz", "classical",
      "country", "folk", "reggae", "blues", "metal", "punk"
    ];
  }

  protected getMaxBpm(): number {
    return 180;
  }

  protected getMinBpm(): number {
    return 70;
  }

  protected supportsStems(): boolean {
    return true;
  }

  protected supportsVariations(): boolean {
    return true;
  }

  async generateMusic(
    prompt: string,
    duration: number,
    options?: MusicGenerationOptions
  ): Promise<{ task_id: string; estimated_time: number }> {
    try {
      const task_id = `suno_${Date.now()}_${Math.random().toString(36).substring(7)}`;
      
      console.log("Suno generation request:", {
        prompt,
        duration,
        options,
      });

      return {
        task_id,
        estimated_time: duration * 3, // Suno takes longer but higher quality
      };
    } catch (error) {
      console.error("Suno generation failed:", error);
      throw new Error("Failed to start Suno generation");
    }
  }

  async checkStatus(task_id: string, context?: any) {
    try {
      if (context) {
        console.log("Checking Suno status:", {
          generation_uuid: context.generation_uuid,
          provider_task_id: context.provider_task_id,
          user_uuid: context.user_uuid
        });
      } else {
        console.log("Checking Suno status for:", task_id);
      }

      const age = Date.now() - parseInt(task_id.split('_')[1]);
      
      if (age < 10000) {
        return { status: "pending" as const };
      } else if (age < 30000) {
        return { 
          status: "processing" as const, 
          progress: Math.min(90, Math.floor((age - 10000) / 200))
        };
      } else {
        return {
          status: "completed" as const,
          progress: 100,
          result: {
            file_url: `https://example.com/suno/${task_id}.mp3`,
            file_size: 1024 * 1024 * 3, // 3MB
            metadata: {
              provider: "suno",
              generated_at: new Date().toISOString(),
              quality: "high",
            },
          },
        };
      }
    } catch (error) {
      console.error("Suno status check failed:", error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      return { status: "failed" as const, error: errorMessage };
    }
  }

  async cancelGeneration(task_id: string): Promise<boolean> {
    try {
      console.log("Cancelling Suno generation:", task_id);
      return true;
    } catch (error) {
      console.error("Failed to cancel Suno generation:", error);
      return false;
    }
  }
}

// Music provider factory
export class MusicProviderFactory {
  private static providers: Map<string, BaseMusicProvider> = new Map();

  static registerProvider(name: string, provider: BaseMusicProvider) {
    this.providers.set(name, provider);
  }

  static getProvider(name: string): BaseMusicProvider | null {
    return this.providers.get(name) || null;
  }

  static getAllProviders(): MusicProvider[] {
    return Array.from(this.providers.values()).map(provider => 
      provider.getProviderInfo()
    );
  }

  static getDefaultProvider(): BaseMusicProvider | null {
    // 根据环境变量配置选择默认提供商
    const primaryProvider = process.env.NEXT_PUBLIC_PRIMARY_MUSIC_PROVIDER || "volcano";

    // 首先尝试使用配置的主要提供商
    if (this.providers.has(primaryProvider)) {
      return this.providers.get(primaryProvider)!;
    }

    // 如果主要提供商不可用，按优先级选择：volcano > mubert > suno > mock
    return this.providers.get("volcano") ||
           this.providers.get("mubert") ||
           this.providers.get("suno") ||
           this.providers.get("mock") ||
           null;
  }

  // Initialize default providers
  static async initialize() {
    // 验证TOS配置
    try {
      const { validateConfigOnStartup } = await import("@/lib/config-validator");
      const configValid = validateConfigOnStartup();
      
      if (!configValid) {
        console.error("❌ Configuration validation failed - some features may not work properly");
      }
    } catch (error) {
      console.error("Failed to validate configuration:", error);
    }

    // Initialize Mock provider (always available for testing)
    try {
      const { MockProvider } = await import("./mock-provider");
      const mockProvider = new MockProvider({});
      this.registerProvider("mock", mockProvider);
      console.log("✅ Mock provider registered");
    } catch (error) {
      console.error("Failed to initialize Mock provider:", error);
    }

    // Initialize Volcengine
    if (process.env.VOLCANO_ACCESS_KEY_ID && process.env.VOLCANO_SECRET_ACCESS_KEY) {
      try {
        const { VolcanoProvider } = await import("./volcano-provider");
        const volcanoProvider = new VolcanoProvider({
          api_key: "", // Volcano 不使用这个字段
          access_key_id: process.env.VOLCANO_ACCESS_KEY_ID,
          secret_access_key: process.env.VOLCANO_SECRET_ACCESS_KEY,
          base_url: process.env.VOLCANO_BASE_URL,
          region: process.env.VOLCANO_REGION,
          service_name: process.env.VOLCANO_SERVICE_NAME,
          version: process.env.VOLCANO_VERSION,
          timeout: 60000,
          max_retries: 3,
        });
        this.registerProvider("volcano", volcanoProvider);
        console.log("✅ Volcano provider registered");
      } catch (error) {
        console.error("Failed to initialize Volcano provider:", error);
      }
    }

    // Initialize Mubert
    if (process.env.MUBERT_API_KEY) {
      const mubertProvider = new MubertProvider({
        api_key: process.env.MUBERT_API_KEY,
        base_url: process.env.MUBERT_BASE_URL,
        timeout: 30000,
        max_retries: 3,
      });
      this.registerProvider("mubert", mubertProvider);
    }

    // Initialize Suno
    if (process.env.SUNO_API_KEY) {
      const sunoProvider = new SunoProvider({
        api_key: process.env.SUNO_API_KEY,
        base_url: process.env.SUNO_BASE_URL,
        timeout: 60000,
        max_retries: 2,
      });
      this.registerProvider("suno", sunoProvider);
    }

    console.log(`Initialized ${this.providers.size} music providers`);
  }
}

// Initialize providers on module load
MusicProviderFactory.initialize().catch(console.error);
