import type { 
  ContentGenerator, 
  SitemapEntry, 
  AlternateLink 
} from "@/types/sitemap";
import { URLBuilder } from "./sitemap-url-builder";
import { 
  STATIC_PAGES, 
  SITEMAP_CONFIG,
  CONTENT_TYPE_CONFIG 
} from "@/constants/sitemap";
import { 
  createSitemapEntry,
  validateSitemapEntry 
} from "@/lib/sitemap-utils";
import { getPostsByLocale, PostStatus } from "@/models/post";
import { getPublicTracks } from "@/models/track";

/**
 * Abstract base class for sitemap content generators
 * Provides common functionality for all content generators
 */
export abstract class BaseContentGenerator implements ContentGenerator {
  protected urlBuilder: URLBuilder;
  protected contentType: string;

  constructor(contentType: string, urlBuilder?: URLBuilder) {
    this.contentType = contentType;
    this.urlBuilder = urlBuilder || new URLBuilder();
  }

  /**
   * Abstract method to be implemented by concrete generators
   */
  abstract generate(locale: string, limit?: number): Promise<SitemapEntry[]>;

  /**
   * Abstract method to get last modified date for content type
   */
  abstract getLastModified(): Promise<Date>;

  /**
   * Abstract method to get estimated count of entries
   */
  abstract getEstimatedCount(): Promise<number>;

  /**
   * Creates a sitemap entry with proper URL and alternate links
   */
  protected createEntry(
    path: string,
    locale: string,
    lastmod?: Date | string,
    changefreq?: SitemapEntry['changefreq'],
    priority?: string,
    includeAlternates: boolean = true
  ): SitemapEntry {
    const url = this.urlBuilder.buildUrl(locale, path);
    const alternates: AlternateLink[] = includeAlternates 
      ? this.urlBuilder.buildAlternateLinks(path)
      : [];

    const entry = createSitemapEntry(
      url,
      lastmod,
      changefreq,
      priority,
      alternates
    );

    // Validate the entry before returning
    if (!validateSitemapEntry(entry)) {
      throw new Error(`Invalid sitemap entry created for path: ${path}`);
    }

    return entry;
  }

  /**
   * Filters and validates entries before returning
   */
  protected validateEntries(entries: SitemapEntry[]): SitemapEntry[] {
    return entries.filter(entry => {
      try {
        return validateSitemapEntry(entry);
      } catch (error) {
        console.warn(`Invalid sitemap entry filtered out: ${entry.url}`, error);
        return false;
      }
    });
  }

  /**
   * Gets the content type identifier
   */
  getContentType(): string {
    return this.contentType;
  }

  /**
   * Gets the URL builder instance
   */
  getUrlBuilder(): URLBuilder {
    return this.urlBuilder;
  }
}

/**
 * Static Pages Content Generator
 * Generates sitemap entries for static pages like home, generate, explore, etc.
 */
export class StaticPagesGenerator extends BaseContentGenerator {
  constructor(urlBuilder?: URLBuilder) {
    super('static', urlBuilder);
  }

  /**
   * Generates sitemap entries for static pages
   */
  async generate(locale: string, limit?: number): Promise<SitemapEntry[]> {
    const entries: SitemapEntry[] = [];
    const currentDate = new Date();
    
    // Get static pages configuration
    const pagesToProcess = limit ? STATIC_PAGES.slice(0, limit) : STATIC_PAGES;

    for (const pageConfig of pagesToProcess) {
      try {
        // Check if page should be included for this locale
        if (pageConfig.excludeFromLocales?.includes(locale)) {
          continue;
        }

        if (pageConfig.includeInLocales && !pageConfig.includeInLocales.includes(locale)) {
          continue;
        }

        // Create sitemap entry for the page
        const entry = this.createEntry(
          pageConfig.path,
          locale,
          currentDate,
          pageConfig.changefreq,
          pageConfig.priority,
          SITEMAP_CONFIG.enableHreflang
        );

        entries.push(entry);
      } catch (error) {
        console.error(`Error generating sitemap entry for static page ${pageConfig.path}:`, error);
        // Continue with other pages even if one fails
      }
    }

    return this.validateEntries(entries);
  }

  /**
   * Gets the last modified date for static pages
   * Since static pages don't have database records, we use deployment time or current time
   */
  async getLastModified(): Promise<Date> {
    // In a real application, this could be the deployment time or build time
    // For now, we'll use a reasonable default
    return new Date();
  }

  /**
   * Gets estimated count of static page entries
   */
  async getEstimatedCount(): Promise<number> {
    // Return count of static pages multiplied by number of locales
    return STATIC_PAGES.length * SITEMAP_CONFIG.locales.length;
  }

  /**
   * Gets static pages configuration
   */
  getStaticPagesConfig() {
    return STATIC_PAGES;
  }

  /**
   * Checks if a specific page exists in static pages
   */
  hasStaticPage(path: string): boolean {
    return STATIC_PAGES.some(page => page.path === path);
  }

  /**
   * Gets configuration for a specific static page
   */
  getPageConfig(path: string) {
    return STATIC_PAGES.find(page => page.path === path);
  }
}

/**
 * Blog Posts Content Generator
 * Generates sitemap entries for blog posts from the database
 */
export class BlogPostsGenerator extends BaseContentGenerator {
  constructor(urlBuilder?: URLBuilder) {
    super('posts', urlBuilder);
  }

  /**
   * Generates sitemap entries for blog posts
   */
  async generate(locale: string, limit?: number): Promise<SitemapEntry[]> {
    const entries: SitemapEntry[] = [];
    
    try {
      // Fetch posts for the specified locale
      const posts = await getPostsByLocale(locale, 1, limit || 1000);
      
      if (!posts || !Array.isArray(posts) || posts.length === 0) {
        return entries;
      }

      for (const post of posts) {
        try {
          // Skip posts without slug or that are not online
          if (!post.slug || post.status !== PostStatus.Online) {
            continue;
          }

          // Create sitemap entry for the post
          const lastModified = post.updated_at || post.created_at || new Date();
          const entry = this.createEntry(
            `posts/${post.slug}`,
            locale,
            lastModified,
            CONTENT_TYPE_CONFIG.BLOG_POSTS.changefreq,
            CONTENT_TYPE_CONFIG.BLOG_POSTS.priority,
            SITEMAP_CONFIG.enableHreflang
          );

          entries.push(entry);
        } catch (error) {
          console.error(`Error generating sitemap entry for post ${post.slug}:`, error);
          // Continue with other posts even if one fails
        }
      }
    } catch (error) {
      console.error('Error fetching posts for sitemap generation:', error);
      // Return empty array if database query fails
    }

    return this.validateEntries(entries);
  }

  /**
   * Gets the last modified date for blog posts
   */
  async getLastModified(): Promise<Date> {
    try {
      // Get the most recent post from all locales to determine last modified
      const recentPosts = await Promise.all(
        SITEMAP_CONFIG.locales.map(locale => getPostsByLocale(locale, 1, 1))
      );

      let latestDate = new Date(0); // Start with epoch

      recentPosts.forEach(posts => {
        if (posts && posts.length > 0) {
          const post = posts[0];
          const postDate = new Date(post.updated_at || post.created_at || new Date());
          if (postDate > latestDate) {
            latestDate = postDate;
          }
        }
      });

      // If no posts found, return current date
      return latestDate.getTime() === 0 ? new Date() : latestDate;
    } catch (error) {
      console.error('Error getting last modified date for posts:', error);
      return new Date();
    }
  }

  /**
   * Gets estimated count of blog post entries
   */
  async getEstimatedCount(): Promise<number> {
    try {
      // Get count for each locale and sum them up
      const counts = await Promise.all(
        SITEMAP_CONFIG.locales.map(async (locale) => {
          const posts = await getPostsByLocale(locale, 1, 1000);
          return posts ? posts.length : 0;
        })
      );

      return counts.reduce((total, count) => total + count, 0);
    } catch (error) {
      console.error('Error getting estimated count for posts:', error);
      return 0;
    }
  }

  /**
   * Gets posts for a specific locale (useful for debugging)
   */
  async getPostsForLocale(locale: string, limit?: number) {
    return await getPostsByLocale(locale, 1, limit || 1000);
  }
}

/**
 * Music Loops Content Generator
 * Generates sitemap entries for public music loops from the database
 */
export class MusicLoopsGenerator extends BaseContentGenerator {
  constructor(urlBuilder?: URLBuilder) {
    super('loops', urlBuilder);
  }

  /**
   * Generates sitemap entries for music loops
   */
  async generate(locale: string, limit?: number): Promise<SitemapEntry[]> {
    const entries: SitemapEntry[] = [];
    
    try {
      // Fetch public tracks (music loops don't have locale-specific content)
      const tracks = await getPublicTracks(limit || 1000, 0);
      
      if (!tracks || !Array.isArray(tracks) || tracks.length === 0) {
        return entries;
      }

      for (const track of tracks) {
        try {
          // Skip tracks without slug or that are not public
          if (!track.slug || !track.is_public) {
            continue;
          }

          // Create sitemap entry for the track
          const lastModified = track.updated_at || track.created_at || new Date();
          const entry = this.createEntry(
            `loops/${track.slug}`,
            locale,
            lastModified,
            CONTENT_TYPE_CONFIG.MUSIC_LOOPS.changefreq,
            CONTENT_TYPE_CONFIG.MUSIC_LOOPS.priority,
            SITEMAP_CONFIG.enableHreflang
          );

          entries.push(entry);
        } catch (error) {
          console.error(`Error generating sitemap entry for track ${track.slug}:`, error);
          // Continue with other tracks even if one fails
        }
      }
    } catch (error) {
      console.error('Error fetching tracks for sitemap generation:', error);
      // Return empty array if database query fails
    }

    return this.validateEntries(entries);
  }

  /**
   * Gets the last modified date for music loops
   */
  async getLastModified(): Promise<Date> {
    try {
      // Get the most recent public track to determine last modified
      const recentTracks = await getPublicTracks(1, 0);

      if (recentTracks && recentTracks.length > 0) {
        const track = recentTracks[0];
        return new Date(track.updated_at || track.created_at || new Date());
      }

      // If no tracks found, return current date
      return new Date();
    } catch (error) {
      console.error('Error getting last modified date for tracks:', error);
      return new Date();
    }
  }

  /**
   * Gets estimated count of music loop entries
   */
  async getEstimatedCount(): Promise<number> {
    try {
      // Get a large batch to estimate total count
      // Since music loops are not locale-specific, we multiply by locale count
      const tracks = await getPublicTracks(10000, 0);
      const trackCount = tracks ? tracks.filter(track => track.slug && track.is_public).length : 0;
      
      // Multiply by number of locales since each track appears in all locales
      return trackCount * SITEMAP_CONFIG.locales.length;
    } catch (error) {
      console.error('Error getting estimated count for tracks:', error);
      return 0;
    }
  }

  /**
   * Gets public tracks (useful for debugging)
   */
  async getPublicTracksForSitemap(limit?: number) {
    return await getPublicTracks(limit || 1000, 0);
  }
}

/**
 * Factory function to create content generators
 */
export function createContentGenerator(
  type: 'static' | 'posts' | 'loops',
  urlBuilder?: URLBuilder
): ContentGenerator {
  switch (type) {
    case 'static':
      return new StaticPagesGenerator(urlBuilder);
    case 'posts':
      return new BlogPostsGenerator(urlBuilder);
    case 'loops':
      return new MusicLoopsGenerator(urlBuilder);
    default:
      throw new Error(`Unknown content generator type: ${type}`);
  }
}

/**
 * Gets default configuration for content type
 */
export function getContentTypeConfig(type: string) {
  switch (type) {
    case 'static':
      return CONTENT_TYPE_CONFIG.STATIC_PAGES;
    case 'posts':
      return CONTENT_TYPE_CONFIG.BLOG_POSTS;
    case 'loops':
      return CONTENT_TYPE_CONFIG.MUSIC_LOOPS;
    default:
      return {
        priority: "0.5",
        changefreq: "weekly" as const,
      };
  }
}