import { SitemapValidator, type SitemapValidationResult } from "./sitemap-validator";
import { sitemapAnalytics } from "./sitemap-analytics";
import type { SitemapEntry, SitemapXMLResult, SitemapContentType } from "@/types/sitemap";

/**
 * Quality assurance report
 */
export interface QualityAssuranceReport {
  overall: {
    score: number; // 0-100
    grade: 'A' | 'B' | 'C' | 'D' | 'F';
    status: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';
  };
  validation: SitemapValidationResult;
  recommendations: string[];
  criticalIssues: string[];
  performanceMetrics: {
    generationTime: number;
    xmlSize: number;
    urlCount: number;
    duplicateCount: number;
    errorRate: number;
  };
  contentQuality: {
    coverageScore: number;
    freshnessScore: number;
    structureScore: number;
  };
  timestamp: Date;
}

/**
 * Sitemap Quality Assurance Service
 * Provides comprehensive quality assessment and recommendations
 */
export class SitemapQualityAssurance {
  private validator: SitemapValidator;

  constructor(validator?: SitemapValidator) {
    this.validator = validator || new SitemapValidator();
  }

  /**
   * Performs comprehensive quality assurance check
   */
  async performQualityAssurance(
    xmlResult: SitemapXMLResult,
    entries: SitemapEntry[],
    contentType: SitemapContentType,
    options?: {
      checkAccessibility?: boolean;
      maxUrlsToCheck?: number;
    }
  ): Promise<QualityAssuranceReport> {
    // Perform validation
    const validation = await this.validator.validateSitemap(xmlResult, entries, options);

    // Calculate quality scores
    const contentQuality = this.assessContentQuality(entries, contentType);
    const performanceMetrics = this.calculatePerformanceMetrics(xmlResult, validation);
    
    // Generate recommendations and identify critical issues
    const recommendations = this.generateRecommendations(validation, contentQuality, performanceMetrics);
    const criticalIssues = this.identifyCriticalIssues(validation);

    // Calculate overall score and grade
    const overall = this.calculateOverallScore(validation, contentQuality, performanceMetrics);

    return {
      overall,
      validation,
      recommendations,
      criticalIssues,
      performanceMetrics,
      contentQuality,
      timestamp: new Date(),
    };
  }

  /**
   * Assesses content quality metrics
   */
  private assessContentQuality(entries: SitemapEntry[], contentType: SitemapContentType): {
    coverageScore: number;
    freshnessScore: number;
    structureScore: number;
  } {
    // Coverage score (based on expected vs actual URLs)
    const coverageScore = this.calculateCoverageScore(entries, contentType);

    // Freshness score (based on lastmod dates)
    const freshnessScore = this.calculateFreshnessScore(entries);

    // Structure score (based on URL structure and metadata quality)
    const structureScore = this.calculateStructureScore(entries);

    return {
      coverageScore,
      freshnessScore,
      structureScore,
    };
  }

  /**
   * Calculates coverage score based on expected content
   */
  private calculateCoverageScore(entries: SitemapEntry[], contentType: SitemapContentType): number {
    // This would normally check against expected content from database
    // For now, we'll use heuristics based on URL patterns and content type
    
    let expectedPatterns: RegExp[] = [];
    
    switch (contentType) {
      case 'static':
        expectedPatterns = [
          /\/$/, // Homepage
          /\/generate$/,
          /\/explore$/,
          /\/posts$/,
          /\/pricing$/,
        ];
        break;
      case 'posts':
        expectedPatterns = [
          /\/posts\/[^\/]+$/,
        ];
        break;
      case 'loops':
        expectedPatterns = [
          /\/loops\/[^\/]+$/,
        ];
        break;
    }

    if (expectedPatterns.length === 0) {
      return 100; // No specific expectations
    }

    const matchingUrls = entries.filter(entry => 
      expectedPatterns.some(pattern => pattern.test(new URL(entry.url).pathname))
    );

    const coverageRatio = matchingUrls.length / Math.max(expectedPatterns.length, entries.length);
    return Math.min(100, coverageRatio * 100);
  }

  /**
   * Calculates freshness score based on lastmod dates
   */
  private calculateFreshnessScore(entries: SitemapEntry[]): number {
    if (entries.length === 0) return 100;

    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    let totalScore = 0;
    let validEntries = 0;

    for (const entry of entries) {
      if (entry.lastmod) {
        const lastmodDate = new Date(entry.lastmod);
        if (!isNaN(lastmodDate.getTime())) {
          validEntries++;
          
          const daysSinceUpdate = (now.getTime() - lastmodDate.getTime()) / (24 * 60 * 60 * 1000);
          
          // Score based on recency (100 for today, decreasing over time)
          let entryScore = Math.max(0, 100 - (daysSinceUpdate * 2));
          
          // Bonus for very recent updates
          if (daysSinceUpdate < 1) entryScore = 100;
          else if (daysSinceUpdate < 7) entryScore = Math.max(entryScore, 90);
          else if (daysSinceUpdate < 30) entryScore = Math.max(entryScore, 70);
          
          totalScore += entryScore;
        }
      }
    }

    return validEntries > 0 ? totalScore / validEntries : 50; // Default score if no valid dates
  }

  /**
   * Calculates structure score based on URL quality and metadata
   */
  private calculateStructureScore(entries: SitemapEntry[]): number {
    if (entries.length === 0) return 100;

    let totalScore = 0;

    for (const entry of entries) {
      let entryScore = 100;

      try {
        const url = new URL(entry.url);
        
        // Deduct points for poor URL structure
        if (url.pathname.includes('//')) entryScore -= 10; // Double slashes
        if (url.pathname.includes('_')) entryScore -= 5; // Underscores
        if (url.pathname.length > 100) entryScore -= 10; // Very long paths
        if (url.search.length > 50) entryScore -= 15; // Long query strings
        if (url.pathname.split('/').length > 6) entryScore -= 10; // Too deep nesting

        // Deduct points for missing metadata
        if (!entry.lastmod) entryScore -= 20;
        if (!entry.changefreq) entryScore -= 10;
        if (!entry.priority) entryScore -= 10;

        // Bonus points for good practices
        if (entry.alternates && entry.alternates.length > 0) entryScore += 10; // Internationalization
        if (url.pathname.endsWith('/') && url.pathname !== '/') entryScore -= 5; // Trailing slashes (except root)

      } catch {
        entryScore = 0; // Invalid URL
      }

      totalScore += Math.max(0, entryScore);
    }

    return totalScore / entries.length;
  }

  /**
   * Calculates performance metrics
   */
  private calculatePerformanceMetrics(
    xmlResult: SitemapXMLResult,
    validation: SitemapValidationResult
  ): {
    generationTime: number;
    xmlSize: number;
    urlCount: number;
    duplicateCount: number;
    errorRate: number;
  } {
    return {
      generationTime: xmlResult.generationTime,
      xmlSize: validation.performance.totalSize,
      urlCount: validation.performance.urlCount,
      duplicateCount: validation.duplicateUrls.length,
      errorRate: validation.urlsInvalid / Math.max(1, validation.urlsValid + validation.urlsInvalid) * 100,
    };
  }

  /**
   * Generates actionable recommendations
   */
  private generateRecommendations(
    validation: SitemapValidationResult,
    contentQuality: { coverageScore: number; freshnessScore: number; structureScore: number },
    performanceMetrics: { generationTime: number; xmlSize: number; urlCount: number; duplicateCount: number; errorRate: number }
  ): string[] {
    const recommendations: string[] = [];

    // XML and validation recommendations
    if (!validation.xmlValid) {
      recommendations.push("Fix XML structure errors to ensure proper sitemap parsing");
    }

    if (validation.duplicateUrls.length > 0) {
      recommendations.push(`Remove ${validation.duplicateUrls.length} duplicate URLs to improve sitemap efficiency`);
    }

    if (validation.errors.length > 0) {
      recommendations.push("Address validation errors to ensure search engine compatibility");
    }

    // Performance recommendations
    if (performanceMetrics.generationTime > 5000) {
      recommendations.push("Optimize sitemap generation performance (currently taking over 5 seconds)");
    }

    if (performanceMetrics.xmlSize > 10 * 1024 * 1024) { // 10MB
      recommendations.push("Consider splitting large sitemap into multiple files using sitemap index");
    }

    if (performanceMetrics.errorRate > 10) {
      recommendations.push("Investigate and fix URLs returning HTTP errors");
    }

    // Content quality recommendations
    if (contentQuality.coverageScore < 80) {
      recommendations.push("Improve content coverage by ensuring all important pages are included");
    }

    if (contentQuality.freshnessScore < 70) {
      recommendations.push("Update lastmod dates more frequently to reflect content changes");
    }

    if (contentQuality.structureScore < 80) {
      recommendations.push("Improve URL structure and ensure all entries have proper metadata");
    }

    // URL accessibility recommendations
    if (validation.performance.slowUrls.length > 0) {
      recommendations.push(`Optimize ${validation.performance.slowUrls.length} slow-loading URLs for better user experience`);
    }

    // Hreflang recommendations
    const hreflangErrors = validation.errors.filter(error => error.includes('hreflang'));
    if (hreflangErrors.length > 0) {
      recommendations.push("Fix hreflang annotations for proper international SEO");
    }

    return recommendations;
  }

  /**
   * Identifies critical issues that need immediate attention
   */
  private identifyCriticalIssues(validation: SitemapValidationResult): string[] {
    const criticalIssues: string[] = [];

    // Critical XML issues
    if (!validation.xmlValid) {
      criticalIssues.push("Invalid XML structure prevents search engine parsing");
    }

    // Critical size issues
    if (validation.performance.totalSize > 50 * 1024 * 1024) {
      criticalIssues.push("Sitemap exceeds maximum size limit (50MB)");
    }

    if (validation.performance.urlCount > 50000) {
      criticalIssues.push("Sitemap exceeds maximum URL limit (50,000)");
    }

    // Critical error rate
    const errorRate = validation.urlsInvalid / Math.max(1, validation.urlsValid + validation.urlsInvalid) * 100;
    if (errorRate > 25) {
      criticalIssues.push(`High error rate (${errorRate.toFixed(1)}%) indicates serious content issues`);
    }

    // Critical duplicate issues
    if (validation.duplicateUrls.length > validation.performance.urlCount * 0.1) {
      criticalIssues.push("Excessive duplicate URLs (>10%) waste crawl budget");
    }

    return criticalIssues;
  }

  /**
   * Calculates overall quality score and grade
   */
  private calculateOverallScore(
    validation: SitemapValidationResult,
    contentQuality: { coverageScore: number; freshnessScore: number; structureScore: number },
    performanceMetrics: { generationTime: number; xmlSize: number; urlCount: number; duplicateCount: number; errorRate: number }
  ): { score: number; grade: 'A' | 'B' | 'C' | 'D' | 'F'; status: 'excellent' | 'good' | 'fair' | 'poor' | 'critical' } {
    let score = 100;

    // Deduct points for validation issues
    if (!validation.xmlValid) score -= 30;
    score -= validation.errors.length * 5;
    score -= validation.warnings.length * 2;
    score -= validation.duplicateUrls.length * 2;

    // Deduct points for performance issues
    if (performanceMetrics.generationTime > 5000) score -= 10;
    if (performanceMetrics.errorRate > 10) score -= 20;
    if (performanceMetrics.duplicateCount > 0) score -= Math.min(20, performanceMetrics.duplicateCount);

    // Factor in content quality (weighted average)
    const contentScore = (
      contentQuality.coverageScore * 0.4 +
      contentQuality.freshnessScore * 0.3 +
      contentQuality.structureScore * 0.3
    );
    
    // Blend overall score with content quality
    score = (score * 0.7) + (contentScore * 0.3);

    // Ensure score is within bounds
    score = Math.max(0, Math.min(100, score));

    // Determine grade and status
    let grade: 'A' | 'B' | 'C' | 'D' | 'F';
    let status: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';

    if (score >= 90) {
      grade = 'A';
      status = 'excellent';
    } else if (score >= 80) {
      grade = 'B';
      status = 'good';
    } else if (score >= 70) {
      grade = 'C';
      status = 'fair';
    } else if (score >= 60) {
      grade = 'D';
      status = 'poor';
    } else {
      grade = 'F';
      status = 'critical';
    }

    return { score: Math.round(score), grade, status };
  }

  /**
   * Generates a summary report for quick overview
   */
  generateSummaryReport(report: QualityAssuranceReport): string {
    const lines = [
      `Sitemap Quality Report - Grade: ${report.overall.grade} (${report.overall.score}/100)`,
      `Status: ${report.overall.status.toUpperCase()}`,
      ``,
      `📊 Performance Metrics:`,
      `  • Generation Time: ${report.performanceMetrics.generationTime}ms`,
      `  • XML Size: ${(report.performanceMetrics.xmlSize / 1024).toFixed(1)}KB`,
      `  • URL Count: ${report.performanceMetrics.urlCount}`,
      `  • Duplicates: ${report.performanceMetrics.duplicateCount}`,
      `  • Error Rate: ${report.performanceMetrics.errorRate.toFixed(1)}%`,
      ``,
      `📈 Content Quality:`,
      `  • Coverage: ${report.contentQuality.coverageScore.toFixed(1)}/100`,
      `  • Freshness: ${report.contentQuality.freshnessScore.toFixed(1)}/100`,
      `  • Structure: ${report.contentQuality.structureScore.toFixed(1)}/100`,
      ``,
    ];

    if (report.criticalIssues.length > 0) {
      lines.push(`🚨 Critical Issues:`);
      report.criticalIssues.forEach(issue => lines.push(`  • ${issue}`));
      lines.push(``);
    }

    if (report.recommendations.length > 0) {
      lines.push(`💡 Top Recommendations:`);
      report.recommendations.slice(0, 5).forEach(rec => lines.push(`  • ${rec}`));
      if (report.recommendations.length > 5) {
        lines.push(`  • ... and ${report.recommendations.length - 5} more`);
      }
    }

    return lines.join('\n');
  }

  /**
   * Updates validator configuration
   */
  updateValidatorConfig(options: {
    maxConcurrentRequests?: number;
    requestTimeout?: number;
    slowUrlThreshold?: number;
  }): void {
    this.validator.updateConfig(options);
  }
}