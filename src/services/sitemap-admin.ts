import { SitemapIndexGenerator } from "./sitemap-index-generator";
import { SitemapCacheManager } from "./sitemap-cache";
import { sitemapMonitor } from "./sitemap-monitor";
import { sitemapAnalytics } from "./sitemap-analytics";
import { SitemapValidator } from "./sitemap-validator";
import { SitemapQualityAssurance } from "./sitemap-quality-assurance";
import type { SitemapContentType } from "@/types/sitemap";
import { SITEMAP_CONFIG } from "@/constants/sitemap";

/**
 * Cache warming result
 */
export interface CacheWarmingResult {
  success: boolean;
  warmedSitemaps: string[];
  errors: string[];
  totalTime: number;
  cacheHitsBefore: number;
  cacheHitsAfter: number;
}

/**
 * Regeneration result
 */
export interface RegenerationResult {
  success: boolean;
  regeneratedSitemaps: string[];
  errors: string[];
  totalTime: number;
  totalUrls: number;
}

/**
 * Health check result
 */
export interface SystemHealthResult {
  overall: 'healthy' | 'warning' | 'critical';
  components: {
    cache: { status: string; details: any };
    generation: { status: string; details: any };
    monitoring: { status: string; details: any };
    validation: { status: string; details: any };
  };
  recommendations: string[];
  timestamp: Date;
}

/**
 * Statistics result
 */
export interface SystemStatistics {
  generation: {
    totalGenerations: number;
    averageTime: number;
    successRate: number;
    recentErrors: number;
  };
  cache: {
    hitRate: number;
    totalRequests: number;
    cacheHits: number;
    cacheMisses: number;
    averageGenerationTime: number;
  };
  content: {
    totalUrls: number;
    byContentType: Record<string, number>;
    byLocale: Record<string, number>;
    lastUpdated: Date;
  };
  performance: {
    averageResponseTime: number;
    slowUrls: number;
    errorRate: number;
  };
}

/**
 * Sitemap Administration Service
 * Provides administrative utilities for sitemap management
 */
export class SitemapAdmin {
  private sitemapGenerator: SitemapIndexGenerator;
  private cacheManager: SitemapCacheManager;
  private validator: SitemapValidator;
  private qualityAssurance: SitemapQualityAssurance;

  constructor(
    sitemapGenerator: SitemapIndexGenerator,
    cacheManager: SitemapCacheManager,
    validator?: SitemapValidator,
    qualityAssurance?: SitemapQualityAssurance
  ) {
    this.sitemapGenerator = sitemapGenerator;
    this.cacheManager = cacheManager;
    this.validator = validator || new SitemapValidator();
    this.qualityAssurance = qualityAssurance || new SitemapQualityAssurance(this.validator);
  }

  /**
   * Warms up the sitemap cache by pre-generating all sitemaps
   */
  async warmCache(options?: {
    contentTypes?: SitemapContentType[];
    locales?: string[];
    forceRegeneration?: boolean;
  }): Promise<CacheWarmingResult> {
    const startTime = Date.now();
    const contentTypes = options?.contentTypes || ['static', 'posts', 'loops'];
    const locales = options?.locales || SITEMAP_CONFIG.locales;
    const forceRegeneration = options?.forceRegeneration || false;

    const warmedSitemaps: string[] = [];
    const errors: string[] = [];

    // Get initial cache stats
    const initialStats = this.cacheManager.getStats();
    const cacheHitsBefore = initialStats.cacheHits;

    console.log(`[Sitemap Admin] Starting cache warming for ${contentTypes.length} content types and ${locales.length} locales`);

    // Warm main sitemap for each locale
    for (const locale of locales) {
      try {
        if (forceRegeneration) {
          await this.cacheManager.invalidateCache('index', locale);
        }
        
        await this.sitemapGenerator.generateSitemap(locale);
        warmedSitemaps.push(`index:${locale}`);
        console.log(`[Sitemap Admin] Warmed main sitemap for locale: ${locale}`);
      } catch (error) {
        const errorMsg = `Failed to warm main sitemap for locale ${locale}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        errors.push(errorMsg);
        console.error(`[Sitemap Admin] ${errorMsg}`);
      }
    }

    // Warm content-specific sitemaps
    for (const contentType of contentTypes) {
      for (const locale of locales) {
        try {
          if (forceRegeneration) {
            await this.cacheManager.invalidateCache(contentType, locale);
          }
          
          await this.sitemapGenerator.generateContentTypeSitemap(contentType, locale);
          warmedSitemaps.push(`${contentType}:${locale}`);
          console.log(`[Sitemap Admin] Warmed ${contentType} sitemap for locale: ${locale}`);
        } catch (error) {
          const errorMsg = `Failed to warm ${contentType} sitemap for locale ${locale}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          errors.push(errorMsg);
          console.error(`[Sitemap Admin] ${errorMsg}`);
        }
      }
    }

    // Get final cache stats
    const finalStats = this.cacheManager.getStats();
    const cacheHitsAfter = finalStats.cacheHits;

    const totalTime = Date.now() - startTime;
    const success = errors.length === 0;

    console.log(`[Sitemap Admin] Cache warming completed in ${totalTime}ms. Warmed ${warmedSitemaps.length} sitemaps with ${errors.length} errors`);

    return {
      success,
      warmedSitemaps,
      errors,
      totalTime,
      cacheHitsBefore,
      cacheHitsAfter,
    };
  }

  /**
   * Manually regenerates all sitemaps
   */
  async regenerateAllSitemaps(options?: {
    contentTypes?: SitemapContentType[];
    locales?: string[];
    clearCache?: boolean;
  }): Promise<RegenerationResult> {
    const startTime = Date.now();
    const contentTypes = options?.contentTypes || ['static', 'posts', 'loops'];
    const locales = options?.locales || SITEMAP_CONFIG.locales;
    const clearCache = options?.clearCache || true;

    const regeneratedSitemaps: string[] = [];
    const errors: string[] = [];
    let totalUrls = 0;

    console.log(`[Sitemap Admin] Starting regeneration of all sitemaps`);

    // Clear cache if requested
    if (clearCache) {
      console.log(`[Sitemap Admin] Clearing cache before regeneration`);
      await this.cacheManager.invalidateCache();
    }

    // Regenerate main sitemap for each locale
    for (const locale of locales) {
      try {
        const result = await this.sitemapGenerator.generateSitemap(locale);
        regeneratedSitemaps.push(`index:${locale}`);
        totalUrls += result.urlCount;
        console.log(`[Sitemap Admin] Regenerated main sitemap for locale: ${locale} (${result.urlCount} URLs)`);
      } catch (error) {
        const errorMsg = `Failed to regenerate main sitemap for locale ${locale}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        errors.push(errorMsg);
        console.error(`[Sitemap Admin] ${errorMsg}`);
      }
    }

    // Regenerate content-specific sitemaps
    for (const contentType of contentTypes) {
      for (const locale of locales) {
        try {
          const result = await this.sitemapGenerator.generateContentTypeSitemap(contentType, locale);
          regeneratedSitemaps.push(`${contentType}:${locale}`);
          totalUrls += result.urlCount;
          console.log(`[Sitemap Admin] Regenerated ${contentType} sitemap for locale: ${locale} (${result.urlCount} URLs)`);
        } catch (error) {
          const errorMsg = `Failed to regenerate ${contentType} sitemap for locale ${locale}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          errors.push(errorMsg);
          console.error(`[Sitemap Admin] ${errorMsg}`);
        }
      }
    }

    const totalTime = Date.now() - startTime;
    const success = errors.length === 0;

    console.log(`[Sitemap Admin] Regeneration completed in ${totalTime}ms. Generated ${regeneratedSitemaps.length} sitemaps with ${totalUrls} total URLs and ${errors.length} errors`);

    return {
      success,
      regeneratedSitemaps,
      errors,
      totalTime,
      totalUrls,
    };
  }

  /**
   * Performs comprehensive system health check
   */
  async performHealthCheck(): Promise<SystemHealthResult> {
    console.log(`[Sitemap Admin] Performing comprehensive health check`);

    const components = {
      cache: await this.checkCacheHealth(),
      generation: await this.checkGenerationHealth(),
      monitoring: await this.checkMonitoringHealth(),
      validation: await this.checkValidationHealth(),
    };

    // Determine overall health
    const statuses = Object.values(components).map(comp => comp.status);
    let overall: 'healthy' | 'warning' | 'critical' = 'healthy';
    
    if (statuses.includes('critical')) {
      overall = 'critical';
    } else if (statuses.includes('warning')) {
      overall = 'warning';
    }

    // Generate recommendations
    const recommendations = this.generateHealthRecommendations(components);

    return {
      overall,
      components,
      recommendations,
      timestamp: new Date(),
    };
  }

  /**
   * Gets comprehensive system statistics
   */
  async getSystemStatistics(): Promise<SystemStatistics> {
    console.log(`[Sitemap Admin] Gathering system statistics`);

    // Generation statistics
    const analyticsData = sitemapAnalytics.getAnalyticsSummary();
    const performanceData = sitemapAnalytics.getPerformanceMetrics(60);

    // Cache statistics
    const cacheStats = this.cacheManager.getStats();

    // Content statistics
    const contentStats = await this.getContentStatistics();

    return {
      generation: {
        totalGenerations: analyticsData.totalGenerations,
        averageTime: analyticsData.averageGenerationTime,
        successRate: analyticsData.totalGenerations > 0 
          ? ((analyticsData.totalGenerations - analyticsData.totalErrors) / analyticsData.totalGenerations) * 100 
          : 100,
        recentErrors: sitemapAnalytics.getRecentErrors(60).length,
      },
      cache: {
        hitRate: cacheStats.hitRate,
        totalRequests: cacheStats.totalRequests,
        cacheHits: cacheStats.cacheHits,
        cacheMisses: cacheStats.cacheMisses,
        averageGenerationTime: cacheStats.averageGenerationTime,
      },
      content: contentStats,
      performance: {
        averageResponseTime: performanceData.averageTime,
        slowUrls: performanceData.slowRequests,
        errorRate: performanceData.errorRate,
      },
    };
  }

  /**
   * Creates debugging information for troubleshooting
   */
  async createDebugReport(options?: {
    includeCache?: boolean;
    includeAnalytics?: boolean;
    includeValidation?: boolean;
    contentType?: SitemapContentType;
    locale?: string;
  }): Promise<{
    system: any;
    cache?: any;
    analytics?: any;
    validation?: any;
    sampleGeneration?: any;
  }> {
    const includeCache = options?.includeCache ?? true;
    const includeAnalytics = options?.includeAnalytics ?? true;
    const includeValidation = options?.includeValidation ?? false;
    const contentType = options?.contentType || 'static';
    const locale = options?.locale || SITEMAP_CONFIG.defaultLocale;

    console.log(`[Sitemap Admin] Creating debug report`);

    const report: any = {
      system: {
        timestamp: new Date().toISOString(),
        nodeVersion: process.version,
        environment: process.env.NODE_ENV,
        config: {
          baseUrl: SITEMAP_CONFIG.baseUrl,
          locales: SITEMAP_CONFIG.locales,
          defaultLocale: SITEMAP_CONFIG.defaultLocale,
          maxUrlsPerSitemap: SITEMAP_CONFIG.maxUrlsPerSitemap,
          maxSitemapSize: SITEMAP_CONFIG.maxSitemapSize,
          cacheTimeout: SITEMAP_CONFIG.cacheTimeout,
        },
      },
    };

    if (includeCache) {
      report.cache = {
        stats: this.cacheManager.getStats(),
        keys: 'Cache keys not available in current implementation',
      };
    }

    if (includeAnalytics) {
      report.analytics = {
        summary: sitemapAnalytics.getAnalyticsSummary(),
        performance: sitemapAnalytics.getPerformanceMetrics(60),
        coverage: sitemapAnalytics.getContentCoverage(),
        recentErrors: sitemapAnalytics.getRecentErrors(60),
      };
    }

    if (includeValidation) {
      try {
        // Generate a sample sitemap for validation
        const sampleResult = await this.sitemapGenerator.generateContentTypeSitemap(contentType, locale);
        const generator = this.sitemapGenerator.getContentGenerator(contentType);
        const entries = generator ? await generator.generate(locale) : [];
        
        const validationResult = await this.validator.validateSitemap(sampleResult, entries, {
          checkAccessibility: false,
          maxUrlsToCheck: 10,
        });

        report.validation = validationResult;
      } catch (error) {
        report.validation = {
          error: error instanceof Error ? error.message : 'Unknown validation error',
        };
      }
    }

    // Always include a sample generation for debugging
    try {
      const startTime = Date.now();
      const sampleResult = await this.sitemapGenerator.generateContentTypeSitemap(contentType, locale);
      const generationTime = Date.now() - startTime;

      report.sampleGeneration = {
        contentType,
        locale,
        success: true,
        urlCount: sampleResult.urlCount,
        xmlSize: Buffer.byteLength(sampleResult.xml, 'utf8'),
        generationTime,
        isValid: sampleResult.isValid,
        validationErrors: sampleResult.validationErrors,
      };
    } catch (error) {
      report.sampleGeneration = {
        contentType,
        locale,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown generation error',
      };
    }

    return report;
  }

  /**
   * Cleans up old cache entries and analytics data
   */
  async performMaintenance(options?: {
    clearOldCache?: boolean;
    clearOldAnalytics?: boolean;
    cacheMaxAge?: number; // days
    analyticsMaxAge?: number; // days
  }): Promise<{
    cacheCleared: number;
    analyticsCleared: number;
    errors: string[];
  }> {
    const clearOldCache = options?.clearOldCache ?? true;
    const clearOldAnalytics = options?.clearOldAnalytics ?? true;
    const cacheMaxAge = options?.cacheMaxAge ?? 7; // 7 days
    const analyticsMaxAge = options?.analyticsMaxAge ?? 30; // 30 days

    console.log(`[Sitemap Admin] Performing maintenance`);

    const errors: string[] = [];
    let cacheCleared = 0;
    let analyticsCleared = 0;

    // Clear old cache entries
    if (clearOldCache) {
      try {
        cacheCleared = 0; // Cache clearing not implemented with age-based filtering
        console.log(`[Sitemap Admin] Cache clearing with age filter not implemented`);
      } catch (error) {
        const errorMsg = `Failed to clear old cache entries: ${error instanceof Error ? error.message : 'Unknown error'}`;
        errors.push(errorMsg);
        console.error(`[Sitemap Admin] ${errorMsg}`);
      }
    }

    // Clear old analytics data
    if (clearOldAnalytics) {
      try {
        analyticsCleared = sitemapAnalytics.clearOldMetrics(analyticsMaxAge);
        console.log(`[Sitemap Admin] Cleared ${analyticsCleared} old analytics entries`);
      } catch (error) {
        const errorMsg = `Failed to clear old analytics data: ${error instanceof Error ? error.message : 'Unknown error'}`;
        errors.push(errorMsg);
        console.error(`[Sitemap Admin] ${errorMsg}`);
      }
    }

    return {
      cacheCleared,
      analyticsCleared,
      errors,
    };
  }

  // Private helper methods

  private async checkCacheHealth(): Promise<{ status: string; details: any }> {
    try {
      const stats = this.cacheManager.getStats();
      
      let status = 'healthy';
      if (stats.hitRate < 50) status = 'warning';
      if (stats.hitRate < 20) status = 'critical';
      
      return {
        status,
        details: {
          hitRate: stats.hitRate,
          totalRequests: stats.totalRequests,
          cacheHits: stats.cacheHits,
          cacheMisses: stats.cacheMisses,
          averageGenerationTime: stats.averageGenerationTime,
        },
      };
    } catch (error) {
      return {
        status: 'critical',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      };
    }
  }

  private async checkGenerationHealth(): Promise<{ status: string; details: any }> {
    try {
      const performance = sitemapAnalytics.getPerformanceMetrics(60);
      
      let status = 'healthy';
      if (performance.averageTime > 5000) status = 'warning';
      if (performance.averageTime > 10000 || performance.errorRate > 10) status = 'critical';
      
      return {
        status,
        details: {
          averageTime: performance.averageTime,
          errorRate: performance.errorRate,
          slowRequests: performance.slowRequests,
          cacheHitRate: performance.cacheHitRate,
        },
      };
    } catch (error) {
      return {
        status: 'critical',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      };
    }
  }

  private async checkMonitoringHealth(): Promise<{ status: string; details: any }> {
    try {
      const healthCheck = sitemapMonitor.performHealthCheck();
      
      return {
        status: healthCheck.status === 'critical' ? 'critical' : 
               healthCheck.status === 'warning' ? 'warning' : 'healthy',
        details: {
          overallStatus: healthCheck.status,
          errorRate: healthCheck.checks.errorRate,
          performance: healthCheck.checks.performance,
          cacheHitRate: healthCheck.checks.cacheHitRate,
          contentCoverage: healthCheck.checks.contentCoverage,
        },
      };
    } catch (error) {
      return {
        status: 'critical',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      };
    }
  }

  private async checkValidationHealth(): Promise<{ status: string; details: any }> {
    try {
      // Test validation with a simple sitemap
      const testXml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://example.com/</loc>
    <lastmod>2023-01-01T00:00:00Z</lastmod>
  </url>
</urlset>`;

      const validation = this.validator.validateXMLStructure(testXml);
      
      return {
        status: validation.isValid ? 'healthy' : 'warning',
        details: {
          xmlValidation: validation.isValid,
          validatorConfig: this.validator.getConfig(),
        },
      };
    } catch (error) {
      return {
        status: 'critical',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      };
    }
  }

  private generateHealthRecommendations(components: any): string[] {
    const recommendations: string[] = [];

    if (components.cache.status !== 'healthy') {
      recommendations.push('Improve cache performance by optimizing cache hit rates');
    }

    if (components.generation.status !== 'healthy') {
      recommendations.push('Optimize sitemap generation performance and reduce error rates');
    }

    if (components.monitoring.status !== 'healthy') {
      recommendations.push('Review monitoring alerts and address identified issues');
    }

    if (components.validation.status !== 'healthy') {
      recommendations.push('Fix validation issues to ensure sitemap compliance');
    }

    if (recommendations.length === 0) {
      recommendations.push('System is healthy - continue regular monitoring');
    }

    return recommendations;
  }

  private async getContentStatistics(): Promise<{
    totalUrls: number;
    byContentType: Record<string, number>;
    byLocale: Record<string, number>;
    lastUpdated: Date;
  }> {
    const contentTypes: SitemapContentType[] = ['static', 'posts', 'loops'];
    const locales = SITEMAP_CONFIG.locales;
    
    let totalUrls = 0;
    const byContentType: Record<string, number> = {};
    const byLocale: Record<string, number> = {};

    for (const contentType of contentTypes) {
      byContentType[contentType] = 0;
      
      for (const locale of locales) {
        try {
          const generator = this.sitemapGenerator.getContentGenerator(contentType);
          if (generator) {
            const count = await generator.getEstimatedCount();
            byContentType[contentType] += count;
            byLocale[locale] = (byLocale[locale] || 0) + count;
            totalUrls += count;
          }
        } catch (error) {
          console.warn(`[Sitemap Admin] Failed to get count for ${contentType}:${locale}:`, error);
        }
      }
    }

    return {
      totalUrls,
      byContentType,
      byLocale,
      lastUpdated: new Date(),
    };
  }
}