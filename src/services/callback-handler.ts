/**
 * 回调处理服务
 * 处理来自音乐生成提供商的回调通知
 */

import { findMusicGenerationByProviderTaskId, updateMusicGenerationStatus } from "@/models/music-generation";
import { findTrackByGenerationUuid } from "@/models/track";
import { TrackCreationService } from "@/services/track-creation-service";
import { audioFileProcessor } from "@/services/audio-file-processor";
import type { VolcanoCallbackPayload } from "@/app/api/music/callback/volcano/route";

export class CallbackHandler {
  private static instance: CallbackHandler;

  private constructor() {}

  static getInstance(): CallbackHandler {
    if (!CallbackHandler.instance) {
      CallbackHandler.instance = new CallbackHandler();
    }
    return CallbackHandler.instance;
  }

  /**
   * 处理Volcano回调
   */
  async handleVolcanoCallback(payload: VolcanoCallbackPayload): Promise<void> {
    const { TaskID, Status, Progress, SongDetail, FailureReason } = payload;

    console.log(`Processing Volcano callback for task ${TaskID}, status: ${Status}`);

    let processingError: Error | null = null;

    try {
      // 1. 查找对应的音乐生成记录
      const generation = await findMusicGenerationByProviderTaskId(TaskID);
      if (!generation) {
        const error = new Error(`No generation found for provider task ID: ${TaskID}`);
        console.error(error.message);
        throw error;
      }

      console.log(`Found generation: ${generation.uuid} for task ${TaskID}`);

      // 2. 检查是否已经处理过这个回调（幂等性检查）
      if (generation.callback_processed) {
        console.log(`Callback already processed for generation ${generation.uuid}, skipping`);
        return;
      }

      // 3. 映射状态
      const mappedStatus = this.mapVolcanoStatus(Status);
      
      // 4. 检查状态是否需要更新（避免状态倒退）
      if (this.shouldUpdateStatus(generation.status, mappedStatus)) {
        const errorMessage = FailureReason ? `${FailureReason.Code}: ${FailureReason.Msg}` : undefined;
        
        try {
          await updateMusicGenerationStatus(generation.uuid, mappedStatus, errorMessage);
          console.log(`Updated generation ${generation.uuid} status: ${generation.status} -> ${mappedStatus}`);
        } catch (error) {
          console.error(`Failed to update generation status for ${generation.uuid}:`, error);
          processingError = error instanceof Error ? error : new Error(String(error));
        }
      } else {
        console.log(`Status update skipped for generation ${generation.uuid}: ${generation.status} -> ${mappedStatus}`);
      }
      
      // 5. 更新回调接收时间（如果还没有记录）
      if (!generation.callback_received_at) {
        try {
          await this.updateCallbackReceived(generation.uuid);
        } catch (error) {
          console.error(`Failed to update callback received time for ${generation.uuid}:`, error);
          // 不阻塞处理流程
        }
      }

      // 6. 如果完成且有音乐详情，创建track记录
      if (mappedStatus === "completed" && SongDetail) {
        try {
          await this.processCompletedGeneration(generation, SongDetail);
        } catch (error) {
          console.error(`Failed to process completed generation ${generation.uuid}:`, error);
          processingError = error instanceof Error ? error : new Error(String(error));
        }
      }

      // 7. 标记回调已处理（即使有部分错误）
      try {
        await this.markCallbackProcessed(generation.uuid);
      } catch (error) {
        console.error(`Failed to mark callback processed for ${generation.uuid}:`, error);
        // 这个错误比较严重，因为可能导致重复处理
        processingError = error instanceof Error ? error : new Error(String(error));
      }

      // 8. 如果有处理错误，记录但不抛出（避免影响其他回调）
      if (processingError) {
        console.error(`Callback processing completed with errors for task ${TaskID}:`, processingError);
        // 可以考虑发送告警或记录到错误跟踪系统
        await this.recordCallbackError(TaskID, generation?.uuid, processingError);
      } else {
        console.log(`Callback processing completed successfully for task ${TaskID}`);
      }

    } catch (error) {
      console.error(`Critical callback processing failure for task ${TaskID}:`, error);
      
      // 记录关键错误
      await this.recordCallbackError(TaskID, null, error instanceof Error ? error : new Error(String(error)));
      
      // 对于关键错误，我们仍然抛出以便上层处理
      throw error;
    }
  }

  /**
   * 处理完成的音乐生成
   */
  private async processCompletedGeneration(generation: any, songDetail: any): Promise<void> {
    try {
      // 检查是否已经存在track记录（幂等性）
      const existingTrack = await findTrackByGenerationUuid(generation.uuid);
      if (existingTrack) {
        console.log(`Track already exists for generation ${generation.uuid}: ${existingTrack.uuid}`);
        return;
      }

      // 优先使用TOS URL，回退到AudioUrl
      let fileUrl = songDetail.AudioUrl; // 默认使用AudioUrl作为备用
      let filePath: string | undefined;
      let useTOSUrl = false;

      if (songDetail.TosPath) {
        const domain = process.env.VOLCANO_MUSIC_DOMAIN;
        if (domain) {
          try {
            const cleanDomain = domain.replace(/\/$/, '');
            const cleanPath = songDetail.TosPath.replace(/^\//, '');
            const tosUrl = `${cleanDomain}/${cleanPath}`;
            
            // 验证TOS URL格式
            new URL(tosUrl); // 这会抛出错误如果URL无效
            
            fileUrl = tosUrl;
            filePath = songDetail.TosPath;
            useTOSUrl = true;
            
            console.log(`Using TOS URL: ${fileUrl} (path: ${filePath})`);
          } catch (error) {
            console.warn(`Invalid TOS URL construction from path ${songDetail.TosPath}:`, error);
            console.log(`Falling back to AudioUrl: ${songDetail.AudioUrl}`);
          }
        } else {
          console.warn("VOLCANO_MUSIC_DOMAIN not configured, using AudioUrl");
        }
      }

      // 创建track记录，直接包含file_path
      const trackData = {
        generation,
        file_url: fileUrl,
        file_path: filePath, // 传递TOS路径
        file_size: 0, // Volcano API不提供文件大小
        metadata: {
          duration: songDetail.Duration,
          prompt: songDetail.Prompt,
          genre: songDetail.Genre,
          mood: songDetail.Mood,
          theme: songDetail.Theme,
          instrument: songDetail.Instrument,
          tos_path: filePath, // 保存TOS路径到metadata
          storage_type: useTOSUrl ? 'tos' : 'legacy', // 标记存储类型
          original_audio_url: songDetail.AudioUrl, // 保存原始AudioUrl
        },
        is_public: true,
      };

      // Use optimized track creation for Volcano responses if available
      const track = generation.provider === 'volcano' && songDetail.TosPath && songDetail.AudioUrl && songDetail.Duration
        ? await TrackCreationService.createTrackFromVolcanoResponse(generation, songDetail)
        : await TrackCreationService.createTrack(trackData);

      if (track) {
        console.log(`Created track ${track.uuid} for generation ${generation.uuid} (TOS: ${useTOSUrl})`);

        // 如果使用TOS直接访问，更新track记录中的file_path
        if (filePath) {
          const { updateTrack } = await import("@/models/track");
          await updateTrack(track.uuid, { file_path: filePath });
          console.log(`Updated track ${track.uuid} with TOS path: ${filePath}`);
        }

        // 对于TOS文件，跳过额外的文件处理（因为文件已经在TOS中）
        if (!useTOSUrl) {
          // 只有非TOS文件才需要额外处理
          setImmediate(async () => {
            try {
              const result = await audioFileProcessor.processAudioFile(
                track.uuid,
                fileUrl,
                0
              );

              if (result.success) {
                console.log(`Audio file processed for track ${track.uuid}`);
              } else {
                console.warn(`Audio file processing failed for track ${track.uuid}:`, result.error);
              }
            } catch (error) {
              console.error(`Audio file processing error for track ${track.uuid}:`, error);
            }
          });
        } else {
          console.log(`Skipping audio file processing for TOS track ${track.uuid}`);
        }
      }

    } catch (error) {
      console.error(`Failed to process completed generation ${generation.uuid}:`, error);
      throw error;
    }
  }

  /**
   * 更新回调接收时间
   */
  private async updateCallbackReceived(generationUuid: string): Promise<void> {
    try {
      const { updateMusicGenerationCallbackReceived } = await import("@/models/music-generation");
      await updateMusicGenerationCallbackReceived(generationUuid);
    } catch (error) {
      console.error(`Failed to update callback received time for ${generationUuid}:`, error);
      // 不抛出错误，这不是关键操作
    }
  }

  /**
   * 标记回调已处理
   */
  private async markCallbackProcessed(generationUuid: string): Promise<void> {
    try {
      const { markMusicGenerationCallbackProcessed } = await import("@/models/music-generation");
      await markMusicGenerationCallbackProcessed(generationUuid);
    } catch (error) {
      console.error(`Failed to mark callback processed for ${generationUuid}:`, error);
      // 不抛出错误，这不是关键操作
    }
  }

  /**
   * 映射Volcano状态到标准状态
   */
  private mapVolcanoStatus(volcanoStatus: number): "pending" | "processing" | "completed" | "failed" {
    switch (volcanoStatus) {
      case 0:
        return "pending";
      case 1:
        return "processing";
      case 2:
        return "completed";
      case 3:
        return "failed";
      default:
        return "pending";
    }
  }

  /**
   * 检查是否应该更新状态（避免状态倒退）
   */
  private shouldUpdateStatus(currentStatus: string, newStatus: string): boolean {
    // 定义状态优先级
    const statusPriority = {
      "pending": 1,
      "processing": 2,
      "completed": 4,
      "failed": 3,
    };

    const currentPriority = statusPriority[currentStatus as keyof typeof statusPriority] || 0;
    const newPriority = statusPriority[newStatus as keyof typeof statusPriority] || 0;

    // 只有当新状态优先级更高时才更新
    // 或者当前状态是processing且新状态也是processing时（更新进度）
    return newPriority > currentPriority || 
           (currentStatus === "processing" && newStatus === "processing");
  }

  /**
   * 验证回调签名（如果Volcano提供签名机制）
   */
  private validateSignature(payload: any, signature?: string): boolean {
    // TODO: 实现签名验证逻辑
    // 目前返回true，后续可以根据Volcano的签名机制实现
    return true;
  }

  /**
   * 记录回调错误
   */
  private async recordCallbackError(taskId: string, generationUuid: string | null, error: Error): Promise<void> {
    try {
      // 记录错误到日志
      console.error(`Callback Error Record:`, {
        taskId,
        generationUuid,
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      });

      // TODO: 可以扩展为发送到错误跟踪服务
      // 例如：Sentry, LogRocket, 或自定义错误收集系统
      
      // TODO: 可以扩展为发送告警
      // 例如：Slack, 邮件, 或其他通知系统
      
    } catch (recordError) {
      console.error(`Failed to record callback error:`, recordError);
      // 避免错误记录失败影响主流程
    }
  }

  /**
   * 检查回调健康状态
   */
  async checkCallbackHealth(): Promise<{
    isHealthy: boolean;
    issues: string[];
    stats: {
      recentCallbacks: number;
      recentErrors: number;
      avgProcessingTime: number;
    };
  }> {
    const issues: string[] = [];
    
    try {
      // 检查配置
      const callbackSecret = process.env.VOLCANO_CALLBACK_SECRET;
      const baseUrl = process.env.NEXT_PUBLIC_WEB_URL;
      
      if (!callbackSecret) {
        issues.push("VOLCANO_CALLBACK_SECRET not configured");
      }
      
      if (!baseUrl) {
        issues.push("NEXT_PUBLIC_WEB_URL not configured");
      }

      // 检查最近的回调处理情况
      // TODO: 实现基于数据库的统计
      const stats = {
        recentCallbacks: 0,
        recentErrors: 0,
        avgProcessingTime: 0,
      };

      return {
        isHealthy: issues.length === 0,
        issues,
        stats,
      };
      
    } catch (error) {
      console.error("Failed to check callback health:", error);
      return {
        isHealthy: false,
        issues: ["Health check failed"],
        stats: {
          recentCallbacks: 0,
          recentErrors: 0,
          avgProcessingTime: 0,
        },
      };
    }
  }

  /**
   * 重试失败的回调处理
   */
  async retryFailedCallback(taskId: string): Promise<boolean> {
    try {
      console.log(`Attempting to retry callback processing for task: ${taskId}`);
      
      // 查找对应的生成记录
      const generation = await findMusicGenerationByProviderTaskId(taskId);
      if (!generation) {
        console.error(`No generation found for retry task ID: ${taskId}`);
        return false;
      }

      // 重置回调处理状态
      const { markMusicGenerationCallbackProcessed } = await import("@/models/music-generation");
      await markMusicGenerationCallbackProcessed(generation.uuid);
      
      console.log(`Reset callback processing status for generation: ${generation.uuid}`);
      
      // 触发状态更新器重新处理
      const { musicStatusUpdater } = await import("./music-status-updater");
      await musicStatusUpdater.triggerCheck();
      
      return true;
      
    } catch (error) {
      console.error(`Failed to retry callback for task ${taskId}:`, error);
      return false;
    }
  }

  /**
   * 获取处理统计信息
   */
  getStats(): {
    totalCallbacksProcessed: number;
    lastProcessedAt?: Date;
    healthStatus: string;
  } {
    // TODO: 实现基于数据库的统计逻辑
    return {
      totalCallbacksProcessed: 0,
      healthStatus: "unknown",
    };
  }
}

// 导出单例实例
export const callbackHandler = CallbackHandler.getInstance();