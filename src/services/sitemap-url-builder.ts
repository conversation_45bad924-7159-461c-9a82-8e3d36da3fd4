import type { AlternateLink, URLBuilderConfig } from "@/types/sitemap";
import { SITEMAP_CONFIG } from "@/constants/sitemap";
import { locales, defaultLocale, localePrefix } from "@/i18n/locale";

/**
 * URL Builder Service for Sitemap Generation
 * Handles locale-aware URL generation with proper prefixes and hreflang support
 */
export class URLBuilder {
  private config: URLBuilderConfig;

  constructor(config?: Partial<URLBuilderConfig>) {
    this.config = {
      baseUrl: SITEMAP_CONFIG.baseUrl,
      locales: locales,
      defaultLocale: defaultLocale,
      localePrefix: localePrefix as 'always' | 'as-needed' | 'never',
      ...config,
    };
  }

  /**
   * Builds a complete URL for a given locale and path
   * @param locale - The locale code (e.g., 'en', 'zh')
   * @param path - The path without leading slash (e.g., 'generate', 'posts/my-post')
   * @returns Complete URL with proper locale prefix
   */
  buildUrl(locale: string, path: string = ""): string {
    const { baseUrl, defaultLocale, localePrefix } = this.config;
    
    // Clean the path - remove leading slash if present
    const cleanPath = path.startsWith("/") ? path.slice(1) : path;
    
    // Determine if we need locale prefix
    const needsLocalePrefix = this.shouldIncludeLocalePrefix(locale);
    
    // Build the URL parts
    const urlParts = [baseUrl.replace(/\/$/, "")]; // Remove trailing slash from baseUrl
    
    if (needsLocalePrefix) {
      urlParts.push(locale);
    }
    
    if (cleanPath) {
      urlParts.push(cleanPath);
    }
    
    return urlParts.join("/");
  }

  /**
   * Determines if locale prefix should be included in URL
   * @param locale - The locale to check
   * @returns True if locale prefix should be included
   */
  private shouldIncludeLocalePrefix(locale: string): boolean {
    const { defaultLocale, localePrefix } = this.config;
    
    switch (localePrefix) {
      case 'always':
        return true;
      case 'never':
        return false;
      case 'as-needed':
      default:
        return locale !== defaultLocale;
    }
  }

  /**
   * Builds alternate language links for hreflang support
   * @param path - The path without locale prefix
   * @returns Array of alternate links for all supported locales
   */
  buildAlternateLinks(path: string = ""): AlternateLink[] {
    const { locales } = this.config;
    
    return locales.map(locale => ({
      href: this.buildUrl(locale, path),
      hreflang: locale,
    }));
  }

  /**
   * Builds URLs for all locales for a given path
   * @param path - The path without locale prefix
   * @returns Object with locale as key and URL as value
   */
  buildAllLocaleUrls(path: string = ""): Record<string, string> {
    const { locales } = this.config;
    const urls: Record<string, string> = {};
    
    locales.forEach(locale => {
      urls[locale] = this.buildUrl(locale, path);
    });
    
    return urls;
  }

  /**
   * Extracts locale from a full URL
   * @param url - The full URL to analyze
   * @returns The locale code or default locale if not found
   */
  extractLocaleFromUrl(url: string): string {
    const { baseUrl, locales, defaultLocale } = this.config;
    
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      
      // Remove base path if URL includes full domain
      const relativePath = url.startsWith(baseUrl) 
        ? pathname 
        : pathname;
      
      // Check if path starts with a locale
      const pathSegments = relativePath.split('/').filter(Boolean);
      const firstSegment = pathSegments[0];
      
      if (firstSegment && locales.includes(firstSegment)) {
        return firstSegment;
      }
      
      return defaultLocale;
    } catch {
      return defaultLocale;
    }
  }

  /**
   * Extracts path without locale prefix from a URL
   * @param url - The full URL to analyze
   * @returns The path without locale prefix
   */
  extractPathFromUrl(url: string): string {
    const { baseUrl, locales } = this.config;
    
    try {
      const urlObj = new URL(url);
      let pathname = urlObj.pathname;
      
      // Remove base path if URL includes full domain
      if (url.startsWith(baseUrl)) {
        const basePath = new URL(baseUrl).pathname;
        if (pathname.startsWith(basePath)) {
          pathname = pathname.slice(basePath.length);
        }
      }
      
      // Remove leading slash
      pathname = pathname.startsWith('/') ? pathname.slice(1) : pathname;
      
      // Check if path starts with a locale and remove it
      const pathSegments = pathname.split('/');
      const firstSegment = pathSegments[0];
      
      if (firstSegment && locales.includes(firstSegment)) {
        return pathSegments.slice(1).join('/');
      }
      
      return pathname;
    } catch {
      return '';
    }
  }

  /**
   * Validates if a URL belongs to this site
   * @param url - The URL to validate
   * @returns True if URL is valid and belongs to this site
   */
  isValidSiteUrl(url: string): boolean {
    const { baseUrl } = this.config;
    
    try {
      const urlObj = new URL(url);
      const baseUrlObj = new URL(baseUrl);
      
      return urlObj.origin === baseUrlObj.origin;
    } catch {
      return false;
    }
  }

  /**
   * Normalizes a URL by ensuring proper format and removing duplicates
   * @param url - The URL to normalize
   * @returns Normalized URL
   */
  normalizeUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      
      // Remove trailing slash except for root
      if (urlObj.pathname !== '/' && urlObj.pathname.endsWith('/')) {
        urlObj.pathname = urlObj.pathname.slice(0, -1);
      }
      
      // Remove default port numbers
      if ((urlObj.protocol === 'https:' && urlObj.port === '443') ||
          (urlObj.protocol === 'http:' && urlObj.port === '80')) {
        urlObj.port = '';
      }
      
      return urlObj.toString();
    } catch {
      return url; // Return original if parsing fails
    }
  }

  /**
   * Gets the base URL configuration
   * @returns The configured base URL
   */
  getBaseUrl(): string {
    return this.config.baseUrl;
  }

  /**
   * Gets all supported locales
   * @returns Array of supported locale codes
   */
  getSupportedLocales(): string[] {
    return [...this.config.locales];
  }

  /**
   * Gets the default locale
   * @returns The default locale code
   */
  getDefaultLocale(): string {
    return this.config.defaultLocale;
  }

  /**
   * Checks if a locale is supported
   * @param locale - The locale to check
   * @returns True if locale is supported
   */
  isSupportedLocale(locale: string): boolean {
    return this.config.locales.includes(locale);
  }
}