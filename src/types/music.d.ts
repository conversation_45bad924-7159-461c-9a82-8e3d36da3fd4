// Import types from constants
import type { MusicGenre, MusicMood, MusicInstrument, MusicTheme } from "@/constants/music-api";

// Music Generation Types
export interface MusicGeneration {
  id?: number;
  uuid: string;
  user_uuid: string;
  prompt: string;
  style?: string;
  mood?: string;
  bpm?: number;
  duration: number; // 15, 30, or 60 seconds
  // New fields for API compatibility
  genre?: string[]; // Array of genre strings
  instrument?: string[]; // Array of instrument strings
  theme?: string[]; // Array of theme strings
  provider: string; // mubert, suno, etc.
  provider_task_id?: string;
  status: MusicGenerationStatus;
  error_message?: string;
  created_at?: string | Date;
  updated_at?: string | Date;
  completed_at?: string | Date;
  // Callback tracking fields
  callback_received_at?: string | Date;
  callback_processed?: boolean;
}

export type MusicGenerationStatus = 
  | "pending" 
  | "processing" 
  | "completed" 
  | "failed";

export interface MusicGenerationRequest {
  prompt: string;
  style?: string;
  mood?: string;
  bpm?: number;
  duration: 15 | 30 | 60;
  // New fields for API compatibility
  genre?: string[]; // Array of genre strings
  instrument?: string[]; // Array of instrument strings
  theme?: string[]; // Array of theme strings
  provider?: string;
}

// Track Types
export interface Track {
  id?: number;
  uuid: string;
  generation_uuid: string;
  user_uuid: string;
  title?: string;
  slug?: string;
  prompt: string;
  style?: string;
  mood?: string;
  bpm?: number;
  duration: number;
  // New fields for API compatibility
  genre?: string[]; // Array of genre strings
  instrument?: string[]; // Array of instrument strings
  theme?: string[]; // Array of theme strings
  key_signature?: string;
  file_url: string;
  file_path?: string; // TOS path for direct access (e.g., "music_ai/v02e70g10004d1tfrgnog65j3h9ndol0.wav")
  file_size?: number;
  file_format: "mp3" | "wav";
  waveform_data?: WaveformData;
  metadata?: TrackMetadata;
  download_count: number;
  is_public: boolean;
  created_at?: string | Date;
  updated_at?: string | Date;
  loop_verification?: {
    is_seamless: boolean;
    verification_score: string;
    verification_method?: string;
    verified_at?: string;
    start_analysis?: any;
    end_analysis?: any;
  } | null;
}

export interface WaveformData {
  peaks: number[];
  duration: number;
  sample_rate: number;
}

export interface TrackMetadata {
  tempo?: number;
  key?: string;
  genre?: string;
  instruments?: string[];
  energy_level?: number;
  danceability?: number;
  valence?: number;
}

// Loop Verification Types
export interface LoopVerification {
  id?: number;
  track_uuid: string;
  verification_score?: number; // 0.00 - 1.00
  is_seamless: boolean;
  start_analysis?: AudioAnalysis;
  end_analysis?: AudioAnalysis;
  verification_method: "ml" | "signal_processing";
  created_at?: string | Date;
}

export interface AudioAnalysis {
  frequency_spectrum: number[];
  amplitude: number;
  phase: number;
  spectral_centroid: number;
  zero_crossing_rate: number;
}

// Track Variation Types
export interface TrackVariation {
  id?: number;
  uuid: string;
  original_track_uuid: string;
  user_uuid: string;
  variation_type: VariationType;
  variation_params?: VariationParams;
  file_url: string;
  file_size?: number;
  file_format: "mp3" | "wav";
  created_at?: string | Date;
}

export type VariationType = "tempo" | "style" | "mood" | "key" | "arrangement";

export interface VariationParams {
  tempo_change?: number; // percentage change
  style_target?: string;
  mood_target?: string;
  key_target?: string;
  arrangement_type?: string;
}

// Track Stems Types
export interface TrackStem {
  id?: number;
  uuid: string;
  track_uuid: string;
  stem_type: StemType;
  file_url: string;
  file_size?: number;
  file_format: "wav" | "mp3";
  created_at?: string | Date;
}

export type StemType = 
  | "drums" 
  | "bass" 
  | "melody" 
  | "harmony" 
  | "vocals" 
  | "percussion"
  | "lead"
  | "pad";

// Collection Types
export interface UserTrackCollection {
  id?: number;
  uuid: string;
  user_uuid: string;
  name: string;
  description?: string;
  is_public: boolean;
  created_at?: string | Date;
  updated_at?: string | Date;
  tracks?: Track[];
  track_count?: number;
}

export interface CollectionTrack {
  id?: number;
  collection_uuid: string;
  track_uuid: string;
  added_at?: string | Date;
}

// API Response Types
export interface MusicGenerationResponse {
  generation: MusicGeneration;
  estimated_completion_time?: number; // seconds
}

export interface TrackResponse {
  track: Track;
  variations?: TrackVariation[];
  stems?: TrackStem[];
  loop_verification?: LoopVerification;
}

export interface TrackListResponse {
  tracks: Track[];
  total: number;
  page: number;
  per_page: number;
  has_more: boolean;
}

// Music Provider Types
export interface MusicProvider {
  name: string;
  display_name: string;
  supported_durations: number[];
  supported_styles: string[];
  max_bpm: number;
  min_bpm: number;
  supports_stems: boolean;
  supports_variations: boolean;
}

export interface MusicProviderConfig {
  api_key: string;
  base_url?: string;
  timeout?: number;
  max_retries?: number;
}

// Music Generation Options
export interface MusicGenerationOptions {
  provider: string;
  model?: string;
  quality?: "standard" | "high" | "premium";
  seed?: number;
  temperature?: number;
  top_p?: number;
  guidance_scale?: number;
  style?: string;
  mood?: string;
  bpm?: number;
  // New fields for API compatibility
  genre?: string[]; // Array of genre strings
  instrument?: string[]; // Array of instrument strings
  theme?: string[]; // Array of theme strings
  negative_prompt?: string; // For better loop generation
}

// Search and Filter Types
export interface TrackSearchParams {
  query?: string;
  style?: string;
  mood?: string;
  bpm_min?: number;
  bpm_max?: number;
  duration?: number;
  user_uuid?: string;
  is_public?: boolean;
  sort_by?: "created_at" | "download_count" | "title" | "bpm";
  sort_order?: "asc" | "desc";
  page?: number;
  per_page?: number;
}

export interface TrackFilters {
  styles: string[];
  moods: string[];
  durations: number[];
  bpm_ranges: { min: number; max: number; label: string }[];
}
