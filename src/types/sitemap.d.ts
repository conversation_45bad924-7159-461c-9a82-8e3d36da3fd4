// Sitemap Core Types

export interface SitemapEntry {
  url: string;
  lastmod: string;
  changefreq: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority: string;
  alternates?: AlternateLink[];
}

export interface AlternateLink {
  href: string;
  hreflang: string;
}

export interface SitemapConfig {
  maxUrlsPerSitemap: number;
  maxSitemapSize: number;
  baseUrl: string;
  locales: string[];
  defaultLocale: string;
  cacheTimeout: number;
  enableSitemapIndex: boolean;
  enableHreflang: boolean;
  priorities: {
    static: number;
    posts: number;
    loops: number;
  };
  changeFreq: {
    static: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
    posts: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
    loops: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  };
}

// Content Generator Interface
export interface ContentGenerator {
  generate(locale: string, limit?: number): Promise<SitemapEntry[]>;
  getLastModified(): Promise<Date>;
  getEstimatedCount(): Promise<number>;
}

// Sitemap Index Types
export interface SitemapIndexEntry {
  loc: string;
  lastmod: string;
}

export interface SitemapIndex {
  sitemaps: SitemapIndexEntry[];
}

// Cache Types
export interface SitemapCache {
  key: string;
  content: string;
  lastModified: Date;
  expiresAt: Date;
  contentType: 'index' | 'static' | 'posts' | 'loops';
  locale?: string;
}

export interface SitemapCacheStats {
  hitRate: number;
  totalRequests: number;
  cacheHits: number;
  cacheMisses: number;
  averageGenerationTime: number;
}

// Statistics and Monitoring Types
export interface SitemapStats {
  totalUrls: number;
  urlsByType: {
    static: number;
    posts: number;
    loops: number;
  };
  urlsByLocale: Record<string, number>;
  lastGenerated: Date;
  generationTime: number;
  cacheHitRate: number;
}

export interface SitemapGenerationMetrics {
  contentType: string;
  locale: string;
  urlCount: number;
  generationTime: number;
  cacheHit: boolean;
  errors: string[];
}

// Static Page Configuration
export interface StaticPageConfig {
  path: string;
  priority: string;
  changefreq: SitemapEntry['changefreq'];
  includeInLocales?: string[];
  excludeFromLocales?: string[];
}

// Content Type Definitions
export type SitemapContentType = 'static' | 'posts' | 'loops' | 'index';

export interface ContentTypeConfig {
  type: SitemapContentType;
  generator: ContentGenerator;
  priority: number;
  changefreq: SitemapEntry['changefreq'];
  cacheTimeout: number;
}

// URL Builder Types
export interface URLBuilderConfig {
  baseUrl: string;
  locales: string[];
  defaultLocale: string;
  localePrefix: 'always' | 'as-needed' | 'never';
}

// Error Types
export class SitemapError extends Error {
  constructor(
    message: string,
    public code: string,
    public context?: Record<string, any>
  ) {
    super(message);
    this.name = 'SitemapError';
  }
}

export class SitemapGenerationError extends SitemapError {
  constructor(message: string, public contentType: string, context?: Record<string, any>) {
    super(message, 'GENERATION_ERROR', { contentType, ...context });
    this.name = 'SitemapGenerationError';
  }
}

export class SitemapCacheError extends SitemapError {
  constructor(message: string, public operation: string, context?: Record<string, any>) {
    super(message, 'CACHE_ERROR', { operation, ...context });
    this.name = 'SitemapCacheError';
  }
}

export class SitemapValidationError extends SitemapError {
  constructor(message: string, public validationType: string, context?: Record<string, any>) {
    super(message, 'VALIDATION_ERROR', { validationType, ...context });
    this.name = 'SitemapValidationError';
  }
}

// XML Generation Types
export interface XMLGenerationOptions {
  includeHreflang: boolean;
  prettyPrint: boolean;
  validateXML: boolean;
}

export interface SitemapXMLResult {
  xml: string;
  urlCount: number;
  generationTime: number;
  isValid: boolean;
  validationErrors?: string[];
}

// Database Query Types for Sitemap Generation
export interface SitemapQueryOptions {
  limit?: number;
  offset?: number;
  locale?: string;
  includePrivate?: boolean;
  sortBy?: 'created_at' | 'updated_at' | 'slug';
  sortOrder?: 'asc' | 'desc';
}

// Health Check Types
export interface SitemapHealthCheck {
  status: 'healthy' | 'degraded' | 'unhealthy';
  checks: {
    database: boolean;
    cache: boolean;
    xmlGeneration: boolean;
    urlValidation: boolean;
  };
  metrics: {
    averageResponseTime: number;
    errorRate: number;
    cacheHitRate: number;
  };
  lastChecked: Date;
}

// Administration Types
export interface SitemapAdminAction {
  action: 'regenerate' | 'clear_cache' | 'warm_cache' | 'validate';
  contentType?: SitemapContentType;
  locale?: string;
  force?: boolean;
}

export interface SitemapAdminResult {
  success: boolean;
  message: string;
  data?: any;
  executionTime: number;
  timestamp: Date;
}