import { SITEMAP_CONFIG } from "@/constants/sitemap";

/**
 * Configuration validation result
 */
export interface ConfigValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Validates sitemap configuration
 */
export function validateSitemapConfig(): ConfigValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate base URL
  if (!SITEMAP_CONFIG.baseUrl) {
    errors.push("Base URL is required");
  } else {
    try {
      new URL(SITEMAP_CONFIG.baseUrl);
    } catch {
      errors.push("Base URL is not a valid URL");
    }
  }

  // Validate locales
  if (!SITEMAP_CONFIG.locales || SITEMAP_CONFIG.locales.length === 0) {
    errors.push("At least one locale is required");
  }

  if (!SITEMAP_CONFIG.defaultLocale) {
    errors.push("Default locale is required");
  } else if (!SITEMAP_CONFIG.locales.includes(SITEMAP_CONFIG.defaultLocale)) {
    errors.push("Default locale must be included in locales array");
  }

  // Validate limits
  if (SITEMAP_CONFIG.maxUrlsPerSitemap <= 0) {
    errors.push("Max URLs per sitemap must be greater than 0");
  }

  if (SITEMAP_CONFIG.maxUrlsPerSitemap > 50000) {
    warnings.push("Max URLs per sitemap exceeds recommended limit of 50,000");
  }

  if (SITEMAP_CONFIG.maxSitemapSize <= 0) {
    errors.push("Max sitemap size must be greater than 0");
  }

  if (SITEMAP_CONFIG.maxSitemapSize > 50 * 1024 * 1024) {
    warnings.push("Max sitemap size exceeds recommended limit of 50MB");
  }

  // Validate cache timeout
  if (SITEMAP_CONFIG.cacheTimeout <= 0) {
    warnings.push("Cache timeout should be greater than 0 for better performance");
  }

  // Validate priorities
  const priorities = [
    SITEMAP_CONFIG.priorities.static,
    SITEMAP_CONFIG.priorities.posts,
    SITEMAP_CONFIG.priorities.loops,
  ];

  for (const priority of priorities) {
    if (priority < 0 || priority > 1) {
      errors.push(`Priority values must be between 0 and 1, got ${priority}`);
    }
  }

  // Validate change frequencies
  const validFrequencies = ['always', 'hourly', 'daily', 'weekly', 'monthly', 'yearly', 'never'];
  const frequencies = [
    SITEMAP_CONFIG.changeFreq.static,
    SITEMAP_CONFIG.changeFreq.posts,
    SITEMAP_CONFIG.changeFreq.loops,
  ];

  for (const freq of frequencies) {
    if (!validFrequencies.includes(freq)) {
      errors.push(`Invalid change frequency: ${freq}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Validates environment-specific configuration
 */
export function validateEnvironmentConfig(): ConfigValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check if running in production
  const isProduction = process.env.NODE_ENV === 'production';
  
  if (isProduction) {
    // Production-specific validations
    if (SITEMAP_CONFIG.baseUrl.includes('localhost')) {
      errors.push("Production environment should not use localhost URLs");
    }

    if (!SITEMAP_CONFIG.baseUrl.startsWith('https://')) {
      warnings.push("Production sitemaps should use HTTPS URLs");
    }
  } else {
    // Development-specific warnings
    if (!SITEMAP_CONFIG.baseUrl.includes('localhost') && !SITEMAP_CONFIG.baseUrl.includes('127.0.0.1')) {
      warnings.push("Development environment typically uses localhost URLs");
    }
  }

  // Check database connection (if available)
  if (typeof process !== 'undefined' && process.env) {
    if (!process.env.DATABASE_URL && !process.env.POSTGRES_URL) {
      warnings.push("Database URL not configured - sitemap generation may fail");
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Runs all configuration validations
 */
export function validateAllConfigurations(): ConfigValidationResult {
  const configResult = validateSitemapConfig();
  const envResult = validateEnvironmentConfig();

  return {
    isValid: configResult.isValid && envResult.isValid,
    errors: [...configResult.errors, ...envResult.errors],
    warnings: [...configResult.warnings, ...envResult.warnings],
  };
}

/**
 * Logs configuration validation results
 */
export function logConfigValidation(): void {
  const result = validateAllConfigurations();

  if (result.isValid) {
    console.log("✅ Sitemap configuration is valid");
  } else {
    console.error("❌ Sitemap configuration has errors:");
    result.errors.forEach(error => console.error(`  - ${error}`));
  }

  if (result.warnings.length > 0) {
    console.warn("⚠️ Sitemap configuration warnings:");
    result.warnings.forEach(warning => console.warn(`  - ${warning}`));
  }
}

/**
 * Validates configuration on module load in development
 */
if (process.env.NODE_ENV === 'development') {
  // Run validation on import in development
  setTimeout(() => {
    logConfigValidation();
  }, 100);
}