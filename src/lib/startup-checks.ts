/**
 * 应用启动检查
 * 在应用启动时验证关键配置和依赖
 */

import { validateTOSConfig, logConfigStatus } from "./config-validator";

/**
 * 执行所有启动检查
 */
export async function runStartupChecks(): Promise<boolean> {
  console.log("🚀 Running startup checks...");
  
  let allChecksPass = true;

  // 1. 验证TOS配置
  try {
    console.log("📋 Checking TOS configuration...");
    const tosValidation = validateTOSConfig();
    
    if (!tosValidation.isValid) {
      console.error("❌ TOS configuration validation failed");
      tosValidation.errors.forEach(error => console.error(`   ${error}`));
      allChecksPass = false;
    } else {
      console.log("✅ TOS configuration is valid");
    }

    if (tosValidation.warnings.length > 0) {
      console.warn("⚠️  TOS configuration warnings:");
      tosValidation.warnings.forEach(warning => console.warn(`   ${warning}`));
    }

    // 记录详细配置状态
    logConfigStatus();
    
  } catch (error) {
    console.error("❌ Failed to validate TOS configuration:", error);
    allChecksPass = false;
  }

  // 2. 检查数据库连接
  try {
    console.log("🗄️  Checking database connection...");
    const dbUrl = process.env.DATABASE_URL;
    
    if (!dbUrl) {
      console.error("❌ DATABASE_URL not configured");
      allChecksPass = false;
    } else {
      console.log("✅ Database URL is configured");
    }
  } catch (error) {
    console.error("❌ Database check failed:", error);
    allChecksPass = false;
  }

  // 3. 检查认证配置
  try {
    console.log("🔐 Checking authentication configuration...");
    const authSecret = process.env.AUTH_SECRET;
    const authUrl = process.env.AUTH_URL;
    
    if (!authSecret) {
      console.error("❌ AUTH_SECRET not configured");
      allChecksPass = false;
    }
    
    if (!authUrl) {
      console.error("❌ AUTH_URL not configured");
      allChecksPass = false;
    }
    
    if (authSecret && authUrl) {
      console.log("✅ Authentication configuration is valid");
    }
  } catch (error) {
    console.error("❌ Authentication check failed:", error);
    allChecksPass = false;
  }

  // 4. 检查存储配置
  try {
    console.log("💾 Checking storage configuration...");
    const storageEndpoint = process.env.STORAGE_ENDPOINT;
    const storageBucket = process.env.STORAGE_BUCKET;
    
    if (!storageEndpoint || !storageBucket) {
      console.warn("⚠️  Storage configuration incomplete - some features may not work");
    } else {
      console.log("✅ Storage configuration is valid");
    }
  } catch (error) {
    console.error("❌ Storage check failed:", error);
    // Storage不是关键配置，不影响启动
  }

  // 5. 检查音乐提供商配置
  try {
    console.log("🎵 Checking music provider configuration...");
    const hasVolcano = !!(process.env.VOLCANO_ACCESS_KEY_ID && process.env.VOLCANO_SECRET_ACCESS_KEY);
    const hasMubert = !!process.env.MUBERT_API_KEY;
    const hasSuno = !!process.env.SUNO_API_KEY;
    
    if (!hasVolcano && !hasMubert && !hasSuno) {
      console.error("❌ No music providers configured");
      allChecksPass = false;
    } else {
      const providers = [];
      if (hasVolcano) providers.push("Volcano");
      if (hasMubert) providers.push("Mubert");
      if (hasSuno) providers.push("Suno");
      
      console.log(`✅ Music providers configured: ${providers.join(", ")}`);
    }
  } catch (error) {
    console.error("❌ Music provider check failed:", error);
    allChecksPass = false;
  }

  // 总结
  if (allChecksPass) {
    console.log("🎉 All startup checks passed!");
  } else {
    console.error("💥 Some startup checks failed - application may not function properly");
  }

  return allChecksPass;
}

/**
 * 检查关键环境变量
 */
export function checkCriticalEnvVars(): string[] {
  const missing: string[] = [];
  
  const critical = [
    'DATABASE_URL',
    'AUTH_SECRET',
    'NEXT_PUBLIC_WEB_URL',
  ];

  critical.forEach(varName => {
    if (!process.env[varName]) {
      missing.push(varName);
    }
  });

  return missing;
}

/**
 * 检查TOS相关环境变量
 */
export function checkTOSEnvVars(): {
  missing: string[];
  warnings: string[];
} {
  const missing: string[] = [];
  const warnings: string[] = [];
  
  const required = [
    'VOLCANO_ACCESS_KEY_ID',
    'VOLCANO_SECRET_ACCESS_KEY',
    'VOLCANO_MUSIC_DOMAIN',
    'VOLCANO_MUSIC_BUCKET',
  ];

  const optional = [
    'VOLCANO_CALLBACK_SECRET',
    'VOLCANO_CALLBACK_ENABLED',
  ];

  required.forEach(varName => {
    if (!process.env[varName]) {
      missing.push(varName);
    }
  });

  optional.forEach(varName => {
    if (!process.env[varName]) {
      warnings.push(`${varName} not set - related features may be disabled`);
    }
  });

  return { missing, warnings };
}

/**
 * 生成配置报告
 */
export function generateConfigReport(): {
  critical: { name: string; value: string; status: 'ok' | 'missing' | 'invalid' }[];
  tos: { name: string; value: string; status: 'ok' | 'missing' | 'invalid' }[];
  optional: { name: string; value: string; status: 'ok' | 'missing' }[];
} {
  const report = {
    critical: [] as { name: string; value: string; status: 'ok' | 'missing' | 'invalid' }[],
    tos: [] as { name: string; value: string; status: 'ok' | 'missing' | 'invalid' }[],
    optional: [] as { name: string; value: string; status: 'ok' | 'missing' }[],
  };

  // 关键配置
  const critical = ['DATABASE_URL', 'AUTH_SECRET', 'NEXT_PUBLIC_WEB_URL'];
  critical.forEach(name => {
    const value = process.env[name];
    report.critical.push({
      name,
      value: value ? '***configured***' : '',
      status: value ? 'ok' : 'missing',
    });
  });

  // TOS配置
  const tos = [
    'VOLCANO_ACCESS_KEY_ID',
    'VOLCANO_SECRET_ACCESS_KEY', 
    'VOLCANO_MUSIC_DOMAIN',
    'VOLCANO_MUSIC_BUCKET'
  ];
  tos.forEach(name => {
    const value = process.env[name];
    report.tos.push({
      name,
      value: value ? (name.includes('SECRET') ? '***configured***' : value) : '',
      status: value ? 'ok' : 'missing',
    });
  });

  // 可选配置
  const optional = ['VOLCANO_CALLBACK_SECRET', 'VOLCANO_CALLBACK_ENABLED'];
  optional.forEach(name => {
    const value = process.env[name];
    report.optional.push({
      name,
      value: value ? (name.includes('SECRET') ? '***configured***' : value) : '',
      status: value ? 'ok' : 'missing',
    });
  });

  return report;
}