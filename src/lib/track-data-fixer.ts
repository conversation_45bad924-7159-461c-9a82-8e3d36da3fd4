/**
 * Track数据修复工具
 * 修复现有track记录中的数据问题
 */

export interface TrackFixResult {
  success: boolean;
  fixed_fields: string[];
  errors: string[];
}

export class TrackDataFixer {
  
  /**
   * 修复单个track的数据问题
   */
  static async fixTrackData(trackUuid: string): Promise<TrackFixResult> {
    const result: TrackFixResult = {
      success: false,
      fixed_fields: [],
      errors: [],
    };

    try {
      // TODO: 实际的数据库操作需要在这里实现
      // 这里提供修复逻辑的框架
      
      console.log(`Fixing track data for: ${trackUuid}`);
      
      // 1. 修复文件格式问题
      // 如果file_url是.wav但file_format是mp3，修复它
      const fileFormatFixed = await this.fixFileFormat(trackUuid);
      if (fileFormatFixed) {
        result.fixed_fields.push('file_format');
      }

      // 2. 修复缺失的style/mood字段
      // 尝试从metadata或其他来源推断
      const styleMoodFixed = await this.fixStyleMoodFromMetadata(trackUuid);
      if (styleMoodFixed.length > 0) {
        result.fixed_fields.push(...styleMoodFixed);
      }

      // 3. 修复original_file_url
      const originalUrlFixed = await this.fixOriginalFileUrl(trackUuid);
      if (originalUrlFixed) {
        result.fixed_fields.push('original_file_url');
      }

      result.success = result.fixed_fields.length > 0;
      
      return result;
      
    } catch (error) {
      result.errors.push(error instanceof Error ? error.message : String(error));
      return result;
    }
  }

  /**
   * 修复文件格式
   */
  private static async fixFileFormat(trackUuid: string): Promise<boolean> {
    try {
      // 模拟修复逻辑
      console.log(`Checking file format for track: ${trackUuid}`);
      
      // 实际实现应该：
      // 1. 查询track记录
      // 2. 检查file_url的扩展名
      // 3. 如果与file_format不匹配，更新file_format
      
      return true; // 模拟修复成功
      
    } catch (error) {
      console.error(`Failed to fix file format for ${trackUuid}:`, error);
      return false;
    }
  }

  /**
   * 从metadata推断并修复style/mood
   */
  private static async fixStyleMoodFromMetadata(trackUuid: string): Promise<string[]> {
    const fixed: string[] = [];
    
    try {
      console.log(`Inferring style/mood from metadata for track: ${trackUuid}`);
      
      // 实际实现应该：
      // 1. 查询track记录和metadata
      // 2. 从genre/theme/instrument推断style和mood
      // 3. 更新track记录
      
      // 示例推断逻辑：
      // genre: ["cinematic", "orchestral"] -> style: "cinematic"
      // theme: ["journey", "fantasy"] -> mood: "epic"
      
      fixed.push('style', 'mood');
      return fixed;
      
    } catch (error) {
      console.error(`Failed to fix style/mood for ${trackUuid}:`, error);
      return [];
    }
  }

  /**
   * 修复original_file_url
   */
  private static async fixOriginalFileUrl(trackUuid: string): Promise<boolean> {
    try {
      console.log(`Fixing original_file_url for track: ${trackUuid}`);
      
      // 实际实现应该：
      // 1. 查询track记录
      // 2. 根据is_premium和has_watermark确定正确的original_file_url
      // 3. 更新记录
      
      return true;
      
    } catch (error) {
      console.error(`Failed to fix original_file_url for ${trackUuid}:`, error);
      return false;
    }
  }

  /**
   * 生成针对特定track的修复SQL
   */
  static generateFixSQL(trackUuid: string, fileUrl: string): string[] {
    const sqlStatements: string[] = [];

    // 1. 修复文件格式
    if (fileUrl.endsWith('.wav')) {
      sqlStatements.push(`
UPDATE tracks 
SET file_format = 'wav' 
WHERE uuid = '${trackUuid}' AND file_format != 'wav';
      `.trim());
    }

    // 2. 从metadata推断style和mood
    sqlStatements.push(`
UPDATE tracks 
SET 
  style = CASE 
    WHEN metadata::jsonb ? 'genre' THEN 
      CASE 
        WHEN metadata::jsonb->>'genre' LIKE '%cinematic%' THEN 'cinematic'
        WHEN metadata::jsonb->>'genre' LIKE '%orchestral%' THEN 'orchestral'
        WHEN metadata::jsonb->>'genre' LIKE '%electronic%' THEN 'electronic'
        ELSE 'ambient'
      END
    ELSE style
  END,
  mood = CASE 
    WHEN metadata::jsonb ? 'theme' THEN 
      CASE 
        WHEN metadata::jsonb->>'theme' LIKE '%journey%' OR metadata::jsonb->>'theme' LIKE '%fantasy%' THEN 'epic'
        WHEN metadata::jsonb->>'theme' LIKE '%technology%' THEN 'modern'
        ELSE 'uplifting'
      END
    ELSE mood
  END
WHERE uuid = '${trackUuid}' AND (style IS NULL OR mood IS NULL);
    `.trim());

    // 3. 修复original_file_url
    sqlStatements.push(`
UPDATE tracks 
SET original_file_url = CASE 
  WHEN has_watermark = false AND is_premium = true THEN file_url
  WHEN has_watermark = true THEN 
    COALESCE(
      metadata::jsonb->>'original_provider_url',
      file_url
    )
  ELSE file_url
END
WHERE uuid = '${trackUuid}' AND original_file_url IS NULL;
    `.trim());

    return sqlStatements;
  }

  /**
   * 批量修复所有有问题的tracks
   */
  static generateBatchFixSQL(): string {
    return `
-- 批量修复所有tracks的数据问题

-- 1. 修复文件格式不匹配问题
UPDATE tracks 
SET file_format = CASE 
  WHEN file_url LIKE '%.wav%' THEN 'wav'
  WHEN file_url LIKE '%.mp3%' THEN 'mp3'
  WHEN file_url LIKE '%.flac%' THEN 'flac'
  ELSE file_format
END
WHERE (
  (file_url LIKE '%.wav%' AND file_format != 'wav') OR
  (file_url LIKE '%.mp3%' AND file_format != 'mp3') OR
  (file_url LIKE '%.flac%' AND file_format != 'flac')
);

-- 2. 从metadata推断缺失的style字段
UPDATE tracks 
SET style = CASE 
  WHEN metadata::jsonb->>'genre' LIKE '%cinematic%' THEN 'cinematic'
  WHEN metadata::jsonb->>'genre' LIKE '%orchestral%' THEN 'orchestral'
  WHEN metadata::jsonb->>'genre' LIKE '%electronic%' THEN 'electronic'
  WHEN metadata::jsonb->>'genre' LIKE '%corporate%' THEN 'corporate'
  WHEN metadata::jsonb->>'instrument' LIKE '%piano%' THEN 'ambient'
  WHEN metadata::jsonb->>'instrument' LIKE '%strings%' THEN 'orchestral'
  ELSE 'ambient'
END
WHERE style IS NULL AND metadata IS NOT NULL;

-- 3. 从metadata推断缺失的mood字段
UPDATE tracks 
SET mood = CASE 
  WHEN metadata::jsonb->>'theme' LIKE '%journey%' OR metadata::jsonb->>'theme' LIKE '%fantasy%' THEN 'epic'
  WHEN metadata::jsonb->>'theme' LIKE '%technology%' THEN 'modern'
  WHEN prompt LIKE '%uplifting%' THEN 'uplifting'
  WHEN prompt LIKE '%corporate%' THEN 'professional'
  WHEN prompt LIKE '%electronic%' THEN 'energetic'
  ELSE 'neutral'
END
WHERE mood IS NULL;

-- 4. 修复所有缺失的original_file_url
UPDATE tracks 
SET original_file_url = CASE 
  WHEN has_watermark = false AND is_premium = true THEN file_url
  WHEN has_watermark = true THEN 
    COALESCE(
      metadata::jsonb->>'original_provider_url',
      file_url
    )
  ELSE file_url
END
WHERE original_file_url IS NULL;

-- 5. 验证修复结果
SELECT 
  COUNT(*) as total_tracks,
  COUNT(CASE WHEN style IS NULL THEN 1 END) as missing_style,
  COUNT(CASE WHEN mood IS NULL THEN 1 END) as missing_mood,
  COUNT(CASE WHEN original_file_url IS NULL THEN 1 END) as missing_original_url,
  COUNT(CASE WHEN file_url LIKE '%.wav%' AND file_format != 'wav' THEN 1 END) as format_mismatch
FROM tracks;
    `.trim();
  }
}

/**
 * 快速修复指定track的数据问题
 */
export async function quickFixTrack(trackUuid: string, fileUrl: string): Promise<void> {
  console.log(`🔧 Quick fixing track data for: ${trackUuid}`);
  
  const fixSQL = TrackDataFixer.generateFixSQL(trackUuid, fileUrl);
  
  console.log("Generated fix SQL:");
  fixSQL.forEach((sql, index) => {
    console.log(`${index + 1}. ${sql}`);
  });
  
  console.log("\n💡 Execute these SQL statements to fix the track data");
}