/**
 * CDN 测试工具
 */

import { isCDNConfigured, getDefaultCDNConfig } from './cdn-config';
import { generateSecureDownloadUrl } from '@/services/cdn';

export async function testCDNConfiguration(): Promise<{
  isConfigured: boolean;
  config?: any;
  testUrl?: string;
  error?: string;
}> {
  try {
    // 检查CDN是否已配置
    const configured = isCDNConfigured();
    
    if (!configured) {
      return {
        isConfigured: false,
        error: 'CDN not configured. Please check your environment variables.'
      };
    }

    // 获取配置
    const config = getDefaultCDNConfig();
    
    // 生成测试下载URL
    const testS3Url = 'https://loopcraft-music.tos-cn-beijing.volces.com/music/test-file.wav';
    const testResult = generateSecureDownloadUrl(testS3Url, {
      customFilename: 'test-download.wav',
      expiryHours: 1,
      forceDownload: true
    });

    return {
      isConfigured: true,
      config: {
        baseUrl: config.baseUrl,
        secretKeyLength: config.secretKey.length,
        defaultExpiryHours: config.defaultExpiryHours
      },
      testUrl: testResult.downloadUrl
    };

  } catch (error) {
    return {
      isConfigured: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

export function logCDNStatus(): void {
  testCDNConfiguration().then(result => {
    if (result.isConfigured) {
      console.log('✅ CDN Configuration Test Passed');
      console.log('📋 Config:', result.config);
      console.log('🔗 Test URL:', result.testUrl);
    } else {
      console.log('❌ CDN Configuration Test Failed');
      console.log('🚫 Error:', result.error);
    }
  }).catch(error => {
    console.error('💥 CDN Test Error:', error);
  });
}