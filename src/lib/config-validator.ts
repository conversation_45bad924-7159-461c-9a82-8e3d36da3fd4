/**
 * 配置验证工具
 * 验证TOS音乐集成所需的环境变量
 */

export interface ConfigValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  config: {
    volcano: {
      enabled: boolean;
      hasCredentials: boolean;
      hasTOSConfig: boolean;
      hasCallbackConfig: boolean;
    };
    callback: {
      enabled: boolean;
      hasSecret: boolean;
      hasBaseUrl: boolean;
    };
    tos: {
      hasDomain: boolean;
      hasBucket: boolean;
    };
  };
}

/**
 * 验证Volcano TOS集成配置
 */
export function validateTOSConfig(): ConfigValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 检查Volcano基本配置
  const volcanoAccessKeyId = process.env.VOLCANO_ACCESS_KEY_ID;
  const volcanoSecretAccessKey = process.env.VOLCANO_SECRET_ACCESS_KEY;
  const hasVolcanoCredentials = !!(volcanoAccessKeyId && volcanoSecretAccessKey);

  if (!hasVolcanoCredentials) {
    errors.push("Volcano API credentials missing (VOLCANO_ACCESS_KEY_ID, VOLCANO_SECRET_ACCESS_KEY)");
  }

  // 检查TOS配置
  const volcanoMusicDomain = process.env.VOLCANO_MUSIC_DOMAIN;
  const volcanoMusicBucket = process.env.VOLCANO_MUSIC_BUCKET;
  const hasTOSConfig = !!(volcanoMusicDomain && volcanoMusicBucket);

  if (!volcanoMusicDomain) {
    errors.push("VOLCANO_MUSIC_DOMAIN is required for TOS integration");
  }

  if (!volcanoMusicBucket) {
    errors.push("VOLCANO_MUSIC_BUCKET is required for TOS integration");
  }

  // 验证TOS域名格式
  if (volcanoMusicDomain) {
    try {
      new URL(volcanoMusicDomain);
    } catch (error) {
      errors.push(`Invalid VOLCANO_MUSIC_DOMAIN format: ${volcanoMusicDomain}`);
    }
  }

  // 检查回调配置
  const callbackSecret = process.env.VOLCANO_CALLBACK_SECRET;
  const callbackEnabled = process.env.VOLCANO_CALLBACK_ENABLED !== "false";
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL;

  if (callbackEnabled && !callbackSecret) {
    warnings.push("VOLCANO_CALLBACK_SECRET not set - callback validation will be disabled");
  }

  if (callbackEnabled && !baseUrl) {
    warnings.push("NEXT_PUBLIC_WEB_URL not set - callbacks will be disabled");
  }

  // 验证回调URL格式
  if (baseUrl) {
    try {
      new URL(baseUrl);
    } catch (error) {
      errors.push(`Invalid NEXT_PUBLIC_WEB_URL format: ${baseUrl}`);
    }
  }

  const config = {
    volcano: {
      enabled: hasVolcanoCredentials,
      hasCredentials: hasVolcanoCredentials,
      hasTOSConfig: hasTOSConfig,
      hasCallbackConfig: !!(callbackSecret && baseUrl),
    },
    callback: {
      enabled: callbackEnabled,
      hasSecret: !!callbackSecret,
      hasBaseUrl: !!baseUrl,
    },
    tos: {
      hasDomain: !!volcanoMusicDomain,
      hasBucket: !!volcanoMusicBucket,
    },
  };

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    config,
  };
}

/**
 * 获取TOS配置信息
 */
export function getTOSConfig() {
  return {
    domain: process.env.VOLCANO_MUSIC_DOMAIN,
    bucket: process.env.VOLCANO_MUSIC_BUCKET,
    callbackSecret: process.env.VOLCANO_CALLBACK_SECRET,
    callbackEnabled: process.env.VOLCANO_CALLBACK_ENABLED !== "false",
    baseUrl: process.env.NEXT_PUBLIC_WEB_URL,
  };
}

/**
 * 构造回调URL
 */
export function constructCallbackUrl(): string | null {
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL;
  if (!baseUrl) {
    return null;
  }

  try {
    const url = new URL(baseUrl);
    url.pathname = "/api/music/callback/volcano";
    return url.toString();
  } catch (error) {
    console.error("Failed to construct callback URL:", error);
    return null;
  }
}

/**
 * 验证回调请求签名
 */
export function validateCallbackSignature(
  payload: any,
  signature?: string,
  timestamp?: string
): boolean {
  const secret = process.env.VOLCANO_CALLBACK_SECRET;
  
  if (!secret) {
    console.warn("VOLCANO_CALLBACK_SECRET not configured - skipping signature validation");
    return true; // 如果没有配置密钥，跳过验证
  }

  if (!signature) {
    console.warn("No signature provided in callback request");
    return false;
  }

  // TODO: 实现实际的签名验证逻辑
  // 这里需要根据Volcano的签名算法实现
  // 目前返回true作为占位符
  console.log("Callback signature validation:", {
    hasSecret: !!secret,
    hasSignature: !!signature,
    hasTimestamp: !!timestamp,
  });

  return true;
}

/**
 * 记录配置状态
 */
export function logConfigStatus(): void {
  const validation = validateTOSConfig();
  
  console.log("TOS Configuration Status:", {
    isValid: validation.isValid,
    volcano: validation.config.volcano,
    callback: validation.config.callback,
    tos: validation.config.tos,
  });

  if (validation.errors.length > 0) {
    console.error("Configuration Errors:", validation.errors);
  }

  if (validation.warnings.length > 0) {
    console.warn("Configuration Warnings:", validation.warnings);
  }
}

/**
 * 启动时验证配置
 */
export function validateConfigOnStartup(): boolean {
  const validation = validateTOSConfig();
  
  if (!validation.isValid) {
    console.error("❌ TOS Configuration validation failed:");
    validation.errors.forEach(error => console.error(`  - ${error}`));
    return false;
  }

  console.log("✅ TOS Configuration validation passed");
  
  if (validation.warnings.length > 0) {
    console.warn("⚠️  Configuration warnings:");
    validation.warnings.forEach(warning => console.warn(`  - ${warning}`));
  }

  return true;
}