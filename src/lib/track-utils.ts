/**
 * Track utility functions
 * Helper functions for track operations and type detection
 */

/**
 * Check if a track uses TOS (Volcengine Object Storage) based on file URL pattern
 * @param track Track object or file URL string
 * @returns true if the track uses TOS storage
 */
export function isTOSTrack(track: { file_url: string } | string): boolean {
  const fileUrl = typeof track === 'string' ? track : track.file_url;
  const domain = process.env.VOLCANO_MUSIC_DOMAIN;
  
  if (!domain || !fileUrl) {
    return false;
  }
  
  return fileUrl.includes(domain);
}

/**
 * Check if a track is a legacy track (not using TOS)
 * @param track Track object or file URL string
 * @returns true if the track is a legacy track
 */
export function isLegacyTrack(track: { file_url: string } | string): boolean {
  return !isTOSTrack(track);
}

/**
 * Extract TOS path from a TOS URL
 * @param tosUrl Full TOS URL
 * @returns TOS path or null if not a valid TOS URL
 */
export function extractTOSPath(tosUrl: string): string | null {
  const domain = process.env.VOLCANO_MUSIC_DOMAIN;
  
  if (!domain || !tosUrl.includes(domain)) {
    return null;
  }
  
  try {
    const url = new URL(tosUrl);
    // Remove leading slash from pathname
    return url.pathname.replace(/^\//, '');
  } catch (error) {
    console.warn('Failed to extract TOS path from URL:', tosUrl, error);
    return null;
  }
}

/**
 * Construct TOS URL from path
 * @param tosPath TOS path (e.g., "music_ai/v03e70g10004d1tl447og65v4f1uii3g.wav")
 * @returns Full TOS URL
 */
export function constructTOSUrl(tosPath: string): string {
  const domain = process.env.VOLCANO_MUSIC_DOMAIN;
  
  if (!domain) {
    throw new Error('VOLCANO_MUSIC_DOMAIN not configured');
  }
  
  const cleanDomain = domain.replace(/\/$/, '');
  const cleanPath = tosPath.replace(/^\//, '');
  
  return `${cleanDomain}/${cleanPath}`;
}

/**
 * Get track storage type for debugging/logging
 * @param track Track object or file URL string
 * @returns Storage type string
 */
export function getTrackStorageType(track: { file_url: string } | string): 'tos' | 'legacy' {
  return isTOSTrack(track) ? 'tos' : 'legacy';
}