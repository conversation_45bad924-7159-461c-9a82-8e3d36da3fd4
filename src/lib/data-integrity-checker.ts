/**
 * 数据完整性检查和修复工具
 * 检查和修复TOS集成相关的数据问题
 */

export interface DataIntegrityIssue {
  type: 'missing_field' | 'invalid_value' | 'inconsistent_data';
  table: string;
  field: string;
  record_id: string;
  description: string;
  suggested_fix?: string;
}

export interface DataIntegrityReport {
  total_records: number;
  issues_found: number;
  issues: DataIntegrityIssue[];
  summary: {
    missing_style_mood_bpm: number;
    missing_genre_instrument_theme: number;
    missing_original_file_url: number;
    inconsistent_tos_data: number;
  };
}

export class DataIntegrityChecker {
  
  /**
   * 检查tracks表的数据完整性
   */
  static async checkTracksIntegrity(): Promise<DataIntegrityReport> {
    const issues: DataIntegrityIssue[] = [];
    let totalRecords = 0;

    try {
      // 这里需要实际的数据库查询
      // 由于我们没有直接的数据库访问，这里提供检查逻辑的框架
      
      console.log("Starting tracks data integrity check...");
      
      // TODO: 实现实际的数据库查询
      // const tracks = await db().select().from(tracks);
      
      // 模拟检查逻辑
      const mockTrackData = {
        uuid: 'd15391f8-7f53-4e4a-9062-276ba71e2da8',
        style: null,
        mood: null,
        bpm: null,
        genre: null,
        instrument: null,
        theme: null,
        original_file_url: null,
        file_path: 'music_ai/v02e70g10004d1tibanog65hlacpq76g.wav',
        file_url: 'https://loopcraft-music.tos-cn-beijing.volces.com/music_ai/v02e70g10004d1tibanog65hlacpq76g.wav',
      };

      // 检查缺失的style, mood, bpm字段
      if (!mockTrackData.style && !mockTrackData.mood && !mockTrackData.bpm) {
        issues.push({
          type: 'missing_field',
          table: 'tracks',
          field: 'style,mood,bpm',
          record_id: mockTrackData.uuid,
          description: 'Track missing style, mood, and bpm data from generation',
          suggested_fix: 'Copy values from related music_generation record',
        });
      }

      // 检查缺失的新API字段
      if (!mockTrackData.genre && !mockTrackData.instrument && !mockTrackData.theme) {
        issues.push({
          type: 'missing_field',
          table: 'tracks',
          field: 'genre,instrument,theme',
          record_id: mockTrackData.uuid,
          description: 'Track missing new API fields (genre, instrument, theme)',
          suggested_fix: 'Copy values from related music_generation record',
        });
      }

      // 检查TOS文件的original_file_url
      if (mockTrackData.file_path && !mockTrackData.original_file_url) {
        issues.push({
          type: 'missing_field',
          table: 'tracks',
          field: 'original_file_url',
          record_id: mockTrackData.uuid,
          description: 'TOS track missing original_file_url for premium users',
          suggested_fix: 'Set to TOS URL for premium users, or provider URL for watermarked tracks',
        });
      }

      totalRecords = 1; // 模拟数据

      const summary = {
        missing_style_mood_bpm: issues.filter(i => i.field.includes('style')).length,
        missing_genre_instrument_theme: issues.filter(i => i.field.includes('genre')).length,
        missing_original_file_url: issues.filter(i => i.field === 'original_file_url').length,
        inconsistent_tos_data: 0,
      };

      return {
        total_records: totalRecords,
        issues_found: issues.length,
        issues,
        summary,
      };

    } catch (error) {
      console.error("Data integrity check failed:", error);
      throw error;
    }
  }

  /**
   * 生成修复SQL语句
   */
  static generateFixSQL(issues: DataIntegrityIssue[]): string[] {
    const sqlStatements: string[] = [];

    for (const issue of issues) {
      switch (issue.type) {
        case 'missing_field':
          if (issue.field.includes('style,mood,bmp')) {
            sqlStatements.push(`
-- Fix missing style, mood, bpm for track ${issue.record_id}
UPDATE tracks 
SET 
  style = (SELECT style FROM music_generations WHERE uuid = tracks.generation_uuid),
  mood = (SELECT mood FROM music_generations WHERE uuid = tracks.generation_uuid),
  bpm = (SELECT bmp FROM music_generations WHERE uuid = tracks.generation_uuid)
WHERE uuid = '${issue.record_id}';
            `.trim());
          }

          if (issue.field.includes('genre,instrument,theme')) {
            sqlStatements.push(`
-- Fix missing genre, instrument, theme for track ${issue.record_id}
UPDATE tracks 
SET 
  genre = (SELECT genre FROM music_generations WHERE uuid = tracks.generation_uuid),
  instrument = (SELECT instrument FROM music_generations WHERE uuid = tracks.generation_uuid),
  theme = (SELECT theme FROM music_generations WHERE uuid = tracks.generation_uuid)
WHERE uuid = '${issue.record_id}';
            `.trim());
          }

          if (issue.field === 'original_file_url') {
            sqlStatements.push(`
-- Fix missing original_file_url for TOS track ${issue.record_id}
UPDATE tracks 
SET original_file_url = CASE 
  WHEN has_watermark = false THEN file_url  -- Premium users get TOS URL
  WHEN has_watermark = true THEN (
    -- Extract original provider URL from metadata
    CASE 
      WHEN metadata::jsonb ? 'original_provider_url' 
      THEN metadata::jsonb->>'original_provider_url'
      ELSE file_url
    END
  )
  ELSE file_url
END
WHERE uuid = '${issue.record_id}' AND original_file_url IS NULL;
            `.trim());
          }
          break;
      }
    }

    return sqlStatements;
  }

  /**
   * 验证TOS URL的有效性
   */
  static async validateTOSUrls(trackUuids: string[]): Promise<{
    valid: string[];
    invalid: string[];
    errors: { uuid: string; error: string }[];
  }> {
    const valid: string[] = [];
    const invalid: string[] = [];
    const errors: { uuid: string; error: string }[] = [];

    for (const uuid of trackUuids) {
      try {
        // TODO: 实际查询track的file_url
        // const track = await findTrackByUuid(uuid);
        // if (track && track.file_url) {
        //   const response = await fetch(track.file_url, { method: 'HEAD' });
        //   if (response.ok) {
        //     valid.push(uuid);
        //   } else {
        //     invalid.push(uuid);
        //   }
        // }
        
        // 模拟验证
        valid.push(uuid);
        
      } catch (error) {
        errors.push({
          uuid,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    return { valid, invalid, errors };
  }

  /**
   * 生成数据完整性报告
   */
  static async generateReport(): Promise<string> {
    try {
      const report = await this.checkTracksIntegrity();
      
      let reportText = `# Data Integrity Report\n\n`;
      reportText += `**Generated:** ${new Date().toISOString()}\n\n`;
      reportText += `## Summary\n`;
      reportText += `- Total Records: ${report.total_records}\n`;
      reportText += `- Issues Found: ${report.issues_found}\n\n`;
      
      if (report.issues_found > 0) {
        reportText += `## Issues Breakdown\n`;
        reportText += `- Missing style/mood/bpm: ${report.summary.missing_style_mood_bpm}\n`;
        reportText += `- Missing genre/instrument/theme: ${report.summary.missing_genre_instrument_theme}\n`;
        reportText += `- Missing original_file_url: ${report.summary.missing_original_file_url}\n`;
        reportText += `- Inconsistent TOS data: ${report.summary.inconsistent_tos_data}\n\n`;
        
        reportText += `## Detailed Issues\n`;
        for (const issue of report.issues) {
          reportText += `### ${issue.type.toUpperCase()}: ${issue.field}\n`;
          reportText += `- **Record:** ${issue.record_id}\n`;
          reportText += `- **Description:** ${issue.description}\n`;
          if (issue.suggested_fix) {
            reportText += `- **Suggested Fix:** ${issue.suggested_fix}\n`;
          }
          reportText += `\n`;
        }

        reportText += `## Fix SQL\n`;
        reportText += `\`\`\`sql\n`;
        const fixSQL = this.generateFixSQL(report.issues);
        reportText += fixSQL.join('\n\n');
        reportText += `\n\`\`\`\n`;
      } else {
        reportText += `✅ No data integrity issues found!\n`;
      }

      return reportText;
      
    } catch (error) {
      return `# Data Integrity Report - ERROR\n\nFailed to generate report: ${error}`;
    }
  }
}

/**
 * 快速检查和报告数据问题
 */
export async function quickDataCheck(): Promise<void> {
  console.log("🔍 Running quick data integrity check...");
  
  try {
    const report = await DataIntegrityChecker.checkTracksIntegrity();
    
    if (report.issues_found === 0) {
      console.log("✅ No data integrity issues found!");
    } else {
      console.log(`⚠️  Found ${report.issues_found} data integrity issues:`);
      
      report.issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue.description} (${issue.record_id})`);
      });
      
      console.log("\n💡 Run DataIntegrityChecker.generateReport() for detailed fix suggestions");
    }
    
  } catch (error) {
    console.error("❌ Data integrity check failed:", error);
  }
}