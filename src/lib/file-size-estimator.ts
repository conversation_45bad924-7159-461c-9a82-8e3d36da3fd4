/**
 * File Size Estimator for Music Tracks
 * 
 * Estimates file size based on duration using linear interpolation
 * from known reference points in production data.
 */

interface SizeReferencePoint {
  duration: number; // Duration in seconds
  size: number;     // File size in bytes
}

export class FileSizeEstimator {
  // Reference points from production data analysis
  private static readonly REFERENCE_POINTS: SizeReferencePoint[] = [
    { duration: 15, size: 2646078 },   // 15 seconds = ~2.65MB
    { duration: 30, size: 5292078 },   // 30 seconds = ~5.29MB  
    { duration: 60, size: 10646078 }   // 60 seconds = ~10.65MB
  ];

  /**
   * Estimates file size based on track duration using linear interpolation
   * @param duration Duration in seconds
   * @returns Estimated file size in bytes
   */
  static estimateFileSize(duration: number): number {
    if (duration <= 0) {
      return 0;
    }

    const points = this.REFERENCE_POINTS;

    // For durations at or below 15 seconds
    if (duration <= points[0].duration) {
      const ratio = duration / points[0].duration;
      return Math.round(ratio * points[0].size);
    }

    // For durations between 15 and 30 seconds
    if (duration <= points[1].duration) {
      return this.interpolate(
        duration,
        points[0].duration, points[0].size,
        points[1].duration, points[1].size
      );
    }

    // For durations between 30 and 60 seconds
    if (duration <= points[2].duration) {
      return this.interpolate(
        duration,
        points[1].duration, points[1].size,
        points[2].duration, points[2].size
      );
    }

    // For durations beyond 60 seconds, extrapolate using the 30-60s slope
    const bytesPerSecond = (points[2].size - points[1].size) / (points[2].duration - points[1].duration);
    const extrapolatedSize = points[2].size + (duration - points[2].duration) * bytesPerSecond;
    return Math.round(extrapolatedSize);
  }

  /**
   * Linear interpolation between two points
   */
  private static interpolate(
    x: number,
    x1: number, y1: number,
    x2: number, y2: number
  ): number {
    const ratio = (x - x1) / (x2 - x1);
    const interpolatedValue = y1 + ratio * (y2 - y1);
    return Math.round(interpolatedValue);
  }

  /**
   * Get the average bytes per second based on reference points
   * Useful for validation and debugging
   */
  static getAverageBytesPerSecond(): number {
    const totalBytes = this.REFERENCE_POINTS.reduce((sum, point) => sum + point.size, 0);
    const totalDuration = this.REFERENCE_POINTS.reduce((sum, point) => sum + point.duration, 0);
    return Math.round(totalBytes / totalDuration);
  }

  /**
   * Validate if an estimated size is reasonable for the given duration
   * @param duration Duration in seconds
   * @param estimatedSize Estimated size in bytes
   * @returns true if the size seems reasonable
   */
  static validateEstimatedSize(duration: number, estimatedSize: number): boolean {
    const expectedSize = this.estimateFileSize(duration);
    const tolerance = 0.1; // 10% tolerance
    const minSize = expectedSize * (1 - tolerance);
    const maxSize = expectedSize * (1 + tolerance);
    
    return estimatedSize >= minSize && estimatedSize <= maxSize;
  }
}