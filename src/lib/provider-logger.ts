/**
 * Provider Logger Utility
 * Provides consistent logging for music provider operations
 */

export interface TaskContext {
  generation_uuid: string;
  provider_task_id: string;
  user_uuid: string;
}

export class ProviderLogger {
  /**
   * Log provider status with consistent format
   */
  static logStatus(context: TaskContext, status: any, message: string) {
    console.log(`${message}: {
      generation_uuid: ${context.generation_uuid},
      provider_task_id: ${context.provider_task_id},
      user_uuid: ${context.user_uuid},
      status: ${JSON.stringify(status)}
    }`);
  }

  /**
   * Log provider errors with consistent format
   */
  static logError(context: TaskContext, error: any, operation: string) {
    console.error(`${operation} failed for generation ${context.generation_uuid} (task: ${context.provider_task_id}, user: ${context.user_uuid}):`, error);
  }

  /**
   * Log provider operation start
   */
  static logOperationStart(context: TaskContext, operation: string, details?: any) {
    console.log(`Starting ${operation} for generation ${context.generation_uuid} (task: ${context.provider_task_id})`, details || '');
  }

  /**
   * Log provider operation completion
   */
  static logOperationComplete(context: TaskContext, operation: string, result?: any) {
    console.log(`Completed ${operation} for generation ${context.generation_uuid} (task: ${context.provider_task_id})`, result ? JSON.stringify(result) : '');
  }

  /**
   * Log provider warning with context
   */
  static logWarning(context: TaskContext, message: string, details?: any) {
    console.warn(`Warning for generation ${context.generation_uuid} (task: ${context.provider_task_id}): ${message}`, details || '');
  }

  /**
   * Create a safe logger that handles missing context gracefully
   */
  static createSafeLogger(context?: TaskContext | null) {
    return {
      logStatus: (status: any, message: string) => {
        if (context) {
          this.logStatus(context, status, message);
        } else {
          console.log(`${message}:`, JSON.stringify(status));
        }
      },
      logError: (error: any, operation: string) => {
        if (context) {
          this.logError(context, error, operation);
        } else {
          console.error(`${operation} failed:`, error);
        }
      },
      logOperationStart: (operation: string, details?: any) => {
        if (context) {
          this.logOperationStart(context, operation, details);
        } else {
          console.log(`Starting ${operation}`, details || '');
        }
      },
      logOperationComplete: (operation: string, result?: any) => {
        if (context) {
          this.logOperationComplete(context, operation, result);
        } else {
          console.log(`Completed ${operation}`, result ? JSON.stringify(result) : '');
        }
      },
      logWarning: (message: string, details?: any) => {
        if (context) {
          this.logWarning(context, message, details);
        } else {
          console.warn(message, details || '');
        }
      }
    };
  }

  /**
   * Format context for display in logs
   */
  static formatContext(context: TaskContext): string {
    return `[gen:${context.generation_uuid.substring(0, 8)}, task:${context.provider_task_id}, user:${context.user_uuid.substring(0, 8)}]`;
  }

  /**
   * Log with formatted context prefix
   */
  static logWithContext(context: TaskContext, level: 'log' | 'error' | 'warn' | 'info', message: string, ...args: any[]) {
    const prefix = this.formatContext(context);
    console[level](`${prefix} ${message}`, ...args);
  }
}