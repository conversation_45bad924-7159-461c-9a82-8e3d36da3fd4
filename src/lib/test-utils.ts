// Test utilities for mocking and testing

export const mockSession = {
  user: {
    id: 'test-user-id',
    email: '<EMAIL>',
    name: 'Test User',
    image: null,
  },
  expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
};

export const mockTrack = {
  uuid: 'test-track-1',
  title: 'Test Track',
  file_url: 'https://example.com/test.mp3',
  duration: 120,
  style: 'Electronic',
  bpm: 128,
  is_public: true,
  created_at: new Date().toISOString(),
  generation_uuid: 'test-generation-1',
  user_uuid: 'test-user-id',
  prompt: 'Test prompt',
  file_format: 'mp3',
  download_count: 0,
  waveform_data: {
    peaks: [0.1, 0.3, 0.5, 0.7, 0.4, 0.2],
    duration: 120,
    sample_rate: 44100,
  },
};

// Mock HTMLAudioElement for tests
export const mockAudioElement = {
  play: () => Promise.resolve(),
  pause: () => {},
  load: () => {},
  addEventListener: () => {},
  removeEventListener: () => {},
  currentTime: 0,
  duration: 120,
  volume: 1,
  paused: true,
  src: '',
};

// Setup mock for HTMLAudioElement (for Jest environments)
export const setupAudioMock = () => {
  if (typeof window !== 'undefined' && (window as any).jest) {
    const jest = (window as any).jest;
    global.HTMLAudioElement = jest.fn().mockImplementation(() => ({
      play: jest.fn().mockResolvedValue(undefined),
      pause: jest.fn(),
      load: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      currentTime: 0,
      duration: 120,
      volume: 1,
      paused: true,
      src: '',
    }));
  }
};