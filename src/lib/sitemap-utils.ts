import type { 
  SitemapEntry, 
  AlternateLink
} from "@/types/sitemap";
import { 
  SITEMAP_CONFIG, 
  XML_CONSTANTS, 
  URL_VALIDATION,
  SITEMAP_ERROR_MESSAGES 
} from "@/constants/sitemap";

/**
 * Validates a sitemap entry for correctness
 */
export function validateSitemapEntry(entry: SitemapEntry): boolean {
  // Validate URL
  if (!entry.url || typeof entry.url !== 'string') {
    return false;
  }

  // Check URL length
  if (entry.url.length > XML_CONSTANTS.MAX_URL_LENGTH) {
    throw new Error(`${SITEMAP_ERROR_MESSAGES.URL_TOO_LONG}: ${entry.url}`);
  }

  // Validate URL format
  try {
    const url = new URL(entry.url);
    if (!URL_VALIDATION.ALLOWED_PROTOCOLS.includes(url.protocol as "http:" | "https:")) {
      return false;
    }
  } catch {
    return false;
  }

  // Validate lastmod format (ISO 8601)
  if (entry.lastmod && !isValidISODate(entry.lastmod)) {
    return false;
  }

  // Validate priority range (0.0 to 1.0)
  if (entry.priority) {
    const priority = parseFloat(entry.priority);
    if (isNaN(priority) || priority < 0 || priority > 1) {
      return false;
    }
  }

  return true;
}

/**
 * Validates ISO 8601 date format
 */
export function isValidISODate(dateString: string): boolean {
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date.getTime()) && 
         date.toISOString().startsWith(dateString.substring(0, 10));
}

/**
 * Escapes XML special characters
 */
export function escapeXML(text: string): string {
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&apos;');
}

/**
 * Generates cache key for sitemap content
 */
export function generateCacheKey(
  contentType: string, 
  locale?: string, 
  additionalParams?: Record<string, string>
): string {
  const parts = ['sitemap', contentType];
  
  if (locale) {
    parts.push(locale);
  }
  
  if (additionalParams) {
    const paramString = Object.entries(additionalParams)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([key, value]) => `${key}:${value}`)
      .join(',');
    parts.push(paramString);
  }
  
  return parts.join(':');
}

/**
 * Formats date for sitemap lastmod field
 */
export function formatSitemapDate(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toISOString();
}

/**
 * Checks if URL should be excluded from sitemap
 */
export function shouldExcludeUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    
    return URL_VALIDATION.DISALLOWED_PATHS.some(disallowedPath => 
      pathname.startsWith(disallowedPath)
    );
  } catch {
    return true; // Exclude invalid URLs
  }
}

/**
 * Removes duplicate URLs from sitemap entries
 */
export function deduplicateSitemapEntries(entries: SitemapEntry[]): SitemapEntry[] {
  const seen = new Set<string>();
  return entries.filter(entry => {
    if (seen.has(entry.url)) {
      return false;
    }
    seen.add(entry.url);
    return true;
  });
}

/**
 * Sorts sitemap entries by priority and lastmod
 */
export function sortSitemapEntries(entries: SitemapEntry[]): SitemapEntry[] {
  return entries.sort((a, b) => {
    // First sort by priority (higher first)
    const priorityA = parseFloat(a.priority) || 0.5;
    const priorityB = parseFloat(b.priority) || 0.5;
    
    if (priorityA !== priorityB) {
      return priorityB - priorityA;
    }
    
    // Then sort by lastmod (newer first)
    const dateA = new Date(a.lastmod).getTime();
    const dateB = new Date(b.lastmod).getTime();
    
    return dateB - dateA;
  });
}

/**
 * Splits large sitemap into chunks
 */
export function chunkSitemapEntries(
  entries: SitemapEntry[], 
  maxPerChunk: number = SITEMAP_CONFIG.maxUrlsPerSitemap
): SitemapEntry[][] {
  const chunks: SitemapEntry[][] = [];
  
  for (let i = 0; i < entries.length; i += maxPerChunk) {
    chunks.push(entries.slice(i, i + maxPerChunk));
  }
  
  return chunks;
}

/**
 * Calculates estimated sitemap size in bytes
 */
export function estimateSitemapSize(entries: SitemapEntry[]): number {
  const baseXMLSize = 200; // Approximate size of XML wrapper
  const averageEntrySize = 150; // Approximate size per URL entry
  
  return baseXMLSize + (entries.length * averageEntrySize);
}

/**
 * Validates complete sitemap structure
 */
export function validateSitemap(entries: SitemapEntry[]): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  // Check if sitemap is empty
  if (entries.length === 0) {
    errors.push("Sitemap contains no entries");
  }
  
  // Check sitemap size limits
  if (entries.length > SITEMAP_CONFIG.maxUrlsPerSitemap) {
    errors.push(`Sitemap exceeds maximum URL limit: ${entries.length} > ${SITEMAP_CONFIG.maxUrlsPerSitemap}`);
  }
  
  const estimatedSize = estimateSitemapSize(entries);
  if (estimatedSize > XML_CONSTANTS.MAX_SITEMAP_SIZE) {
    errors.push(`Sitemap exceeds maximum size limit: ${estimatedSize} > ${XML_CONSTANTS.MAX_SITEMAP_SIZE}`);
  }
  
  // Validate individual entries
  entries.forEach((entry, index) => {
    try {
      if (!validateSitemapEntry(entry)) {
        errors.push(`Invalid sitemap entry at index ${index}: ${entry.url}`);
      }
    } catch (error) {
      errors.push(`Validation error at index ${index}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  });
  
  // Check for duplicates
  const urls = entries.map(entry => entry.url);
  const uniqueUrls = new Set(urls);
  if (urls.length !== uniqueUrls.size) {
    errors.push("Sitemap contains duplicate URLs");
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Creates a basic sitemap entry
 */
export function createSitemapEntry(
  url: string,
  lastmod?: Date | string,
  changefreq?: SitemapEntry['changefreq'],
  priority?: string,
  alternates?: AlternateLink[]
): SitemapEntry {
  return {
    url: escapeXML(url),
    lastmod: formatSitemapDate(lastmod || new Date()),
    changefreq: changefreq || 'weekly',
    priority: priority || '0.5',
    alternates: alternates || []
  };
}

/**
 * Determines if sitemap index should be used based on content size
 */
export function shouldUseSitemapIndex(
  totalUrls: number,
  estimatedSize?: number
): boolean {
  // Use index if we exceed URL limits
  if (totalUrls > SITEMAP_CONFIG.maxUrlsPerSitemap) {
    return true;
  }
  
  // Use index if we exceed size limits
  if (estimatedSize && estimatedSize > SITEMAP_CONFIG.maxSitemapSize) {
    return true;
  }
  
  // Use index if we have multiple content types with significant content
  if (totalUrls > SITEMAP_CONFIG.maxUrlsPerSitemap * 0.8) {
    return true;
  }
  
  return false;
}

/**
 * Extracts locale from URL path
 */
export function extractLocaleFromPath(pathname: string, locales: string[]): string | null {
  const segments = pathname.split('/').filter(Boolean);
  const firstSegment = segments[0];
  
  if (firstSegment && locales.includes(firstSegment)) {
    return firstSegment;
  }
  
  return null;
}

/**
 * Gets the last modified date for sitemap content
 */
export async function getLastModifiedDate(): Promise<Date> {
  // This would normally query the database for the latest update
  // For now, return current time minus cache timeout
  return new Date(Date.now() - SITEMAP_CONFIG.cacheTimeout * 1000);
}

/**
 * Measures execution time of a function
 */
export async function measureExecutionTime<T>(
  fn: () => Promise<T>
): Promise<{ result: T; executionTime: number }> {
  const startTime = Date.now();
  const result = await fn();
  const executionTime = Date.now() - startTime;
  
  return { result, executionTime };
}