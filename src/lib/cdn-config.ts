/**
 * CDN 配置管理服务
 */

export interface CDNConfiguration {
  baseUrl: string;
  secretKey: string;
  defaultExpiryHours: number;
}

export interface CDNConfigValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * CDN 配置管理类
 */
export class CDNConfigManager {
  private static instance: CDNConfigManager | null = null;
  private config: CDNConfiguration | null = null;

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): CDNConfigManager {
    if (!CDNConfigManager.instance) {
      CDNConfigManager.instance = new CDNConfigManager();
    }
    return CDNConfigManager.instance;
  }

  /**
   * 加载配置
   */
  loadConfig(): CDNConfiguration {
    if (this.config) {
      return this.config;
    }

    // 只使用明确的CDN_BASE_URL，不回退到VOLCANO_MUSIC_DOMAIN
    const baseUrl = process.env.CDN_BASE_URL;
    const secretKey = process.env.CDN_SECRET_KEY;
    const defaultExpiryHours = parseInt(process.env.CDN_DEFAULT_EXPIRY_HOURS || '1');

    if (!baseUrl) {
      throw new Error('CDN base URL not configured. Please set CDN_BASE_URL environment variable.');
    }

    if (!secretKey) {
      throw new Error('CDN secret key not configured. Please set CDN_SECRET_KEY environment variable.');
    }

    this.config = {
      baseUrl: baseUrl.replace(/\/$/, ''), // 移除末尾的斜杠
      secretKey,
      defaultExpiryHours
    };

    return this.config;
  }

  /**
   * 验证配置
   */
  validateConfig(config?: CDNConfiguration): CDNConfigValidationResult {
    const configToValidate = config || this.config;
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!configToValidate) {
      errors.push('Configuration not loaded');
      return { isValid: false, errors, warnings };
    }

    // 验证 baseUrl
    if (!configToValidate.baseUrl) {
      errors.push('Base URL is required');
    } else {
      try {
        const url = new URL(configToValidate.baseUrl);
        if (!url.protocol.startsWith('http')) {
          errors.push('Base URL must use HTTP or HTTPS protocol');
        }
        if (url.pathname !== '' && url.pathname !== '/') {
          warnings.push('Base URL contains path, this may cause issues');
        }
      } catch (error) {
        errors.push('Base URL is not a valid URL');
      }
    }

    // 验证 secretKey
    if (!configToValidate.secretKey) {
      errors.push('Secret key is required');
    } else {
      if (configToValidate.secretKey.length < 16) {
        warnings.push('Secret key should be at least 16 characters for better security');
      }
      if (configToValidate.secretKey === 'your-secret-key-here' || 
          configToValidate.secretKey === 'test-secret-key') {
        errors.push('Secret key appears to be a placeholder, please set a real secret key');
      }
    }

    // 验证 defaultExpiryHours
    if (configToValidate.defaultExpiryHours <= 0) {
      errors.push('Default expiry hours must be greater than 0');
    } else if (configToValidate.defaultExpiryHours > 24) {
      warnings.push('Default expiry hours is greater than 24 hours, consider security implications');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 获取配置
   */
  getConfig(): CDNConfiguration {
    if (!this.config) {
      this.loadConfig();
    }
    return this.config!;
  }

  /**
   * 重新加载配置
   */
  reloadConfig(): CDNConfiguration {
    this.config = null;
    return this.loadConfig();
  }

  /**
   * 检查配置是否已加载
   */
  isConfigLoaded(): boolean {
    return this.config !== null;
  }
}

/**
 * 验证启动时配置
 */
export function validateStartupConfig(): void {
  const configManager = CDNConfigManager.getInstance();
  
  try {
    const config = configManager.loadConfig();
    const validation = configManager.validateConfig(config);
    
    if (!validation.isValid) {
      console.error('CDN Configuration Errors:');
      validation.errors.forEach(error => console.error(`  - ${error}`));
      throw new Error('CDN configuration is invalid. Please check your environment variables.');
    }
    
    if (validation.warnings.length > 0) {
      console.warn('CDN Configuration Warnings:');
      validation.warnings.forEach(warning => console.warn(`  - ${warning}`));
    }
    
    console.log('CDN configuration loaded successfully:', {
      baseUrl: config.baseUrl,
      secretKeyLength: config.secretKey.length,
      defaultExpiryHours: config.defaultExpiryHours
    });
    
  } catch (error) {
    console.error('Failed to load CDN configuration:', error);
    throw error;
  }
}

/**
 * 获取默认 CDN 配置
 */
export function getDefaultCDNConfig(): CDNConfiguration {
  const configManager = CDNConfigManager.getInstance();
  return configManager.getConfig();
}

/**
 * 检查 CDN 是否已配置
 */
export function isCDNConfigured(): boolean {
  try {
    // 首先检查是否明确禁用了CDN
    const cdnEnabled = process.env.CDN_ENABLED;
    if (cdnEnabled === 'false' || cdnEnabled === '0') {
      console.log('CDN explicitly disabled via CDN_ENABLED environment variable');
      return false;
    }

    // 检查是否有专门的CDN_BASE_URL配置
    const cdnBaseUrl = process.env.CDN_BASE_URL;
    if (!cdnBaseUrl) {
      console.log('CDN not configured: CDN_BASE_URL not set');
      return false;
    }

    const configManager = CDNConfigManager.getInstance();
    const config = configManager.loadConfig();
    const validation = configManager.validateConfig(config);
    
    if (!validation.isValid) {
      console.log('CDN configuration invalid:', validation.errors);
      return false;
    }
    
    return true;
  } catch (error) {
    console.log('CDN configuration check failed:', error);
    return false;
  }
}