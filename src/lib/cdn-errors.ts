/**
 * CDN 错误处理和日志记录
 */

export enum CDNErrorType {
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  TOKEN_INVALID = 'TOKEN_INVALID',
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  CDN_UNAVAILABLE = 'CDN_UNAVAILABLE',
  RATE_LIMITED = 'RATE_LIMITED',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  INVALID_REQUEST = 'INVALID_REQUEST',
  SERVICE_TIMEOUT = 'SERVICE_TIMEOUT'
}

export interface CDNError extends Error {
  type: CDNErrorType;
  code: string;
  details?: any;
  timestamp: Date;
  requestId?: string;
  userId?: string;
  trackId?: string;
}

export interface ErrorLogEntry {
  timestamp: Date;
  level: 'error' | 'warn' | 'info';
  type: CDNErrorType;
  message: string;
  details?: any;
  requestId?: string;
  userId?: string;
  trackId?: string;
  stack?: string;
}

/**
 * CDN 错误类
 */
export class CDNServiceError extends Error implements CDNError {
  public readonly type: CDNErrorType;
  public readonly code: string;
  public readonly details?: any;
  public readonly timestamp: Date;
  public readonly requestId?: string;
  public readonly userId?: string;
  public readonly trackId?: string;

  constructor(
    type: CDNErrorType,
    message: string,
    options?: {
      code?: string;
      details?: any;
      requestId?: string;
      userId?: string;
      trackId?: string;
      cause?: Error;
    }
  ) {
    super(message);
    this.name = 'CDNServiceError';
    this.type = type;
    this.code = options?.code || type;
    this.details = options?.details;
    this.timestamp = new Date();
    this.requestId = options?.requestId;
    this.userId = options?.userId;
    this.trackId = options?.trackId;

    if (options?.cause) {
      this.cause = options.cause;
      this.stack = options.cause.stack;
    }
  }

  /**
   * 转换为用户友好的错误消息
   */
  toUserMessage(): string {
    switch (this.type) {
      case CDNErrorType.TOKEN_EXPIRED:
        return '下载链接已过期，请重新获取下载链接';
      case CDNErrorType.TOKEN_INVALID:
        return '下载链接无效，请重新获取下载链接';
      case CDNErrorType.FILE_NOT_FOUND:
        return '音频文件未找到，请联系客服';
      case CDNErrorType.CDN_UNAVAILABLE:
        return 'CDN 服务暂时不可用，请稍后重试';
      case CDNErrorType.RATE_LIMITED:
        return '下载请求过于频繁，请稍后重试';
      case CDNErrorType.CONFIGURATION_ERROR:
        return '服务配置错误，请联系技术支持';
      case CDNErrorType.NETWORK_ERROR:
        return '网络连接错误，请检查网络连接后重试';
      case CDNErrorType.PERMISSION_DENIED:
        return '没有权限访问该文件';
      case CDNErrorType.INVALID_REQUEST:
        return '请求参数无效';
      case CDNErrorType.SERVICE_TIMEOUT:
        return '服务响应超时，请稍后重试';
      default:
        return '下载服务出现错误，请稍后重试';
    }
  }

  /**
   * 转换为 JSON 格式
   */
  toJSON() {
    return {
      name: this.name,
      type: this.type,
      code: this.code,
      message: this.message,
      userMessage: this.toUserMessage(),
      details: this.details,
      timestamp: this.timestamp.toISOString(),
      requestId: this.requestId,
      userId: this.userId,
      trackId: this.trackId
    };
  }
}

/**
 * CDN 错误日志管理器
 */
export class CDNErrorLogger {
  private static instance: CDNErrorLogger | null = null;
  private logs: ErrorLogEntry[] = [];
  private readonly maxLogSize = 1000;

  private constructor() {}

  static getInstance(): CDNErrorLogger {
    if (!CDNErrorLogger.instance) {
      CDNErrorLogger.instance = new CDNErrorLogger();
    }
    return CDNErrorLogger.instance;
  }

  /**
   * 记录错误日志
   */
  logError(error: CDNError | Error, context?: {
    requestId?: string;
    userId?: string;
    trackId?: string;
    details?: any;
  }): void {
    const entry: ErrorLogEntry = {
      timestamp: new Date(),
      level: 'error',
      type: error instanceof CDNServiceError ? error.type : CDNErrorType.NETWORK_ERROR,
      message: error.message,
      details: error instanceof CDNServiceError ? error.details : context?.details,
      requestId: error instanceof CDNServiceError ? error.requestId : context?.requestId,
      userId: error instanceof CDNServiceError ? error.userId : context?.userId,
      trackId: error instanceof CDNServiceError ? error.trackId : context?.trackId,
      stack: error.stack
    };

    this.addLogEntry(entry);
    this.consoleLog(entry);
  }

  /**
   * 记录警告日志
   */
  logWarning(message: string, context?: {
    type?: CDNErrorType;
    requestId?: string;
    userId?: string;
    trackId?: string;
    details?: any;
  }): void {
    const entry: ErrorLogEntry = {
      timestamp: new Date(),
      level: 'warn',
      type: context?.type || CDNErrorType.CDN_UNAVAILABLE,
      message,
      details: context?.details,
      requestId: context?.requestId,
      userId: context?.userId,
      trackId: context?.trackId
    };

    this.addLogEntry(entry);
    this.consoleLog(entry);
  }

  /**
   * 记录信息日志
   */
  logInfo(message: string, context?: {
    type?: CDNErrorType;
    requestId?: string;
    userId?: string;
    trackId?: string;
    details?: any;
  }): void {
    const entry: ErrorLogEntry = {
      timestamp: new Date(),
      level: 'info',
      type: context?.type || CDNErrorType.CDN_UNAVAILABLE,
      message,
      details: context?.details,
      requestId: context?.requestId,
      userId: context?.userId,
      trackId: context?.trackId
    };

    this.addLogEntry(entry);
    this.consoleLog(entry);
  }

  /**
   * 获取错误日志
   */
  getLogs(filter?: {
    level?: 'error' | 'warn' | 'info';
    type?: CDNErrorType;
    since?: Date;
    limit?: number;
  }): ErrorLogEntry[] {
    let filteredLogs = [...this.logs];

    if (filter?.level) {
      filteredLogs = filteredLogs.filter(log => log.level === filter.level);
    }

    if (filter?.type) {
      filteredLogs = filteredLogs.filter(log => log.type === filter.type);
    }

    if (filter?.since) {
      filteredLogs = filteredLogs.filter(log => log.timestamp >= filter.since!);
    }

    if (filter?.limit) {
      filteredLogs = filteredLogs.slice(-filter.limit);
    }

    return filteredLogs.reverse(); // 最新的在前
  }

  /**
   * 获取错误统计
   */
  getErrorStats(since?: Date): {
    total: number;
    byType: Record<CDNErrorType, number>;
    byLevel: Record<string, number>;
    recentErrors: ErrorLogEntry[];
  } {
    let logs = this.logs;
    
    if (since) {
      logs = logs.filter(log => log.timestamp >= since);
    }

    const byType: Record<CDNErrorType, number> = {} as any;
    const byLevel: Record<string, number> = {};

    logs.forEach(log => {
      byType[log.type] = (byType[log.type] || 0) + 1;
      byLevel[log.level] = (byLevel[log.level] || 0) + 1;
    });

    return {
      total: logs.length,
      byType,
      byLevel,
      recentErrors: logs.slice(-10).reverse()
    };
  }

  /**
   * 清理旧日志
   */
  cleanup(olderThan: Date): void {
    const initialCount = this.logs.length;
    this.logs = this.logs.filter(log => log.timestamp > olderThan);
    const removedCount = initialCount - this.logs.length;
    
    if (removedCount > 0) {
      console.log(`[CDN Error Logger] 清理了 ${removedCount} 条旧日志`);
    }
  }

  private addLogEntry(entry: ErrorLogEntry): void {
    this.logs.push(entry);
    
    // 保持日志大小限制
    if (this.logs.length > this.maxLogSize) {
      this.logs.shift();
    }
  }

  private consoleLog(entry: ErrorLogEntry): void {
    const prefix = `[CDN ${entry.level.toUpperCase()}]`;
    const context = [
      entry.requestId && `RequestID: ${entry.requestId}`,
      entry.userId && `User: ${entry.userId}`,
      entry.trackId && `Track: ${entry.trackId}`
    ].filter(Boolean).join(', ');
    
    const message = `${prefix} ${entry.message}${context ? ` (${context})` : ''}`;

    switch (entry.level) {
      case 'error':
        console.error(message, entry.details || '');
        if (entry.stack) {
          console.error(entry.stack);
        }
        break;
      case 'warn':
        console.warn(message, entry.details || '');
        break;
      case 'info':
        console.log(message, entry.details || '');
        break;
    }
  }
}

/**
 * 创建 CDN 错误
 */
export function createCDNError(
  type: CDNErrorType,
  message: string,
  options?: {
    code?: string;
    details?: any;
    requestId?: string;
    userId?: string;
    trackId?: string;
    cause?: Error;
  }
): CDNServiceError {
  return new CDNServiceError(type, message, options);
}

/**
 * 获取错误日志管理器实例
 */
export function getCDNErrorLogger(): CDNErrorLogger {
  return CDNErrorLogger.getInstance();
}

/**
 * 记录 CDN 错误的便捷函数
 */
export function logCDNError(error: CDNError | Error, context?: {
  requestId?: string;
  userId?: string;
  trackId?: string;
  details?: any;
}): void {
  const logger = getCDNErrorLogger();
  logger.logError(error, context);
}

/**
 * 记录 CDN 警告的便捷函数
 */
export function logCDNWarning(message: string, context?: {
  type?: CDNErrorType;
  requestId?: string;
  userId?: string;
  trackId?: string;
  details?: any;
}): void {
  const logger = getCDNErrorLogger();
  logger.logWarning(message, context);
}

/**
 * 记录 CDN 信息的便捷函数
 */
export function logCDNInfo(message: string, context?: {
  type?: CDNErrorType;
  requestId?: string;
  userId?: string;
  trackId?: string;
  details?: any;
}): void {
  const logger = getCDNErrorLogger();
  logger.logInfo(message, context);
}

/**
 * 错误处理装饰器
 */
export function withCDNErrorHandling<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  context?: {
    operation: string;
    getUserId?: (...args: T) => string | undefined;
    getTrackId?: (...args: T) => string | undefined;
    getRequestId?: (...args: T) => string | undefined;
  }
) {
  return async (...args: T): Promise<R> => {
    const requestId = context?.getRequestId?.(...args);
    const userId = context?.getUserId?.(...args);
    const trackId = context?.getTrackId?.(...args);

    try {
      return await fn(...args);
    } catch (error) {
      // 如果已经是 CDN 错误，直接重新抛出
      if (error instanceof CDNServiceError) {
        logCDNError(error);
        throw error;
      }

      // 转换为 CDN 错误
      const cdnError = createCDNError(
        CDNErrorType.NETWORK_ERROR,
        `${context?.operation || 'CDN operation'} failed: ${error instanceof Error ? error.message : String(error)}`,
        {
          requestId,
          userId,
          trackId,
          cause: error instanceof Error ? error : undefined,
          details: { operation: context?.operation }
        }
      );

      logCDNError(cdnError);
      throw cdnError;
    }
  };
}