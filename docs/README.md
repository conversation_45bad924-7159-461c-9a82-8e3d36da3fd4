# LoopCraft 项目文档

## 📚 文档导航

> 💡 **完整文档索引**: [查看详细文档索引](DOCUMENTATION_INDEX.md)

### 核心文档
- **[项目总结](PROJECT_SUMMARY.md)** - 完整的项目功能和成就总结
- **[代码文档](CODE_README.md)** - 详细的技术实现文档
- **[产品需求文档](PRD.md)** - 产品规格和需求说明

### 开发文档
- **[手动测试计划](MANUAL_TEST_PLAN.md)** - 完整的测试流程
- **[开发优化文档](development/)** - 开发过程中的优化记录

## 🚀 快速开始

### 项目概述
LoopCraft 是一个基于 AI 的无缝循环音乐生成平台，为内容创作者和音乐制作人提供高质量的背景音乐生成服务。

### 核心功能
- 🎵 **AI 音乐生成**: 多提供商支持的高质量音乐生成
- 🔄 **无缝循环**: 专门创建完美循环的背景音乐
- 💳 **积分系统**: 灵活的使用计费模式
- 📜 **专业证书**: 为生成的音乐提供商业使用证书
- 🌍 **国际化**: 支持多语言（英文/中文）

### 技术栈
- **前端**: Next.js 15 + React 19 + TypeScript + Tailwind CSS
- **后端**: PostgreSQL + Drizzle ORM + NextAuth.js
- **AI**: 多提供商音乐生成集成 (Suno, Mubert)
- **支付**: Stripe 集成
- **存储**: AWS S3

## 🛠️ 开发指南

### 环境设置
```bash
# 克隆项目
git clone https://github.com/opcraft/loopcraft.git

# 安装依赖
pnpm install

# 配置环境变量
cp .env.example .env.development

# 启动开发服务器
pnpm dev
```

### 常用命令
```bash
# 开发
pnpm dev          # 开发服务器
pnpm build        # 生产构建
pnpm start        # 启动生产服务器

# 数据库
pnpm db:generate  # 生成迁移
pnpm db:migrate   # 执行迁移
pnpm db:studio    # 数据库管理界面

# 测试
pnpm test         # 运行测试
pnpm test:watch   # 监视模式测试
pnpm test:coverage # 覆盖率报告

# 清理工具
npx tsx src/lib/cleanup/execute-full-cleanup.ts  # 项目清理
```

## 📁 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── [locale]/          # 国际化路由
│   ├── api/               # API 路由
├── components/            # React 组件
│   ├── ui/               # 基础 UI 组件
│   ├── music/            # 音乐相关组件
│   └── blocks/           # 页面区块
├── services/             # 业务逻辑服务
├── models/               # 数据库模型
├── db/                   # 数据库配置
├── lib/                  # 工具库
│   └── cleanup/          # 清理系统工具
└── types/                # TypeScript 类型定义
```

## 🔧 环境变量

关键配置项：
```bash
DATABASE_URL=              # PostgreSQL 连接
NEXTAUTH_SECRET=           # 认证密钥
NEXTAUTH_URL=              # 应用 URL
STRIPE_SECRET_KEY=         # Stripe 支付
AWS_ACCESS_KEY_ID=         # S3 存储
AWS_SECRET_ACCESS_KEY=     # S3 存储
AWS_S3_BUCKET=             # S3 存储桶
```

## 🚀 部署

### Vercel (推荐)
[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fopcraft%2Fopcraft-ai)

### Cloudflare Pages
```bash
git checkout cloudflare
cp .env.example .env.production
npm run cf:deploy
```

## 📊 项目状态

### 已完成功能 ✅
- ✅ 完整的 AI 音乐生成平台
- ✅ 多提供商集成 (Suno, Mubert)
- ✅ 用户认证和订阅系统
- ✅ 专业证书生成功能
- ✅ 积分系统和支付集成
- ✅ 国际化支持
- ✅ 项目清理和优化工具

### 技术成就 🏆
- **代码质量**: TypeScript + ESLint + 自动化测试
- **性能优化**: 代码分割、图片优化、缓存策略
- **安全性**: 完整的认证授权和数据验证
- **可维护性**: 清晰的架构和完整的文档

## 📞 支持

- **项目主页**: [LoopCraft](https://loopcraft.app)
- **项目文档**: 查看 docs/ 目录下的详细文档
- **许可证**: [LICENSE](../LICENSE)

---

*文档最后更新: ${new Date().toISOString().split('T')[0]}*