# Sitemap Deployment Checklist

This checklist ensures proper deployment and configuration of the optimized sitemap system.

## Pre-Deployment Validation

### Configuration Validation
- [ ] Run sitemap configuration validation: `npm run sitemap:validate`
- [ ] Verify `SITEMAP_CONFIG.baseUrl` matches production domain
- [ ] Confirm all locales are properly configured
- [ ] Check priority and change frequency settings
- [ ] Validate cache timeout settings

### Environment Variables
- [ ] `DATABASE_URL` or `POSTGRES_URL` is configured
- [ ] `NODE_ENV=production` for production deployment
- [ ] Any custom sitemap environment variables are set

### File Verification
- [ ] `public/robots.txt` references correct sitemap URLs
- [ ] `vercel.json` includes sitemap headers and rewrites
- [ ] All sitemap API routes are present in `src/app/api/sitemap/`

## Deployment Steps

### 1. Code Deployment
- [ ] Deploy code to production environment
- [ ] Verify all sitemap routes are accessible:
  - [ ] `/sitemap.xml` (main sitemap)
  - [ ] `/api/sitemap` (API endpoint)
  - [ ] `/api/sitemap/static` (static pages)
  - [ ] `/api/sitemap/posts` (blog posts)
  - [ ] `/api/sitemap/loops` (music loops)
  - [ ] `/api/sitemap/health` (monitoring)

For complete checklist, see the full deployment guide.