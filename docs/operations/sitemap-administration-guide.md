# Sitemap Administration Guide

This guide covers the administration utilities for managing and maintaining the optimized sitemap system.

## Overview

The sitemap administration system provides comprehensive tools for:
- Cache management and warming
- Sitemap regeneration
- System health monitoring
- Performance analytics
- Quality assurance
- Debugging and troubleshooting
- Automated maintenance

## Administration Components

### 1. SitemapAdmin Service

The core administration service (`src/services/sitemap-admin.ts`) provides programmatic access to all administrative functions.

```typescript
import { SitemapAdmin } from "@/services/sitemap-admin";

const admin = new SitemapAdmin(
  sitemapGenerator,
  cacheManager,
  validator,
  qualityAssurance
);
```

### 2. Admin API Endpoints

RESTful API endpoints at `/api/sitemap/admin` for remote administration:

- `GET /api/sitemap/admin?action=status` - System health check
- `GET /api/sitemap/admin?action=statistics` - System statistics
- `GET /api/sitemap/admin?action=debug` - Debug information
- `POST /api/sitemap/admin` - Execute administrative actions
- `PUT /api/sitemap/admin` - Update configuration
- `DELETE /api/sitemap/admin` - Cleanup operations

### 3. Command Line Interface

CLI tool (`scripts/sitemap-admin-cli.js`) for local and remote administration:

```bash
node scripts/sitemap-admin-cli.js <command> [options]
```

## Administrative Operations

### Cache Management

#### Warm Cache
Pre-generates and caches all sitemaps for improved performance:

```bash
# CLI
node scripts/sitemap-admin-cli.js warm-cache --force --types static,posts --locales en,zh

# API
POST /api/sitemap/admin
{
  "action": "warm-cache",
  "options": {
    "forceRegeneration": true,
    "contentTypes": ["static", "posts"],
    "locales": ["en", "zh"]
  }
}

# Programmatic
const result = await admin.warmCache({
  contentTypes: ['static', 'posts'],
  locales: ['en', 'zh'],
  forceRegeneration: true
});
```

For complete documentation, see the full administration guide.