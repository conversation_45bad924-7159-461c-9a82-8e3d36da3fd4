# LoopCraft 产品需求文档（PRD）— 针对欧美用户的英文版 AI 循环背景音乐生成平台

## Table of Contents

- [一、核心价值主张](#)
- [二、市场与竞争分析](#)
- [三、目标用户](#)
- [四、优化后核心功能](#)
  - [1. 循环音乐生成模块 (“Loop Engine”)](#1-loop-engine)
  - [2. Track 详情页](#2-track-)
  - [3. 账户与仪表盘](#3-)
  - [4. 商业化与授权](#4-)
- [五、技术方案](#)
- [六、KPI 与里程碑](#kpi-)
- [七、Roadmap](#roadmap)



## 一、核心价值主张

**LoopCraft.app** — 全球首个专注于**无缝循环 (seamless loop)** 的 AI 背景音乐生成平台，面向短视频、广告、游戏及其他内容创作者，提供**15s/30s/60s**三档固定时长、一键生成、即刻授权、专业级 **WAV/MP3** 下载及**分轨与变奏**。


## 二、市场与竞争分析

1. 市场需求
-  **短视频 & 社交媒体**：用户对**循环 BGM**有极高依赖，用以卡点剪辑、情绪铺陈；
-  **游戏 & 应用**：需循环背景音效、氛围音乐，要求**低延迟、无缝衔接**；
-  **广告 & 企业宣传**：需即买即用的商用授权音乐，快速配合视频节奏。

2. 主要竞品对比

| 功能维度           | Beatoven.ai[1]    | Soundful[2]     | Mubert[3]      | LoopCraft.app（优化版） |
|-------------------|-------------------|-----------------|-----------------|-------------------------|
| 生成时长           | 任意（可自定义） | 任意            | 实时流式         | 固定15/30/60s           |
| 循环质量           | 无保证            | 有淡入淡出处理    | 可编辑 loop 点   | **后端循环校验 + AI 优化** |
| 分轨下载           | 支持 MP3/WAV      | 支持 STEM packs | 支持 API 分轨    | 支持 WAV/MP3 + **分轨**     |
| 变奏生成           | Prompt 调整       | 不支持           | 不支持           | **一键变奏**              |
| 商用授权           | 非独占永续         | 非独占         | 永续             | **一键授权证书 (PDF)**     |
| SEO 优化           | 基本              | 无              | 无               | **结构化数据、关键词 URL**  |
| 价格区间           | $6–$20/月[4][5] | Free–$9.99+/月[6] | Free–$39/月[7]  | **Free & Pro ($19.99/月)**    |


## 三、目标用户

- 英文市场短视频创作者（TikTok、YouTube Shorts）
- 广告/营销视频制作人
- 独立游戏/移动应用开发者
- Podcast 与直播内容创作者


## 四、优化后核心功能



### 1. 循环音乐生成模块 (“Loop Engine”)

- **固定时长选项**：15s、30s、60s（限制 60s 内，利于循环稳定）；
- **Prompt 输入**：风格、情绪、场景描述（英文）；
- **BPM 锁定**：用户可指定 60–200 BPM；
- **AI 循环校验**：生成后自动比对首尾音频特征，剔除不达标版本，确保**首尾无缝**；
- **变奏 & 分轨**：用户可一键生成变奏，或下载分轨（鼓、贝斯、和声等）。


### 2. Track 详情页

- **Loop Test 播放器**：自动连续循环 3 次预览；
- **元数据展示**：BPM、Key、时长、生成参数；
- **下载选项**：
- Free 用户：MP3（带水印，仅个人用途）；
- Pro 用户 ($19.99/月)：WAV/高品质 MP3、Stem packs、Variations；
- **SEO 优化**：URL `/loops/{prompt-keywords}-{bpm}bpm`, Schema.org/MusicRecording 标记；


### 3. 账户与仪表盘

- **素材库**：支持打标签、收藏、文件夹管理；
- **项目协作**：Pro 版可邀请团队成员共享 Loop 库；


### 4. 商业化与授权

- **Freemium**：每月 3 次生成，MP3 下载；
- **Professional ($19.99/月)**：20 次生成，高清下载、分轨、变奏、团队协作；
- **按需包**：$4.99/5 credits，单首付费下载；
- **授权证书**：下载即得 PDF 证书，注明“Seamless Loop” 格式与商用范围。


## 五、技术方案

1. 前端：React + wavesurfer.js 自定义播放器；
2. 后端：
- Loop 校验服务：基于音频信号处理与 ML；
- 分轨服务：集成 Spleeter；
- 第三方 API 适配层：Mubert/Suno 等可切换；
3. DevOps：AWS Lambda + S3 存储，确保**高并发生成**与**低延迟下载**。


## 六、KPI 与里程碑

| 指标               | 目标值                    | 周期        |
|--------------------|---------------------------|------------|
| Activation Rate    | ≥40%（24h 内生成并循环测试） | 首月         |
| Core Feature Usage | 分轨 & 变奏 ≥15%          | Q1 MVP 后   |
| 转化率             | Freemium→Pro ≥10%         | Q2          |
| SEO 自然流量       | /loops 页面 PV ≥50k/月     | Q3          |
| 次月留存           | ≥60%                      | Q4          |


## 七、Roadmap

- **Phase 1 (1–2 个月)**：实现 Loop Engine、Track 页、Free/Pro 模式；
- **Phase 2 (3–5 个月)**：上线分轨、变奏、团队协作；
- **Phase 3 (6–12 个月)**：插件/API 开放、视频编辑器集成、AI Jingles；

---
**LoopCraft.app** 将以“完美循环、即刻授权、专业分轨”为核心，聚焦英文市场，打造行业领先的 AI 无缝循环音乐解决方案。

[1] https://www.beatoven.ai
[2] https://soundful.com
[3] https://mubert.com
[4] https://findmyaitool.io/tool/beatoven-ai/
[5] https://www.beatoven.ai/pricing
[6] https://aihungry.com/tools/soundful/pricing
[7] https://www.spotsaas.com/product/mubert
[8] https://ppl-ai-file-upload.s3.amazonaws.com/web/direct-files/attachments/68238914/759ba683-34fd-40f1-aa04-578c0b74c4d5/LoopCraft-AI-Wan-Mei-Xun-Huan-Yin-Le-Sheng-Cheng-Ping-Tai-Chan-Pin-Xu-Qiu-Wen-Dang-PRD.docx
[9] https://www.soundverse.ai/music-looper-ai
[10] https://www.cbinsights.com/company/musicstarai/alternatives-competitors
[11] https://www.audjust.com/tools/loop-music-ai
[12] https://www.cbinsights.com/company/assisted-music-composition/alternatives-competitors
[13] https://soundraw.io
[14] https://www.audjust.com/tools/ai-music-looper
[15] https://www.beatoven.ai/blog/soundraw-alternatives/
[16] https://wavel.ai/studio/audio-looper
[17] https://www.beatoven.ai/blog/suno-ai-alternatives/
[18] https://www.media.io/ai-music-generator.html
[19] https://arxiv.org/html/2504.04466v1
[20] https://www.topmediai.com/ai-music/suno-ai-alternatives/
[21] https://easymusic.ai
[22] https://www.youtube.com/watch?v=lPh28rKBpd4
[23] https://alternativeto.net/software/ai-music-generatorr/
[24] https://www.canva.com/features/ai-music-generator/
[25] https://www.reddit.com/r/software/comments/1g2khe2/looking_for_a_music_player_with_seamless_looping/
[26] https://www.saasworthy.com/product/beatoven-ai/pricing
[27] https://www.trustradius.com/products/mubert-render/pricing
[28] https://www.spotsaas.com/product/soundful/pricing
[29] https://www.spotsaas.com/product/mubert/pricing
[30] https://www.beatoven.ai/blog/suno-ai-review/
[31] https://singify.fineshare.com/blog/ai-music-apps/soundful-vs-mubert
[32] https://www.cledara.com/marketplace/soundful
[33] https://www.g2.com/products/mubert-inc-mubert/pricing
[34] https://elitecontentmarketer.com/ai/soundful/
[35] https://findmyaitool.io/tool/mubert/
[36] https://alternatives.co/software/beatoven/pricing/
[37] https://soundful.com/pricing/
[38] https://www.g2.com/pt/products/mubert-inc-mubert/pricing
[39] https://aihungry.com/tools/beatoven-ai/pricing
[40] https://www.aiifi.ai/all-ai-tools/soundful
