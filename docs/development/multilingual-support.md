# 中文字符显示解决方案

## 当前问题
jsPDF默认不支持中文字符，导致中文标题显示为乱码。

## 解决方案对比

### 方案1：Canvas渲染 (已实现)
- ✅ 完美支持中文字符
- ❌ 需要浏览器环境，服务端渲染时不可用
- ❌ 增加PDF文件大小

### 方案2：中文转英文映射 (已实现)
- ✅ 兼容性好，适用于所有环境
- ✅ 保持PDF文件小巧
- ❌ 无法显示原始中文内容

### 方案3：添加中文字体支持 (推荐)
```bash
npm install jspdf-font-chinese
```

```typescript
import 'jspdf-font-chinese';

// 在构造函数中添加中文字体
this.pdf.addFont('path/to/chinese-font.ttf', 'chinese', 'normal');
this.pdf.setFont('chinese');
```

### 方案4：使用Puppeteer生成PDF (最佳)
```bash
npm install puppeteer
```

```typescript
import puppeteer from 'puppeteer';

// 生成HTML模板，然后转换为PDF
const html = `
<html>
<head>
  <style>
    body { font-family: 'Microsoft YaHei', 'SimHei', sans-serif; }
    .title { font-size: 24px; font-weight: bold; }
  </style>
</head>
<body>
  <div class="title">${data.trackTitle}</div>
</body>
</html>
`;

const browser = await puppeteer.launch();
const page = await browser.newPage();
await page.setContent(html);
const pdf = await page.pdf({ format: 'A4', landscape: true });
```

## 当前实现状态

目前已实现方案1和方案2的混合方案：
1. 检测中文字符
2. 优先尝试Canvas渲染（客户端）
3. 失败时使用智能的中文转英文映射
4. 最终fallback到通用占位符

这确保了在所有环境下都能正常工作，虽然可能无法显示原始中文内容，但至少不会出现乱码。

## 建议

对于生产环境，推荐：
1. 短期：使用当前的混合方案
2. 长期：考虑迁移到Puppeteer方案以获得完美的中文支持

---

# 多语言证书支持文档

## 概述

LoopCraft证书生成器现在支持11种主要语言的字符处理，确保所有语言的音乐标题都能在证书中正确显示。

## 支持的语言

### 东亚语言
- **中文 (Chinese)** - 汉字 `[\u4e00-\u9fff]`
- **日文 (Japanese)** - 平假名、片假名 `[\u3040-\u309f\u30a0-\u30ff]`
- **韩文 (Korean)** - 韩文字母 `[\uac00-\ud7af]`

### 中东语言
- **阿拉伯文 (Arabic)** - 阿拉伯字母 `[\u0600-\u06ff]`
- **希伯来文 (Hebrew)** - 希伯来字母 `[\u0590-\u05ff]`

### 欧洲语言
- **俄文 (Russian)** - 西里尔字母 `[\u0400-\u04ff]`
- **希腊文 (Greek)** - 希腊字母 `[\u0370-\u03ff]`

### 南亚和东南亚语言
- **印地文 (Hindi)** - 天城文 `[\u0900-\u097f]`
- **泰文 (Thai)** - 泰文字母 `[\u0e00-\u0e7f]`

### 高加索语言
- **亚美尼亚文 (Armenian)** - 亚美尼亚字母 `[\u0530-\u058f]`
- **格鲁吉亚文 (Georgian)** - 格鲁吉亚字母 `[\u10a0-\u10ff]`

## 处理策略

### 1. 混合语言标题
当标题包含拉丁字符和非拉丁字符时：
```
输入: "Epic 史诗音乐 Background"
输出: "Epic Background (Chinese Title)"

输入: "Beautiful さくら Music"
输出: "Beautiful Music (Japanese Title)"

输入: "Classical Русская Music"
输出: "Classical Music (Russian Title)"
```

### 2. 纯非拉丁语言标题
当标题只包含非拉丁字符时：
```
输入: "测试音乐标题"
输出: "Chinese Music Title"

输入: "음악 제목"
输出: "Korean Title"

输入: "عنوان الموسيقى"
输出: "Arabic Music Title"
```

### 3. 多脚本混合标题
当标题包含多种非拉丁脚本时：
```
输入: "Music 音楽 음악"
输出: "Music (Chinese/Korean Title)"

输入: "Музыка 音乐"
输出: "Russian/Chinese Music Title"
```

### 4. 长标题处理
根据原始标题长度提供不同的描述：
```
短标题 (≤10字符): "Chinese Title"
中等标题 (11-20字符): "Chinese Music Title"
长标题 (>20字符): "Chinese Music Title (Long)"
```

## 技术实现

### 脚本检测
```typescript
const scriptRanges = {
  chinese: /[\u4e00-\u9fff]/g,
  japanese: /[\u3040-\u309f\u30a0-\u30ff]/g,
  korean: /[\uac00-\ud7af]/g,
  arabic: /[\u0600-\u06ff]/g,
  russian: /[\u0400-\u04ff]/g,
  thai: /[\u0e00-\u0e7f]/g,
  hebrew: /[\u0590-\u05ff]/g,
  devanagari: /[\u0900-\u097f]/g,
  greek: /[\u0370-\u03ff]/g,
  armenian: /[\u0530-\u058f]/g,
  georgian: /[\u10a0-\u10ff]/g,
};
```

### 拉丁字符提取
```typescript
const latinParts = text.replace(/[^\u0000-\u024f\u1e00-\u1eff]/g, ' ')
                      .replace(/\s+/g, ' ')
                      .trim();
```

### 显示名称映射
```typescript
const displayNames = {
  chinese: 'Chinese',
  japanese: 'Japanese',
  korean: 'Korean',
  arabic: 'Arabic',
  russian: 'Russian',
  thai: 'Thai',
  hebrew: 'Hebrew',
  devanagari: 'Hindi',
  greek: 'Greek',
  armenian: 'Armenian',
  georgian: 'Georgian'
};
```

## 使用示例

### API调用
```typescript
const certificateData = {
  trackTitle: "美しい音楽のタイトル", // 日文标题
  trackUuid: "uuid-123",
  // ... 其他数据
};

const generator = new CertificateGenerator();
const pdfBuffer = generator.generateCertificate(certificateData);
```

### 预期输出
证书中将显示：`"Japanese Music Title"`

## 兼容性

- ✅ 支持所有现代浏览器
- ✅ 服务端渲染兼容
- ✅ PDF阅读器通用兼容
- ✅ 文件名下载兼容
- ✅ 向后兼容现有功能

## 限制和注意事项

1. **字符显示**：非拉丁字符会被转换为英文描述，不会显示原始字符
2. **脚本检测**：基于Unicode范围检测，可能对某些特殊字符有误判
3. **混合脚本**：最多显示前两种检测到的脚本类型
4. **文件大小**：转换后的文本通常比原始文本短，有助于减小PDF文件大小

## 未来改进

1. **完整字符支持**：考虑添加字体支持以显示原始字符
2. **更多语言**：根据用户需求添加更多语言支持
3. **智能翻译**：集成翻译API提供更准确的标题转换
4. **用户配置**：允许用户选择处理策略（显示原文 vs 转换为英文）